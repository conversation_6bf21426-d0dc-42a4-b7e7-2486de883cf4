package com.teyuntong.goods.service.service.remote.bi;

import com.teyuntong.infra.common.retrofit.circuitbreaker.RetrofitFallbackFactory;
import com.teyuntong.infra.common.retrofit.core.RetrofitClient;
import com.teyuntong.infra.common.retrofit.utils.RetrofitUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/13 16:55
 */
@RetrofitClient(value = BiRetrofitSupplier.class,
        group = "biService",
        fallbackFactory = BiService.NewBiServiceFallbackFactory.class)
public interface BiService {


    @POST("/api/goods-model/list")
    @FormUrlEncoded
    Call<BiResponse<BiGoodModelResult>> goodsModel(@FieldMap Map<String, Object> paramMap);

    @POST("/api/goods-model-price/list")
    @FormUrlEncoded
    Call<BiResponse<BiGoodModelResult>> goodsModelPrice(@FieldMap Map<String, Object> paramMap);


    @Component
    @Slf4j
    class NewBiServiceFallbackFactory implements RetrofitFallbackFactory<BiService> {

        @Override
        public BiService create(Throwable t) {
            log.error("bi service 调用异常", t);
            return new BiService() {
                @Override
                public Call<BiResponse<BiGoodModelResult>> goodsModel(Map<String, Object> paramMap) {
                    return RetrofitUtil.createFallbackCall(new BiResponse<>(new BiGoodModelResult()));
                }

                @Override
                public Call<BiResponse<BiGoodModelResult>> goodsModelPrice(Map<String, Object> paramMap) {
                    return RetrofitUtil.createFallbackCall(new BiResponse<>(new BiGoodModelResult()));
                }
            };
        }
    }
}
