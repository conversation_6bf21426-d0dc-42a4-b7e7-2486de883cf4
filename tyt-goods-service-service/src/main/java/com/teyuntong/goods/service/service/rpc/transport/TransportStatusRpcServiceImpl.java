package com.teyuntong.goods.service.service.rpc.transport;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.aliyun.openservices.ons.api.SendResult;
import com.teyuntong.goods.service.client.transport.dto.SaveGoodsStatusDTO;
import com.teyuntong.goods.service.client.transport.service.TransportStatusRpcService;
import com.teyuntong.goods.service.client.transport.vo.CsBusinessUserBindValuableVO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TransportTecServiceFeeMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.CsBusinessUserBindMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.InterestsGrantRecordsMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportBackendMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TytSpecialCarDispatchFailureMapper;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.enums.TransportModelLevelEnum;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.common.enums.ValueTypeEnum;
import com.teyuntong.goods.service.service.common.mq.ASRMessageBean;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.checker.SeckillGoodsLockChecker;
import com.teyuntong.goods.service.service.rpc.transport.checker.SaveTransportStatusChecker;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.user.service.client.user.enums.NewIdentityEnum;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.VALUABLE_TRANSPORT_FILTER_USER_CONFIG;

/**
 * 运输代调服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class TransportStatusRpcServiceImpl implements TransportStatusRpcService {

    private final TransportMainService transportMainService;

    private final TransportService transportService;

    private final RedisUtil redisUtil;

    private final TransportSyncYmmService transportSyncYmmService;

    private final SeckillGoodsTransportService seckillGoodsTransportService;

    private final SpecialCarDispatchFailureService specialCarDispatchFailureService;

    private final TytConfigRemoteService tytConfigRemoteService;

    private final TransportBackendMapper transportBackendMapper;

    private final OwnerCompanyLogService ownerCompanyLogService;

    private final TytSpecialCarDispatchFailureMapper tytSpecialCarDispatchFailureMapper;

    private final TransportYMMService transportYMMService;

    private final RocketMqProducer rocketMqProducer;

    private final MqMessageFactory mqMessageFactory;

    private final BackoutReasonService backoutReasonService;

    private final MBMqMessageService mbmqMessageService;

    private final TransportDoneService transportDoneService;

    private final OrdersRemoteService ordersRemoteService;

    private final TransportTecServiceFeeMapper transportTecServiceFeeMapper;

    private final InterestsGrantRecordsMapper interestsGrantRecordsMapper;

    private final TransportValuableService transportValuableService;

    private final TransportDispatchService transportDispatchService;

    private final UserRemoteService userServiceRemoteService;

    private final TransportQuotedPriceService transportQuotedPriceService;

    private final TransportMainExtendService transportMainExtendService;

    private final ValuableFollowUpService valuableFollowUpService;

    private final CsBusinessUserBindMapper csBusinessUserBindMapper;

    public static final String RECOVER_EXPOSURE_SWITCH = "tyt:transport:recover_exposure:switch";

    public static final String RECOVER_EXPOSURE_TIME = "tyt:transport:recover_exposure_time";

    private static final int CANCEL_AND_REPUBLISH_VALUE = 13;

    private static final String CANCEL_AND_REPUBLISH_KEY = "撤销并重发";

    private static final int CANCEL_AND_REPUBLISH = 1;

    private static final String VALUABLE_TRANSPORT_DISPATCH_CONFIG = "valuable_transport_dispatch_config";

    private final SeckillGoodsLockChecker seckillGoodsLockChecker;

    private final SaveTransportStatusChecker saveTransportStatusChecker;

    private final ExcellentPriceCountService excellentPriceCountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setGoodsBackOut(SaveGoodsStatusDTO saveGoodsStatusDTO) {
        log.info("货源置撤销 请求参数:{}", JSON.toJSONString(saveGoodsStatusDTO));

        if (Objects.equals(saveGoodsStatusDTO.getRequestSource(), CANCEL_AND_REPUBLISH)) {
            saveGoodsStatusDTO.setBackoutReasonKey(CANCEL_AND_REPUBLISH_KEY);
            saveGoodsStatusDTO.setBackoutReasonValue(CANCEL_AND_REPUBLISH_VALUE);
        }

        TransportDO oldTransport = transportService.getLastBySrcMygId(saveGoodsStatusDTO.getSrcMsgId());

        saveTransportStatusCommonCheck(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId());


        //货源表置撤销
        this.updateStatusBusiness(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId(), TransportStatusEnum.CANCEL.getCode(), null);

        //待支付信息费订单状态为取消
        if (oldTransport.getExcellentGoods() != null && oldTransport.getExcellentGoods() == 2) {
            log.info("专车撤销 取消待支付单 srcMsgId:{}", oldTransport.getSrcMsgId());
            cancelOrder(oldTransport.getTsOrderNo());
        }

        //保存货源撤销原因
        backoutReasonService.addBackoutReason(oldTransport, saveGoodsStatusDTO.getBackoutReasonKey(), saveGoodsStatusDTO.getBackoutReasonValue()
                , saveGoodsStatusDTO.getSpecificReason(), saveGoodsStatusDTO.getRemark(), saveGoodsStatusDTO.getBackoutReasonKeyNew(), saveGoodsStatusDTO.getBackoutReasonValueNew());

        //小程序货源撤销
        TransportBackendDO transportBackendDO = transportBackendMapper.selectBySrcMsgId(saveGoodsStatusDTO.getSrcMsgId());
        log.info("获取小程序货源信息:{}", JSON.toJSONString(transportBackendDO));
        if (transportBackendDO != null) {
            if (transportBackendDO.getStatus() == 2) {
                log.info("小程序货源本身已取消，置为取消");
                transportBackendAndCompanLog(transportBackendDO, saveGoodsStatusDTO.getSrcMsgId(), 2, 31);
            } else {
                log.info("小程序货源本身不是已取消，置为已接单");
                transportBackendAndCompanLog(transportBackendDO, saveGoodsStatusDTO.getSrcMsgId(), 3, 32);
            }
        }

        //YMM货源撤销
        if (4 == oldTransport.getSourceType()) {
            transportYMMService.expireYMMCargoMerge(saveGoodsStatusDTO.getSrcMsgId());
            transportYMMService.pushMbCargoExpireMessage(saveGoodsStatusDTO.getSrcMsgId());
        }

        //回收曝光卡
        recoverInterestsGrant(oldTransport);

        //专车货源撤销，把专车货源是否大厅抢单强制改为否
        if (oldTransport.getExcellentGoods() == 2) {
            log.info("专车货源撤销取消大厅抢单");
            tytSpecialCarDispatchFailureMapper.updateDeclareInPublicToNo(saveGoodsStatusDTO.getSrcMsgId());
        }

        //抽佣货源手动下架触发主动通话记录音转文
        Integer commissionTransport = transportTecServiceFeeMapper.isCommissionTransport(saveGoodsStatusDTO.getSrcMsgId());
        if (commissionTransport != null && commissionTransport == 1) {
            transportbackoutASR(saveGoodsStatusDTO.getSrcMsgId());
        }

        saveTransportStatusHandle(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId(), TransportStatusEnum.CANCEL);

        //好中货，成交后，及撤销后立即进入价值货源池
        try {
            insertGoodMedium2ValuablePoor(saveGoodsStatusDTO);
        } catch (Exception e) {
            log.error("好中货进入价值货源池发生异常, 请求对象:{}, 异常信息为：", JSON.toJSONString(saveGoodsStatusDTO), e);
        }
    }

    /**
     * 好中货，成交后，及撤销后立即进入价值货源
     *
     * <AUTHOR>
     * @param saveGoodsStatusDTO 保存货源状态请求对象
     * @return void
     */
    private void insertGoodMedium2ValuablePoor(SaveGoodsStatusDTO saveGoodsStatusDTO) {
        if(Objects.isNull(saveGoodsStatusDTO)){
            log.info("传入的saveGoodsStatusDTO对象为空");
            return;
        }
        TransportMainDO main = transportMainService.getTransportMainForId(saveGoodsStatusDTO.getSrcMsgId());
        // 不进价值货源的用户id
        String filterUserConfigStr = tytConfigRemoteService.getStringValue(VALUABLE_TRANSPORT_FILTER_USER_CONFIG, "");
        List<Long> filterUserList = Stream.of(filterUserConfigStr.split(","))
                .filter(StringUtils::isNotBlank).map(String::trim).map(Long::valueOf).toList();
        if (filterUserList.contains(saveGoodsStatusDTO.getUserId())) {
            log.info("内部货源不进入价值货源池，srcMsgId:{},userId：{}", main.getSrcMsgId(), main.getUserId());
            return;
        }
        // 好1好2好3中1中2中3货源入池
        TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(saveGoodsStatusDTO.getSrcMsgId());
        if (Objects.nonNull(main) && Objects.nonNull(mainExtendDO)) {
            log.info("价值货源过滤，货源ID：{}，货主ID:{}，好货标签:{}", main.getSrcMsgId(), saveGoodsStatusDTO.getUserId(), mainExtendDO.getGoodTransportLabel());
            if (Objects.nonNull(mainExtendDO.getGoodTransportLabel()) &&
                    mainExtendDO.getGoodTransportLabel() >= TransportModelLevelEnum.GOOD_1.getCode() &&
                    mainExtendDO.getGoodTransportLabel() <= TransportModelLevelEnum.MID_3.getCode()) {
                insertValuableTransport(main, ValueTypeEnum.GOOD_MEDIUM_POOR);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setGoodsBackOutNoInteraction(SaveGoodsStatusDTO saveGoodsStatusDTO) {
        log.info("货源置撤销 请求参数:{}", JSON.toJSONString(saveGoodsStatusDTO));

        if (Objects.equals(saveGoodsStatusDTO.getRequestSource(), CANCEL_AND_REPUBLISH)) {
            saveGoodsStatusDTO.setBackoutReasonKey(CANCEL_AND_REPUBLISH_KEY);
            saveGoodsStatusDTO.setBackoutReasonValue(CANCEL_AND_REPUBLISH_VALUE);
        }

        TransportDO oldTransport = transportService.getLastBySrcMygId(saveGoodsStatusDTO.getSrcMsgId());

        saveTransportStatusCommonCheck(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId());


        //货源表置撤销
        this.updateStatusBusiness(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId(), TransportStatusEnum.CANCEL.getCode(), null);

        //待支付信息费订单状态为取消
//        cancelOrder(oldTransport.getTsOrderNo());

        //保存货源撤销原因
        backoutReasonService.addBackoutReason(oldTransport, saveGoodsStatusDTO.getBackoutReasonKey(), saveGoodsStatusDTO.getBackoutReasonValue()
                , saveGoodsStatusDTO.getSpecificReason(), saveGoodsStatusDTO.getRemark(), saveGoodsStatusDTO.getBackoutReasonKeyNew(), saveGoodsStatusDTO.getBackoutReasonValueNew());

        //撤销时 清空加价次数
        deleteAddMoneyTimesCache(saveGoodsStatusDTO.getUserId(), saveGoodsStatusDTO.getSrcMsgId());

        //好中货，成交后，及撤销后立即进入价值货源池
        try {
            insertGoodMedium2ValuablePoor(saveGoodsStatusDTO);
        } catch (Exception e) {
            log.error("好中货进入价值货源池发生异常, 请求对象:{}, 异常信息为：", JSON.toJSONString(saveGoodsStatusDTO), e);
        }
    }

    @Override
    public void setGoodsDone(SaveGoodsStatusDTO saveGoodsStatusDTO) {
        log.info("货源置成交 请求参数:{}", JSON.toJSONString(saveGoodsStatusDTO));

        saveTransportStatusCommonCheck(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId());

        TransportDO oldTransport = transportService.getLastBySrcMygId(saveGoodsStatusDTO.getSrcMsgId());

        // 6610 参与现金奖活动的货源判断
        judgeIsCashPrizeActivityGoodsWhenDealt(oldTransport);

        //货源表置成交
        this.updateStatusBusiness(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId(), TransportStatusEnum.DEAL.getCode(), "6");

        //待支付信息费订单状态为取消
        if (oldTransport.getExcellentGoods() != null && oldTransport.getExcellentGoods() == 2) {
            log.info("专车成交 取消待支付单 srcMsgId:{}", oldTransport.getSrcMsgId());
            cancelOrder(oldTransport.getTsOrderNo());
        }

        //done表保存成交记录
        transportDoneService.saveTransportDone(saveGoodsStatusDTO.getUserId(), saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getDoneRequest());

        //小程序货源置成交
        TransportBackendDO transportBackendDO = transportBackendMapper.selectBySrcMsgId(saveGoodsStatusDTO.getSrcMsgId());
        log.info("获取小程序货源信息:{}", JSON.toJSONString(transportBackendDO));
        if (transportBackendDO != null && transportBackendDO.getStatus() != 2) {
            log.info("小程序货源本身不是已取消，置为货源已成交");
            transportBackendAndCompanLog(transportBackendDO, saveGoodsStatusDTO.getSrcMsgId(), 6, 60);
        }

        saveTransportStatusHandle(saveGoodsStatusDTO.getSrcMsgId(), saveGoodsStatusDTO.getUserId(), TransportStatusEnum.DEAL);

        //好中货，成交后，及撤销后立即进入价值货源池
        try {
            insertGoodMedium2ValuablePoor(saveGoodsStatusDTO);
        } catch (Exception e) {
            log.error("好中货进入价值货源池发生异常, 请求对象:{}, 异常信息为：", JSON.toJSONString(saveGoodsStatusDTO), e);
        }
    }

    /**
     * 修改货源状态后置处理
     */
    private void saveTransportStatusHandle(Long srcMsgId, Long userId, TransportStatusEnum statusEnum) {
        //撤销时 清空加价次数
        deleteAddMoneyTimesCache(userId, srcMsgId);

        //通知YMM下架货源（TYT货源同步给了YMM）
        transportSyncYmmService.noticeYmmBackoutTransport(srcMsgId);

        // 处理专车货源工单状态
        specialCarDispatchFailureService.processWorkOrderStatus(srcMsgId);

        if (statusEnum == TransportStatusEnum.DEAL) {
            // 成交发放电议次数
            excellentPriceCountService.saveDealGain(userId, srcMsgId);
        }
    }

    /**
     * 清空加价次数
     */
    private void deleteAddMoneyTimesCache(Long userId, Long srcMsgId) {
        String freightAddMoneyNumKey = "freight_add_money_num" + "_" + userId + "_" + srcMsgId;
        log.info("清空加价次数 redisKey:{}", freightAddMoneyNumKey);
        redisUtil.delete(freightAddMoneyNumKey);
    }

    /**
     * 修改小程序货源状态
     */
    public void transportBackendAndCompanLog(TransportBackendDO transportBackendDO, Long srcMsgId, Integer status, Integer orderStatus) {
        transportBackendMapper.updateBackendStatusInfo(status, orderStatus, srcMsgId);
        //记录企业货源状态流转日志
        OwnerCompanyLogDO ownerCompanyLog = new OwnerCompanyLogDO();
        ownerCompanyLog.setOrderNo(transportBackendDO.getOrderNo());
        ownerCompanyLog.setBackendId(transportBackendDO.getId());
        ownerCompanyLog.setCompanyId(transportBackendDO.getReceiverUserId());
        ownerCompanyLog.setEnterpriseId(transportBackendDO.getAppletsUserId());
        ownerCompanyLog.setStatus(status);
        ownerCompanyLog.setOrderStatus(orderStatus);
        ownerCompanyLog.setSrcMsgId(transportBackendDO.getSrcMsgId());
        ownerCompanyLogService.addOwnerCompanyLog(ownerCompanyLog);
        log.info("记录小程序货源企业货源状态流转日志 ownerCompanyLog:{}", JSON.toJSONString(ownerCompanyLog));
    }

    /**
     * 置撤销置成交公共校验（秒抢货源、货源是否存在、货源状态、是否本人货源）
     */
    public void saveTransportStatusCommonCheck(Long srcMsgId, Long userId) {
        log.info("校验秒抢货源");
        seckillGoodsLockChecker.check(srcMsgId);

        log.info("货源是否存在、货源状态、是否本人货源");
        saveTransportStatusChecker.check(srcMsgId, userId);
    }

    /**
     * 触发音转文
     */
    public void transportbackoutASR(Long srcMsgId) {
        Integer callPhoneRecordAsrOnOff = tytConfigRemoteService.getIntValue("call_phone_record_asr_on_off", 0);
        if (callPhoneRecordAsrOnOff == 1) {
            ASRMessageBean asrMessageBean = new ASRMessageBean();
            asrMessageBean.setSrcMsgId(srcMsgId);
            log.info("抽佣货源撤销发送音转文MQ:{}", JSON.toJSONString(asrMessageBean));
            MqMessage mqMessage = mqMessageFactory.create("INFRA_ASR_TOPIC", "ASR_DISTRIBUTE", UuidUtils.generateUuid(), asrMessageBean);
            SendResult sendResult = rocketMqProducer.sendNormal(mqMessage);
            log.info("抽佣货源撤销发送音转文MQ结果:{}", JSON.toJSONString(sendResult));
        }
    }

    /**
     * 取消待支付单
     */
    private void cancelOrder(String tsOrderNo) {
        log.info("取消货源待支付单 tsOrderNo:{}", tsOrderNo);
        ordersRemoteService.cancelNoPayOrder(tsOrderNo);
    }

    /**
     * 修改货源表状态
     */
    public void updateStatusBusiness(Long srcMsgId, Long userId, Integer status, String infoStatus) {
        List<Long> todayIdList = transportService.getTodayIdListBySrcMsgId(srcMsgId, userId, 1);
        log.info("修改货源状态 transport表id {}, srcMsgId:{}, status:{}, infoStatus:{}", JSON.toJSONString(todayIdList), srcMsgId, status, infoStatus);
        // 是否显示
        int display = 1;
        if (CollectionUtils.isNotEmpty(todayIdList)) {
            /* 修改tyt_transport表今天的数据状态 */
            transportService.updateStatusByIds(todayIdList, status, infoStatus, display);
            transportMainService.updateStatusById(srcMsgId, status, infoStatus, display);
        }
    }

    /**
     * 回收曝光卡
     */
    public void recoverInterestsGrant(TransportDO oldTransport) {
        Integer exposureSwitch = tytConfigRemoteService.getIntValue(RECOVER_EXPOSURE_SWITCH, 0);
        if (exposureSwitch != 0 && StringUtils.isNotBlank(oldTransport.getPrice())) {
            Integer minutes = tytConfigRemoteService.getIntValue(RECOVER_EXPOSURE_TIME, 20);
            boolean before = new Date().before(DateUtils.addMinutes(oldTransport.getPubDate(), minutes));
            if (before) {
                InterestsGrantRecordsDO interestsGrantRecordsDO = interestsGrantRecordsMapper.selectBySrcMsgIdAndType(oldTransport.getSrcMsgId(), 1);
                if (interestsGrantRecordsDO != null) {
                    int newCounts = interestsGrantRecordsDO.getGrantCounts() - 1;
                    interestsGrantRecordsDO.setGrantCounts(Math.max(newCounts, 0));
                    interestsGrantRecordsDO.setMtime(new Date());
                    interestsGrantRecordsMapper.updateById(interestsGrantRecordsDO);
                }
            }
        }
    }

    /**
     * 现金奖判断
     */
    private void judgeIsCashPrizeActivityGoodsWhenDealt(TransportDO transport) {
        TransportMainDO transportMain = new TransportMainDO();
        BeanUtils.copyProperties(transport, transportMain);
        boolean cashPrizeActivityTransport = transportMainService.isCashPrizeActivityTransport(transportMain);
        log.info("置成交现金奖判断:{}", cashPrizeActivityTransport);
        if (cashPrizeActivityTransport) {
            JSONObject labeJsonObject = JSON.parseObject(transport.getLabelJson());
            if (labeJsonObject == null) {
                labeJsonObject = new JSONObject();
            }
            //是否是参与现金奖活动的货源 1:是；0:否
            labeJsonObject.put("isCashPrizeActivityGoods", 1);
            log.info("置成交现金奖修改labeJson:{}", JSON.toJSONString(labeJsonObject));
            transportMainService.updateLabelJsonBySrcMsgId(transport.getSrcMsgId(), JSON.toJSONString(labeJsonObject));
        }
    }


    /**
     * 插入价值货源数据
     *
     * @param main
     * @param typeEnum
     */
    public void insertValuableTransport(TransportMainDO main, ValueTypeEnum typeEnum) {
        TransportValuableDO valuable = transportValuableService.selectBySrcMsgId(main.getSrcMsgId());
        if (Objects.nonNull(valuable)) {
            return;
        }
        TransportValuableDO valuableDO = new TransportValuableDO();
        valuableDO.setSrcMsgId(main.getSrcMsgId());
        valuableDO.setValueType(typeEnum.getCode());
        valuableDO.setUserId(main.getUserId());
        valuableDO.setPublishUserId(main.getUserId());
        valuableDO.setPublishUserName(main.getUserShowName());
        valuableDO.setStartCity(main.getStartCity());
        TransportDispatchDO dispatchDO = transportDispatchService.getBySrcMsgId(main.getSrcMsgId());
        if (Objects.nonNull(dispatchDO)) {
            valuableDO.setGiveGoodsPhone(dispatchDO.getGiveGoodsPhone());
            valuableDO.setOwnerFreight(dispatchDO.getOwnerFreight());
            valuableDO.setPublishUserId(dispatchDO.getPublishUserId());
            valuableDO.setPublishUserName(dispatchDO.getPublishUserName());
        }
        // 是否优车2.0首单、是否优车2.0前3单
        List<Long> srcMsgIds = transportMainService.selectUserFirstThreeExcellentGoods(main.getUserId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(srcMsgIds)) {
            if (main.getSrcMsgId().equals(srcMsgIds.get(0))) {
                valuableDO.setFirstOrder(1);
            }
            if (srcMsgIds.contains(main.getSrcMsgId())) {
                valuableDO.setTopThreeOrder(1);
            }
        }
        // 是否直客
        valuableDO.setDirectCustomer(0);
        DwsNewIdentiwoDataRpcVO identityTwoDataDO = userServiceRemoteService.getDwsNewIdentiwoDataByUserId(main.getUserId());
        if (Objects.nonNull(identityTwoDataDO)) {
            NewIdentityEnum identityEnum = NewIdentityEnum.getByCode(identityTwoDataDO.getType());
            if (Objects.nonNull(identityEnum)) {
                if (identityEnum == NewIdentityEnum.SELF_CARGO_OWNER ||
                        identityEnum == NewIdentityEnum.ENTERPRISE_CARGO_OWNER) {
                    valuableDO.setDirectCustomer(1);
                }
            }
        }
        // 是否有司机出价、司机最新一次出价
        TransportQuotedPriceDO quotedPrice = transportQuotedPriceService.getQuotedPriceRecord(main.getSrcMsgId());
        if (Objects.nonNull(quotedPrice)) {
            valuableDO.setDriverGivePrice(1);
            valuableDO.setDriverLatestPrice(BigDecimal.valueOf(quotedPrice.getCarQuotedPrice()));
        }
        boolean matchAutoDispatchConfig = checkMatchDispatchConfig(main, identityTwoDataDO);
        if (matchAutoDispatchConfig) {
            // 市场跟进人
            ValuableFollowUpDO followUpDO = valuableFollowUpService.selectMarketByUserId(main.getUserId());
            boolean marketFollowUpUserSuccess = false;
            if (Objects.nonNull(followUpDO)) {
                Integer marketValuableStatus = valuableFollowUpService.getMarketValuableStatus(followUpDO.getEmployeeId());
                if (marketValuableStatus != null && marketValuableStatus > 0) {
                    //市场跟进人可接单，直接指派给该员工
                    valuableDO.setMarketFollowUpUserId(followUpDO.getEmployeeId());
                    valuableDO.setMarketFollowUpUserName(followUpDO.getEmployeeName());
                    marketFollowUpUserSuccess = true;
                }
            }
            if (!marketFollowUpUserSuccess) {
                //市场跟进人不存在或者市场跟进人不可接单，自动指派跟进人
                CsBusinessUserBindValuableVO chooseCsUser = chooseMarketFollowUp();
                if (chooseCsUser != null && chooseCsUser.getId() != null) {
                    valuableDO.setMarketFollowUpUserId(chooseCsUser.getId());
                    valuableDO.setMarketFollowUpUserName(chooseCsUser.getRealName());
                }
            }
            if (valuableDO.getMarketFollowUpUserId() != null) {
                valuableFollowUpService.insertValuableLog(valuableDO.getMarketFollowUpUserId(), main.getSrcMsgId());
            }
        }
        log.info("待插入的价值货源表对象为：{}", JSON.toJSONString(valuableDO));
        transportValuableService.insertTransportValuableDO(valuableDO);
    }

    /**
     * 判断是否满足价值货源自动指派条件
     *
     * @param main
     * @param identityTwoDataDO
     * @return
     */
    public boolean checkMatchDispatchConfig(TransportMainDO main, DwsNewIdentiwoDataRpcVO identityTwoDataDO) {
        try {
            String configStr = tytConfigRemoteService.getStringValue(VALUABLE_TRANSPORT_DISPATCH_CONFIG);
            JSONObject configJson = JSONObject.parseObject(configStr);

            String excellentGoods = configJson.getString("excellentGoods");
            if (StringUtils.isNotBlank(excellentGoods) && Objects.nonNull(main.getExcellentGoods())) {
                if (!excellentGoods.contains(main.getExcellentGoods().toString())) {
                    return false;
                }
            }

            String publishType = configJson.getString("publishType");
            if (StringUtils.isNotBlank(publishType) && Objects.nonNull(main.getPublishType())) {
                if (!publishType.contains(main.getPublishType().toString())) {
                    return false;
                }
            }

            String identity = configJson.getString("identity");
            if (StringUtils.isNotBlank(identity) && Objects.nonNull(identityTwoDataDO) && Objects.nonNull(identityTwoDataDO.getType())) {
                if (!identity.contains(identityTwoDataDO.getType().toString())) {
                    return false;
                }
            }
            String goodTransportLabel = configJson.getString("goodTransportLabel");
            if (StringUtils.isNotBlank(goodTransportLabel)) {
                TransportMainExtendDO  transportMainExtendDO = transportMainExtendService.getBySrcMsgId(main.getSrcMsgId());
                if (transportMainExtendDO == null || null == transportMainExtendDO.getGoodTransportLabel() || !goodTransportLabel.contains(transportMainExtendDO.getGoodTransportLabel().toString())){
                    return false;
                }
            }
            //
            String goodsStatus = configJson.getString("goodsStatus");
            if (StringUtils.isNotBlank(goodsStatus) && Objects.nonNull(main.getStatus())) {
                if (!goodsStatus.contains(main.getStatus().toString())) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("价值货源入库，判断是否满足价值货源自动指派条件异常", e);
        }
        return false;
    }

    public CsBusinessUserBindValuableVO chooseMarketFollowUp() {
        List<CsBusinessUserBindValuableVO> transportDepartmentAllCsUser = csBusinessUserBindMapper.getTransportDepartmentValuableAllCsUser();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(transportDepartmentAllCsUser)) {
            List<Long> valuableCsUserIdList = transportDepartmentAllCsUser.stream().map(CsBusinessUserBindValuableVO::getId).collect(Collectors.toList());
            List<CsBusinessUserBindValuableVO> csUserOrderNum = csBusinessUserBindMapper.getCsUserOrderNum(valuableCsUserIdList, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()));
            Map<Long, Integer> csUserOrderMap = new HashMap<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(csUserOrderNum)) {
                csUserOrderMap = csUserOrderNum.stream().collect(Collectors.toMap(
                        CsBusinessUserBindValuableVO::getId,
                        CsBusinessUserBindValuableVO::getOrderNum,
                        Integer::min // 如果有相同的 id，保留最小的 orderNum
                ));
            }
            for (CsBusinessUserBindValuableVO csBusinessUserBindValuableVO : transportDepartmentAllCsUser) {
                if (csUserOrderMap.containsKey(csBusinessUserBindValuableVO.getId())) {
                    csBusinessUserBindValuableVO.setOrderNum(csUserOrderMap.get(csBusinessUserBindValuableVO.getId()));
                }
            }
            return transportDepartmentAllCsUser.stream().min(Comparator.comparingInt(CsBusinessUserBindValuableVO::getOrderNum)).orElse(null);
        } else {
            return null;
        }
    }
}
