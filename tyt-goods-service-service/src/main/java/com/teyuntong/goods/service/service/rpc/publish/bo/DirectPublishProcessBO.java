package com.teyuntong.goods.service.service.rpc.publish.bo;

import com.teyuntong.goods.service.client.transport.vo.DirectPublishResultVO;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 直接发布BO
 *
 * <AUTHOR>
 * @since 2025/02/23 11:12
 */
@Getter
@Setter
public class DirectPublishProcessBO extends BasePublishProcessBO {


    /**
     * 直接发布参数
     */
    private DirectPublishBO directPublishBO;


    /**
     * 直接发布返回结果
     */
    private DirectPublishResultVO resultDTO;



}
