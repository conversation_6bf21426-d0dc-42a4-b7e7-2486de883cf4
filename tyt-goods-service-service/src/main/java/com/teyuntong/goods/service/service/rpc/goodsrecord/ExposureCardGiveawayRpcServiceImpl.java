package com.teyuntong.goods.service.service.rpc.goodsrecord;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.client.goodsrecord.service.ExposureCardGiveawayRpcService;
import com.teyuntong.goods.service.client.goodsrecord.service.vo.ExposureCardGiveawayVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.biz.exposure.mybatis.entity.ExposureCardGiveawayRecordDO;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 曝光卡发放接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ExposureCardGiveawayRpcServiceImpl implements ExposureCardGiveawayRpcService {

    private final ExposureCardGiveawayService exposureCardGiveawayService;
    private final TransportMainService transportMainService;
    private final TransportDispatchViewService transportDispatchViewService;

    /**
     * 校验货源是否满足曝光卡发放条件，满足条件则发放
     */
    @Override
    public void checkAndSave(Long srcMsgId) {
        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        if (transportMainDO != null) {
            exposureCardGiveawayService.checkAndSave(transportMainDO);
        } else {
            log.error("货源不存在，srcMsgId={}", srcMsgId);
        }
    }

    /**
     * 查询货源的曝光卡发放记录
     */
    @Override
    public Integer getGiveawayNum(Long srcMsgId) {
        return exposureCardGiveawayService.getGiveawayNum(srcMsgId);
    }

    /**
     * 更新曝光卡发放状态
     */
    @Override
    public void updateStatus() {
        Long startId = 0L;
        while (true) {
            List<ExposureCardGiveawayRecordDO> recordList = exposureCardGiveawayService.getUnknownStatusGiveawayRecord(startId, 10);
            if (CollectionUtils.isEmpty(recordList)) {
                break;
            }
            startId = recordList.get(recordList.size() - 1).getId();

            List<Long> srcMsgIds = recordList.stream().map(ExposureCardGiveawayRecordDO::getSrcMsgId).toList();
            Map<Long, TransportDispatchViewDO> viewContactMap = transportDispatchViewService.getContactAndViewCount(srcMsgIds)
                    .stream().collect(Collectors.toMap(TransportDispatchViewDO::getSrcMsgId, t -> t));

            for (ExposureCardGiveawayRecordDO record : recordList) {
                List<BigDecimal> inlineCondition = Stream.of(record.getInlineCondition().split(",")).map(BigDecimal::new).toList();
                BigDecimal period = inlineCondition.get(0);
                BigDecimal rate = inlineCondition.get(1);

                // 如果还未到时间，跳过
                if (DateUtil.offsetMinute(record.getReleaseTime(), period.intValue()).after(new Date())) {
                    continue;
                }

                TransportDispatchViewDO viewContact = viewContactMap.get(record.getSrcMsgId());
                int status;
                if (viewContact == null) {
                    status = 1;
                } else if (viewContact.getViewCount() == null) {
                    status = 2;
                } else {
                    BigDecimal divide = new BigDecimal(viewContact.getContactCount())
                            .divide(new BigDecimal(viewContact.getViewCount()), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100))
                            .setScale(2, RoundingMode.HALF_UP);
                    status = divide.compareTo(rate) < 0 ? 1 : 2;
                }

                exposureCardGiveawayService.updateStatus(record, status);
            }
        }
    }

    @Override
    public ExposureCardGiveawayVO getGiveaway(Long srcMsgId, Integer status) {
        List<ExposureCardGiveawayRecordDO> recordList = exposureCardGiveawayService.getGiveawayRecord(srcMsgId, 1);
        if (CollectionUtils.isNotEmpty(recordList)) {
            return BeanUtil.copyProperties(recordList.get(0), ExposureCardGiveawayVO.class);
        }
        return null;
    }
}
