package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 货源详情类型
 * 1-找货列表/收藏列表的详情 2-我的货源列表的详情（货主） 3-已接单列表的详情（车主）
 *
 * <AUTHOR>
 * @since 2025-04-09 16:57
 */
@Getter
public enum DetailTypeEnum {
    LIST(1, "找货列表/收藏列表的详情"),
    CARGO_OWNER(2, "我的货源列表的详情（货主）"),
    CAR_OWNER(3, "已接单列表的详情（车主）"),
    ;

    private Integer code;
    private String name;
    DetailTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
