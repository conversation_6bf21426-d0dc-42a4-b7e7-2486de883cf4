package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 发布参数校验
 *
 * <AUTHOR>
 * @since 2025/02/21 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublishParamsChecker {

    /**
     * 直接发布校验
     */
    public void checkDirectPublish(DirectPublishProcessBO processBO) {
        TransportMainDO oldMain = processBO.getOldMain();
        String newPrice = processBO.getDirectPublishBO().getPrice();

        // 直接发布校验装卸货时间
        if (PublishOptEnum.DIRECT.equals(processBO.getOptEnum())) {
            if (oldMain.getLoadingTime() != null && new Date().compareTo(oldMain.getLoadingTime()) > 0) {
                throw new BusinessException(GoodsErrorCode.LOADING_TIME_EXPIRE);
            }
            if (oldMain.getUnloadTime() != null && new Date().compareTo(oldMain.getUnloadTime()) > 0) {
                throw new BusinessException(GoodsErrorCode.LOADING_TIME_EXPIRE);
            }
        }

        // 一口价货源信息费与运费必填
        if (PublishOptEnum.TRANSFER_FIXED.equals(processBO.getOptEnum())) {
            if (oldMain.getInfoFee() == null ||
                    TransportUtil.nonPrice(oldMain.getPrice())
                            && TransportUtil.nonPrice(newPrice)) {
                throw BusinessException.createException(GoodsErrorCode.TRANSFER_FIXED_PARAM_ERROR.getCode(), "一口价货源信息费与运费必填!");
            }
        }

        // 订金在不可退的情况下，订金不能大于等于运费
        if (Objects.equals(oldMain.getRefundFlag(), 0)
                && oldMain.getInfoFee() != null && TransportUtil.hasPrice(newPrice)) {
            if (oldMain.getInfoFee().compareTo(new BigDecimal(newPrice)) >= 0) {
                throw new BusinessException(GoodsErrorCode.INFO_FEE_TOO_HIGH);
            }
        }

        // 已成交货源，不允许加价、填价、转一口价/电议、曝光
        if (Objects.equals(oldMain.getStatus(), TransportStatusEnum.DEAL.getCode())) {
            if (PublishOptEnum.isLiveOpt(processBO.getOptEnum())) {
                log.error("已成交货源不允许操作，货源【{}】，操作：【{}】", processBO.getDirectPublishBO().getSrcMsgId(), processBO.getOptEnum());
                throw new BusinessException(GoodsErrorCode.ERROR_NO_IN_PUBLISH);
            }
        }
    }
}
