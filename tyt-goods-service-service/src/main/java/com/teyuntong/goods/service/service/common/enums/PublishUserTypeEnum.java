package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 发货用户类型枚举
 * <AUTHOR>
 * @since 2024-07-01 11:22
 */
@Getter
public enum PublishUserTypeEnum {

    DISPATCH(1, "代调发货"),
    NORMAL_USER(2, "普通用户发货")

    ;
    private Integer code;
    private String name;
    PublishUserTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
