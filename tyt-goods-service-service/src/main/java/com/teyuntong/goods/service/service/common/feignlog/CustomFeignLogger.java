package com.teyuntong.goods.service.service.common.feignlog;

/**
 * <AUTHOR>
 * @since 2025/1/6 11:31
 */

import feign.Logger;
import feign.Request;
import feign.Response;
import feign.Util;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

import static feign.Util.decodeOrDefault;
import static feign.form.util.CharsetUtil.UTF_8;

/**
 * feign 日志打印工具类
 *
 * <AUTHOR>
 * @date 2025/1/6 9:45
 */
@Slf4j
public class CustomFeignLogger extends Logger {
    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        byte[] arrBody = request.body();
        String body = arrBody == null ? "" : new String(arrBody);
        log.info("[feign request started],url:{},requestBody:{}", request.url(), body);
    }

    @Override
    protected Response logAndRebufferResponse(String configKey,
                                              Level logLevel,
                                              Response response,
                                              long elapsedTime) {
        int status = response.status();

        String content = "";
        if (response.body() != null && !(status == 204 || status == 205)) {
            byte[] bodyData;
            try {
                bodyData = Util.toByteArray(response.body().asInputStream());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            if (bodyData.length > 0) {
                content = decodeOrDefault(bodyData, UTF_8, "Binary data");
                if (content.length() > 1000) {
                    content = content.substring(0,1000);
                }
            }
            response = response.toBuilder().body(bodyData).build();
        }

        log.info("[feign request ended],costTime:{}ms,status:{},url:{},requestBody:{},responseBody:{}",
                elapsedTime,
                status,
                response.request().url(),
                response.request().body() == null ? "" : new String(response.request().body()),
                content);

        return response;
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        //log.info(String.format(methodTag(configKey) + format, args));
    }

}
