package com.teyuntong.goods.service.service.rpc.goodsrecord;

import com.teyuntong.goods.service.client.goodsrecord.service.GoodsRecordRpcService;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.CustomFirstOrderRecordDO;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper.CustomFirstOrderRecordMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDispatchDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportDispatchService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 货源记录接口
 *
 * <AUTHOR>
 * @since 2024/12/06 17:45
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class GoodsRecordRpcServiceImpl implements GoodsRecordRpcService {

    private final TransportDispatchService dispatchService;
    private final OrdersRemoteService ordersRemoteService;
    private final CustomFirstOrderRecordMapper customFirstOrderRecordMapper;


    /**
     * 首履落库，更新表tyt_custom_first_order_record的首次履约完单时间
     *
     * @param srcMsgId
     */
    @Override
    public void saveFirstFinishOrder(Long srcMsgId) {
        log.info("[代调六期] 首履落库，货源ID：{}", srcMsgId);
        TransportDispatchDO dispatchDO = dispatchService.getBySrcMsgId(srcMsgId);
        if (dispatchDO != null) {
            String giveGoodsPhone = dispatchDO.getGiveGoodsPhone();
            log.info("[代调六期] 首履落库，查询给货手机号：{}", giveGoodsPhone);

            CustomFirstOrderRecordDO customFirstOrderRecordDO = customFirstOrderRecordMapper.getByCustomPhone(giveGoodsPhone);
            if (customFirstOrderRecordDO == null || customFirstOrderRecordDO.getFirstFinishOrderTime() != null) {
                log.info("[代调六期] 首履落库，表中首次履约完单时间已存在，无需更新，手机号：{}", giveGoodsPhone);
                return;
            }

            Date firstFinishOrderTime = new Date();
            // 查询该手机号首次成交的货源，根据货源id查询订单的支付时间
            Long firstDealTransportId = dispatchService.getFirstDealTransportIdByGivenPhone(dispatchDO.getGiveGoodsPhone());
            if (firstDealTransportId != null) {
                log.info("[代调六期] 首履落库，更新货源表状态为已首履，货源ID：{}", firstDealTransportId);
                List<TransportOrdersVO> orders = ordersRemoteService.getByTsId(firstDealTransportId);
                if (CollectionUtils.isNotEmpty(orders)) {
                    TransportOrdersVO order = orders.stream().filter(o -> Objects.equals(o.getPayStatus(), "2"))
                            .findAny().orElse(null);
                    if (order != null) {
                        firstFinishOrderTime = order.getPayEndTime();
                    }
                }
            }
            customFirstOrderRecordMapper.updateFirstFinishOrderTimeByCustomPhone(giveGoodsPhone, firstFinishOrderTime);
        }


    }
}
