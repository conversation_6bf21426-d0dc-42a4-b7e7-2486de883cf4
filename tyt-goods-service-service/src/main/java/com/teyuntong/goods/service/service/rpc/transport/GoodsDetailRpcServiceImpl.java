package com.teyuntong.goods.service.service.rpc.transport;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.client.transport.enums.BenefitLabelEnum;
import com.teyuntong.goods.search.client.transport.vo.BenefitLabelVO;
import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.client.publish.dto.CalculatePriceDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodCarPriceTransportTabAndBIPriceDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodsDiagnosisDTO;
import com.teyuntong.goods.service.client.transport.dto.SingleDetailDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodsExcessCoveragePromptDTO;
import com.teyuntong.goods.service.client.transport.dto.ShowAddPriceDTO;
import com.teyuntong.goods.service.client.transport.service.GoodsDetailRpcService;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.client.transport.vo.MyTransportListVO;
import com.teyuntong.goods.service.client.transport.vo.SameCityTransportCountVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportViewLogMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.CallFeedbackLogService;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.UserCallPhoneRecordService;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TransportTecServiceFeeDO;
import com.teyuntong.goods.service.service.biz.commission.service.TransportTecServiceFeeService;
import com.teyuntong.goods.service.service.biz.exposure.mybatis.entity.ExposureCardGiveawayRecordDO;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceEnterpriseDO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.order.converter.OrdersConverter;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarDispatchDetailMapper;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarDispatchDetailService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportViewLogService;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsDialConfigService;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportAutoResendRecordDO;
import com.teyuntong.goods.service.service.biz.publish.service.TransportAutoResendService;
import com.teyuntong.goods.service.service.biz.transport.dto.TransportYmmListDTO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportQuotedPriceMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TytTransportSyncYmmMapper;
import com.teyuntong.goods.service.service.biz.transport.service.BackoutReasonService;
import com.teyuntong.goods.service.service.biz.transport.service.GoodCarPriceTransportService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.biz.transport.service.impl.TransportMainServiceImpl;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytSyncgoodsUserBlackDO;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytUserempowerSyncgoodsDO;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytSyncgoodsUserBlackMapper;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytUserempowerSyncgoodsMapper;
import com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.constant.TransportConstant;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.properties.AesProperties;
import com.teyuntong.goods.service.service.common.enums.UserBiIdentityEnum;
import com.teyuntong.goods.service.service.common.properties.PromptProperties;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.common.properties.XxTeaProperties;
import com.teyuntong.goods.service.service.common.utils.*;
import com.teyuntong.goods.service.service.mq.constant.TopicConstant;
import com.teyuntong.goods.service.service.mq.pojo.GoodsDetailMqBean;
import com.teyuntong.goods.service.service.remote.basic.PublicResourceRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytSourceRemoteService;
import com.teyuntong.goods.service.service.remote.goods.search.SimilarityGoodsRemoteService;
import com.teyuntong.goods.service.service.remote.goods.search.TransportSearchRemoteService;
import com.teyuntong.goods.service.service.remote.market.MarketingActivityRemoteService;
import com.teyuntong.goods.service.service.remote.order.FeedBackRemoteService;
import com.teyuntong.goods.service.service.remote.user.*;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.builder.CalcSpecialGoodsPriceService;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.market.activity.client.activity.enums.ActivityTypeEnum;
import com.teyuntong.trade.service.client.feedBack.vo.GetUserFeedbackRatingVO;
import com.teyuntong.trade.service.client.infofee.vo.TransportOrdersListVO;
import com.teyuntong.trade.service.client.orders.dto.FeedBackQueryRpcDTO;
import com.teyuntong.trade.service.client.orders.dto.TransportOrdersRpcDTO;
import com.teyuntong.trade.service.client.orders.enums.OrderPayStatusEnum;
import com.teyuntong.trade.service.client.orders.enums.OrderRobStatusEnum;
import com.teyuntong.trade.service.client.orders.vo.AcceptOrderLimitInfo;
import com.teyuntong.user.service.client.enterprise.vo.ThirdDominantInfoVo;
import com.teyuntong.user.service.client.goods.vo.SmallMealListVO;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.UserPermissionStatusEnum;
import com.teyuntong.user.service.client.permission.enums.UserPermissionTypeEnum;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.PUBLISH_PRICE_ASSISTANT_ABTEST_CODE;
import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.GOODS_DETAIL_EXPOSURE_TOAST_KEY;
import static com.teyuntong.goods.service.service.common.enums.DiagnosisTaskTypeEnum.*;

/**
 * 货源详情页接口
 *
 * <AUTHOR>
 * @since 2024/11/29 17:34
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class GoodsDetailRpcServiceImpl implements GoodsDetailRpcService {

    private final TransportService transportService;
    private final CoverGoodsDialConfigService coverGoodsDialConfigService;
    private final TransportViewLogService transportViewLogService;
    private final InvoiceEnterpriseService invoiceEnterpriseService;
    private final UserCallPhoneRecordService userCallPhoneRecordService;
    private final SpecialCarDispatchDetailService specialCarDispatchDetailService;
    private final TransportDoneService transportDoneService;
    private final TransportQuotedPriceService transportQuotedPriceService;
    private final SeckillGoodsTransportService seckillGoodsTransportService;
    private final TransportBackendService transportBackendService;
    private final TransportMbMergeService transportMbMergeService;
    private final MbCargoSyncLogService mbCargoSyncLogService;
    private final SpecialCarDispatchFailureService specialCarDispatchFailureService;
    private final TransportMainExtendService transportMainExtendService;
    private final TransportCollectService transportCollectService;
    private final TransportAutoResendService transportAutoResendService;
    private final TransportMainService transportMainService;
    private final TransportDispatchViewService dispatchViewService;
    private final TransportAfterOrderDataService transportAfterOrderDataService;
    private final BackoutReasonService backoutReasonService;
    private final TransportMainMapper transportMainMapper;
    private final GoodCarPriceTransportService goodCarPriceTransportService;
    private final TransportQuotedPriceMapper transportQuotedPriceMapper;
    private final TransportTecServiceFeeService transportTecServiceFeeService;
    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final ExposureCardGiveawayService exposureCardGiveawayService;
    private final CallFeedbackLogService callFeedbackLogService;

    private final UserRemoteService userRemoteService;
    private final PublicResourceRemoteService publicResourceRemoteService;
    private final TytSourceRemoteService tytSourceRemoteService;
    private final FeedBackRemoteService feedBackRemoteService;
    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;
    private final MarketingActivityRemoteService marketingActivityRemoteService;
    private final OrdersRemoteService ordersRemoteService;
    private final ComplaintRecordRemoteService complaintRecordRemoteService;
    private final ABTestRemoteService abTestRemoteService;
    private final UserPermissionRemoteService userPermissionRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final SmallMealRemoteService smallMealRemoteService;
    private final SpecialCarDispatchDetailMapper specialCarDispatchDetailMapper;
    private final TytTransportSyncYmmMapper tytTransportSyncYmmMapper;
    private final TransportSearchRemoteService transportSearchRemoteService;
    private final TytSyncgoodsUserBlackMapper tytSyncgoodsUserBlackMapper;
    private final TytUserempowerSyncgoodsMapper tytUserempowerSyncgoodsMapper;
    private final SimilarityGoodsRemoteService similarityGoodsRemoteService;
    private final TransportViewLogMapper transportViewLogMapper;
    private final CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;

    private final PromptProperties promptProperties;
    private final XxTeaProperties xxTeaProperties;
    private final AesProperties aesProperties;
    private final MqMessageFactory mqMessageFactory;
    private final RocketMqProducer rocketMqProducer;
    private final StringRedisTemplate stringRedisTemplate;
    public static final String USER_TYPE_ONE = "1";
    public static final String USER_TYPE_ZERO = "0";
    /**
     * 经过后台自动匹配的标准货源id
     */
    public static final int BACK_MATCH_ITEM_ID = 9999999;
    private final ThPriceService thPriceService;

    /**
     * 货源详情
     *
     * <AUTHOR>
     * @date 2025/2/25 10:52
     * @param dto
     * @return GoodsSingleDetailResultVO
     */
    @Override
    public GoodsSingleDetailResultVO getSingleDetail(SingleDetailDTO dto) {
        TransportMainDO transportMain = transportMainService.getTransportMainForId(dto.getSrcMsgId());
        if (Objects.isNull(transportMain)) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }
        Long userId = dto.getUserId();
        // 组装详情对象
        GoodsSingleDetailResultVO result = getGoodsSingleDetailResultVO(dto, transportMain, userId);
        // 发送后置处理mq,记录访问日志、防爬日志
        sendSingleDetailMq(dto, result);
        return result;
    }

    /**
     * 货源详情mq，记录访问日志，防爬日志
     *
     * @param dto
     * @param result
     */
    private void sendSingleDetailMq(SingleDetailDTO dto, GoodsSingleDetailResultVO result) {
        try {
            SingleGoodsDetailVO detailBean = result.getDetailBean();
            if (Objects.isNull(detailBean)) {
                return;
            }
            Long srcMsgId = detailBean.getSrcMsgId();
            GoodsDetailMqBean mqBean = new GoodsDetailMqBean();
            BeanUtils.copyProperties(dto, mqBean);
            mqBean.setUserId(dto.getUserId());
            mqBean.setGoodsId(detailBean.getId());
            mqBean.setStatus(detailBean.getStatus());
            mqBean.setPublishTime(detailBean.getPublishTime());
            mqBean.setViewTime(new Date());

            MqMessage mqMessage = mqMessageFactory.create(TopicConstant.GOODS_CENTER_TOPIC, TopicConstant.GET_SINGLE_DETAIL, srcMsgId.toString(), mqBean);
            rocketMqProducer.sendNormal(mqMessage);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，发送mq异常，{},{}", dto.getSrcMsgId(), dto.getUserId(), e);
        }
    }


    @NotNull
    private GoodsSingleDetailResultVO getGoodsSingleDetailResultVO(SingleDetailDTO dto, TransportMainDO transportMain, Long userId) {
        GoodsSingleDetailResultVO result = new GoodsSingleDetailResultVO();
        result.setTime(new Date());

        // 捂货信息
        GoodsSingleDetailResultVO.CoverGoodsDialInfo coverGoodsDialInfo = coverGoodsDialConfigService.coverGoodsVersion4(userId, transportMain);
        result.setCoverGoodsDialInfo(coverGoodsDialInfo);
        // 显示超额保障提示文案，非超额保障返回空。已点击货源返回空。
//        result.setExcessCoverageTip(getGoodsDetailExcessCoverageTip(userId, transportMain));
        // 设置提示文案
        result.setTipInfoCollective(getTipInfoCollective(userId, transportMain));
        // 设置保障文案
        result.setExcessCoveragePrompt(getGoodsExcessCoveragePrompt(transportMain.getSrcMsgId()));
        // 用户是否收藏过此货源
        TransportCollectDO transportCollect = transportCollectService.isExit(userId, transportMain.getSrcMsgId());
        if (Objects.nonNull(transportCollect)) {
            result.setCollectId(transportCollect.getId());
        }
        // 组装detailBean信息，获取货物信息,收藏信息,标注信息
        SingleGoodsDetailVO detailBean = getSingleGoodsDetailVO(userId, transportMain, dto);
        result.setDetailBean(detailBean);
        // 货主信息
        TransportUserVO userVO = getTransportUserVO(userId, detailBean.getUserId(), transportMain);
        result.setTransportUserBean(userVO);
        // 是否显示价格弹窗
        boolean isShowPriceBox = getIsShowPriceBox(transportMain, userId, userVO);
        result.setIsPopupPriceBox(isShowPriceBox ? 1 : 0);
        // 判断是否直接返回
        boolean needReturn = getNeedReturn(detailBean);
        if (!needReturn) {
            // 设置agencyMoneyList
            setAgencyMoneyList(result, detailBean, dto.getDetailType(), userId);
            // 是否是参与现金奖活动的货源，有AB测
            setIsCashPrizeActivityGoods(transportMain, userId, result);
        }
        // 查询用户是否拨打过该货源
        result.setCallingStatus(userCallPhoneRecordService.getCallStatusByUserIdAndTsId(dto.getSrcMsgId(), userId, TimeUtil.getStartOfDay(new Date())));
        // 如果是专车货源，或者是普通货源指派专车司机，那么给前端提示跳过权益校验
        setSkipPermission(result, dto.getSrcMsgId(), userId);
        // defaultUserId处理
        setDefaultUserId(result, transportMain.getSrcMsgId());
        // 接单限制信息处理
        setAcceptOrderLimitInfo(result, dto);
        if (ClientSignEnum.isCar(dto.getClientSign())){
            //设置用户权益
            setUserPermissionNum(result,userId);
        }
        return result;
    }



    private void setBenefitLabel(SingleGoodsDetailVO detailBean, Long srcMsgId){
        try {
            BenefitLabelVO labelVo = transportSearchRemoteService.getTransportBenefitLabel(srcMsgId);
            if (null == labelVo){
                return;
            }
            detailBean.setBenefitLabelCode(labelVo.getCode());
            if (StringUtils.isNotBlank(labelVo.getLabel())){
                detailBean.setBenefitLabel(labelVo.getLabel());
            }
        }catch (Exception e){
            log.error("getSingleDetail货源详情, 获取货源利益点标签失败，{}", srcMsgId, e);
        }

    }

    /**
     * 查询用户接单限制信息
     *
     * @param result
     * @param dto
     */
    private void setAcceptOrderLimitInfo(GoodsSingleDetailResultVO result, SingleDetailDTO dto) {
        try {
            AcceptOrderLimitInfo acceptOrderLimitInfo = ordersRemoteService.getAcceptOrderLimitInfo(dto.getUserId(), dto.getSrcMsgId());
            AcceptOrderLimitInfoVO acceptOrderLimitInfoVO = new AcceptOrderLimitInfoVO();
            BeanUtils.copyProperties(acceptOrderLimitInfo, acceptOrderLimitInfoVO);
            result.setAcceptOrderLimitInfo(acceptOrderLimitInfoVO);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情, 查询用户接单限制信息异常，{}", dto.getSrcMsgId(), e);
        }
    }

    /**
     * 处理默认用户ID
     *
     * @param result
     */
    private void setDefaultUserId(GoodsSingleDetailResultVO result, Long srcMsgId) {
        try {
            int defaultUserIdAppOnOff = tytConfigRemoteService.getIntValue(TransportConstant.DEFAULT_USER_ID_APP_ONOFF, 0);
            if (defaultUserIdAppOnOff == 1) {
                String defaultUserId = tytConfigRemoteService.getStringValue(TransportConstant.TRANSPORT_DEFAULT_USER_ID, "4");
                result.getDetailBean().setUserId(Long.parseLong(defaultUserId));
                result.getTransportUserBean().setUserId(Long.parseLong(defaultUserId));
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情,处理默认用户ID异常，{}", srcMsgId, e);
        }
    }

    /**
     * 如果是专车货源，或者是普通货源指派专车司机，那么给前端提示跳过权益校验
     *
     * @param result
     * @param goodsId
     * @param userId
     */
    private void setSkipPermission(GoodsSingleDetailResultVO result, Long goodsId, Long userId) {
        Integer count = specialCarDispatchDetailService.selectCountByUserAndGoodsId(goodsId, userId);
        if (count > 0) {
            result.setSkipPermission(YesOrNoEnum.YES.getId());
        }
    }

    /**
     * 是否是参与现金奖活动的货源，有AB测
     *
     * @param transportMain
     * @param userId
     * @param result
     */
    private void setIsCashPrizeActivityGoods(TransportMainDO transportMain, Long userId, GoodsSingleDetailResultVO result) {
        try {
            Boolean valid = marketingActivityRemoteService.userIsValidByType(ActivityTypeEnum.car_gtv.getId(), userId);
            boolean cashPrizeActivityTransport = false;
            if (Objects.nonNull(valid) && valid) {
                cashPrizeActivityTransport = transportMainService.isCashPrizeActivityTransport(transportMain);
                result.setIsCashPrizeActivityGoods(cashPrizeActivityTransport ? 1 : 0);
            }
            if (!cashPrizeActivityTransport) {
                // 是否显示送订金券标签
                result.setShowDepositCouponsLabel(getShowDepositCouponsLabel(transportMain));
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，是否是参与现金奖活动的货源处理异常.{}", transportMain.getSrcMsgId(), e);
        }
    }

    /**
     * 设置agencyMoneyList
     *
     * @param result
     * @param detailBean
     * @param detailType 1-找货列表/收藏列表的详情 2-我的货源列表的详情（货主） 3-已接单列表的详情（车主）
     * @param userId
     */
    private void setAgencyMoneyList(GoodsSingleDetailResultVO result, SingleGoodsDetailVO detailBean, Integer detailType, Long userId) {
        String transportNo = detailBean.getTsOrderNo();
        // 默认未支付，未下单
        result.setHasMakeOrder("0");
        result.setIsPaySuccess(0);

        // 判断详情按发货方的走还是按车主方的走
        if (!Objects.equals(userId, detailBean.getUserId())) {
            detailType = DetailTypeEnum.CAR_OWNER.getCode();
        } else {
            detailType = DetailTypeEnum.CARGO_OWNER.getCode();
        }
        log.info("getSingleDetail service userId is: " + userId + ", detailType is: " + detailType);
        if (Objects.equals(detailType, DetailTypeEnum.CARGO_OWNER.getCode())) {
            // 货主详情AgencyMoneyList
            setCargoOwnerAgencyMoneyList(result, detailBean, userId, transportNo);
        } else if (Objects.equals(detailType, DetailTypeEnum.CAR_OWNER.getCode())) {
            // 车主详情AgencyMoneyList
            setCarOwnerAgencyMoneyList(result, userId, transportNo);
        }
    }

    /**
     * 车主详情AgencyMoneyList
     *
     * @param result
     * @param userId
     * @param transportNo
     */
    private void setCarOwnerAgencyMoneyList(GoodsSingleDetailResultVO result, Long userId, String transportNo) {
        try {
            // 是否下过订单：0否、1是
            String hasMakeOrder = "0";
            // 默认未支付
            int isPaySuccess = 0;
            List<TransportOrdersListVO> myOrders = getTransportOrders(userId, transportNo, null);
            if (CollectionUtils.isNotEmpty(myOrders)) {
                myOrders = myOrders.stream()
                        .filter(v -> Objects.nonNull(v.getSortId()))
                        .sorted((o1, o2) -> Math.toIntExact(o2.getSortId() - o1.getSortId()))
                        .collect(Collectors.toList());
            }

            List<PayUserOrderVO> agencyList = new ArrayList<PayUserOrderVO>();
            // 判断用户是否可以继续支付:被拒绝后也可以继续支付
            String payStatus = "0";
            // 随便给一数据库没有额初始值
            String robStatus = "-100";
            if (CollectionUtils.isNotEmpty(myOrders)) {
                hasMakeOrder = "1";
                TransportOrdersListVO ordersListVO = myOrders.get(0);
                agencyList.add(OrdersConverter.INSTANCE.ordersListVOToPayUserOrderVO(ordersListVO));
                if (Objects.nonNull(ordersListVO.getPayStatus())) {
                    payStatus = ordersListVO.getPayStatus().toString();
                }
                if (Objects.nonNull(ordersListVO.getRobStatus())) {
                    robStatus = myOrders.get(0).getRobStatus();
                }
                log.info("getSingleDetail service payStatus is: " + payStatus + ", robStatus is: " + robStatus);
                if (Objects.equals(OrderPayStatusEnum.PAY_SUCCESS.getCode(), payStatus) &&
                        !Objects.equals(OrderRobStatusEnum.REFUSED.getCode(), robStatus) &&
                        !Objects.equals(OrderRobStatusEnum.SYSTEM_REFUSED.getCode(), robStatus) &&
                        !Objects.equals(OrderRobStatusEnum.CANCEL.getCode(), robStatus)) {
                    isPaySuccess = 1;
                }
            }
            // 接单状态0待接单 1 接单成功 2 货主拒绝 3 系统拒绝 4 同意装货 5 车主装货完成 6 系统装货完成 7 异常上报 8 货主撤销货源退款
            // 9 系统撤销货源退款 10 车主取销装货 11 接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
            switch (robStatus) {
                case "-100", "0"," 10", "11":
                    break;
                case "1":
                    // 新增显示已支付信息费
                    List<TransportOrdersListVO> transportOrders = getTransportOrders(userId, transportNo, robStatus);
                    List<GoodsDetailOrderVO> goodsDetailOrderVOS = OrdersConverter.INSTANCE.ordersListVOToGoodsDetailOrderVO(transportOrders);
                    result.setAgencyMoneyList(goodsDetailOrderVOS);
                    break;
                case "2", "3", "8", "9", "4", "5", "6", "7":
                    result.setAgencyMoneyList(agencyList);
                    break;
            }
            result.setIsPaySuccess(isPaySuccess);
            result.setHasMakeOrder(hasMakeOrder);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，车主详情AgencyMoneyList异常.{}", result.getDetailBean().getSrcMsgId(), e);
        }
    }

    private void setUserPermissionNum(GoodsSingleDetailResultVO result, Long userId) {
        Integer searchPermissionCount = userPermissionRemoteService.getSearchPermissionCount(userId);
        if (searchPermissionCount != null && searchPermissionCount >= 0){
            result.setSearchPermissionNum(searchPermissionCount);
        }
    }


    private List<TransportOrdersListVO> getTransportOrders(Long userId, String transportNo, String robStatus) {
        TransportOrdersRpcDTO ordersRpcDTO = new TransportOrdersRpcDTO();
        ordersRpcDTO.setTsOrderNo(transportNo);
        if (Objects.nonNull(userId)) {
            ordersRpcDTO.setPayUserId(userId);
        }
        if (StringUtils.isNotBlank(robStatus)) {
            ordersRpcDTO.setRobStatus(robStatus);
        }
        return ordersRemoteService.getByUser(ordersRpcDTO);
    }

    /**
     * 货主详情AgencyMoneyList
     *
     * @param result
     * @param detailBean
     * @param userId
     * @param transportNo
     */
    private void setCargoOwnerAgencyMoneyList(GoodsSingleDetailResultVO result, SingleGoodsDetailVO detailBean, Long userId, String transportNo) {
        try {
            // 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成
            // 5异常上报6线下成交
            switch (detailBean.getInfoStatus()) {
                case "0":
                    break;
                case "1":
                    List<TransportOrdersListVO> transportOrders = getTransportOrders(userId, transportNo, OrderRobStatusEnum.ACCEPTED.getCode());
                    List<GoodsDetailOrderVO> goodsDetailOrderVOS = OrdersConverter.INSTANCE.ordersListVOToGoodsDetailOrderVO(transportOrders);
                    result.setAgencyMoneyList(goodsDetailOrderVOS);
                    break;
                case "2", "5":
                    List<TransportOrdersListVO> loadingOrders = getTransportOrders(null, transportNo, null);
                    List<GoodsDetailOrderLoadingVO> orderLoadingVOS = OrdersConverter.INSTANCE.ordersListVOToGoodsDetailOrderLoadingVO(loadingOrders);
                    result.setAgencyMoneyList(orderLoadingVOS);
                    break;
                case "3", "4":
                    List<TransportOrdersListVO> finishedOrders = getTransportOrders(null, transportNo, null);
                    List<GoodsDetailOrderFinishedVO> orderFinishedVO = OrdersConverter.INSTANCE.ordersListVOToGoodsDetailOrderFinishedVO(finishedOrders);
                    result.setAgencyMoneyList(orderFinishedVO);
                    break;

            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，货主详情AgencyMoneyList异常.{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 判断是否直接返回
     * ①:不需要信息费的直接返回
     * ②:需要信息费但信息费状态为线下成交的也直接返回
     * ③:无效的、撤销的信息只显示货源相关的东西也直接返回
     * ④:过期的信息只显示货源相关的东西也直接返回
     *
     * @param detailBean
     * @return
     */
    private boolean getNeedReturn(SingleGoodsDetailVO detailBean) {
        boolean needReturn = false;
        int compareTime = TimeUtil.formatDateTime(detailBean.getPublishTime()).compareTo(TimeUtil.formatDate(new Date()));
        // ①:不需要信息费的直接返回  ②:需要信息费但信息费状态为线下成交的也直接返回
        // ③:无效的、撤销的信息只显示货源相关的东西也直接返回 ④:过期的信息只显示货源相关的东西也直接返回
        if (detailBean.getIsInfoFee().equals("0") ||
                (detailBean.getIsInfoFee().equals("1") && detailBean.getInfoStatus().equals("6")) ||
                (Objects.equals(detailBean.getStatus(), GoodsStatusEnum.CANCEL.getCode()) || Objects.equals(detailBean.getStatus(), GoodsStatusEnum.INVALID.getCode())) ||
                (compareTime < 0 && Objects.equals(detailBean.getStatus(), GoodsStatusEnum.PUBLISHING.getCode()))) {
            // v6130pc 详情需要展示货源状态
            if ((compareTime < 0 && Objects.equals(detailBean.getStatus(), GoodsStatusEnum.PUBLISHING.getCode()))) {
                detailBean.setStatus(0);
            }
            needReturn = true;
        }
        return needReturn;
    }

    /**
     * 是否显示价格弹框： 当前用户为货主 & 货源有进线（联系）且货源为无价货源 & 每个货源每天最多自动弹一次
     *
     * @param transportMain
     * @param userId
     * @param userVO
     * @return
     */
    private boolean getIsShowPriceBox(TransportMainDO transportMain, Long userId, TransportUserVO userVO) {
        boolean isShowPriceBox = false;
        try {
            if (transportMain.getUserId().equals(userId) && transportMain.getStatus().equals(1)
                    && transportMain.getCtime().after(DateUtil.beginOfDay(new Date()))
                    && (StringUtils.isBlank(transportMain.getPrice()) || new BigDecimal(transportMain.getPrice()).compareTo(new BigDecimal(0)) == 0)
                    && !"0".equals(userVO.getContactCount())) {
                String obj = stringRedisTemplate.opsForValue().get(RedisKeyConstant.DETAIL_POPUP_PRICE + transportMain.getSrcMsgId());
                if (obj == null) {
                    isShowPriceBox = true;
                    stringRedisTemplate.opsForValue().set(RedisKeyConstant.DETAIL_POPUP_PRICE + transportMain.getSrcMsgId(), "1", 86400, TimeUnit.SECONDS);
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，获取是否显示价格弹窗异常，{}", transportMain.getSrcMsgId(), e);
        }
        return isShowPriceBox;
    }

    /**
     * 获取货源用户信息
     *
     * @param userId
     * @param detailUserId
     * @param transportMain
     * @return
     */
    private TransportUserVO getTransportUserVO(Long userId, Long detailUserId, TransportMainDO transportMain) {
        TransportUserVO userVO = new TransportUserVO();
        try {
            UserRpcVO detailUser = userRemoteService.getUser(detailUserId);
            if (Objects.nonNull(detailUser)) {
                userVO.setUserId(detailUser.getId());
                // 销售审核身份
                setDeliverTypeOneCodeAndName(userVO, detailUser, transportMain.getSrcMsgId());
                // 用户分类和分类名称
                setUserClassAndIdentity(userVO, detailUser, transportMain.getSrcMsgId());
                // 交易次数合作次数
                setCoopNumsAndTradeNums(userId, detailUserId, userVO, transportMain.getSrcMsgId());
                // 货源查看和联系次数
                setViewAndContactCount(userId, transportMain.getSrcMsgId(), userVO);
                // 是否是小套餐会员
                setSmallMealVip(userId, transportMain.getSrcMsgId(), userVO);
                // 信任分和评级
                setScoreAndRankLevel(detailUserId, userVO, transportMain.getSrcMsgId());
                // 用户姓名
                setUserShowName(userVO, detailUser, transportMain);
                // 企业名称
                setEnterpriseName(detailUserId, userVO);
            }
            UserRpcVO user = userRemoteService.getUser(userId);
            if (Objects.nonNull(user)) {
                // 好评率，30天保护期
                setFeedBackPositiveRating(userId, userVO, user, transportMain.getSrcMsgId());
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，获取货源用户信息异常，{}", transportMain.getSrcMsgId(), e);
        }
        return userVO;
    }

    /**
     * 设置是否是小套餐会员
     *
     * @param userId
     * @param srcMsgId
     * @param userVO
     */
    private void setSmallMealVip(Long userId, Long srcMsgId, TransportUserVO userVO) {
        try {
            UserPermissionRpcDTO rpcDTO = new UserPermissionRpcDTO();
            rpcDTO.setUserId(userId);
            rpcDTO.setServicePermissionTypeId(UserPermissionTypeEnum.CAR_NUM_MEAL.getTypeId());
            rpcDTO.setStatus(UserPermissionStatusEnum.EFFECTIVE.getStatus());
            List<UserPermissionRpcVO> permission = userPermissionRemoteService.getUserPermissionByUserId(rpcDTO);
            if (CollectionUtils.isNotEmpty(permission)) {
                userVO.setSmallMealVip(YesOrNoEnum.YES.getId());
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置是否是小套餐会员异常，{}", srcMsgId, e);
        }
    }

    /**
     * 设置好评率
     *
     * @param userId
     * @param userVO
     * @param user
     */
    private void setFeedBackPositiveRating(Long userId, TransportUserVO userVO, UserRpcVO user, Long srcMsgId) {
        try {
            if (Objects.nonNull(user.getCtime()) && DateUtils.addDays(user.getCtime(), 30).before(new Date())) {
                FeedBackQueryRpcDTO queryRpcDTO = new FeedBackQueryRpcDTO();
                queryRpcDTO.setUserId(userId);
                queryRpcDTO.setUserType(2);
                GetUserFeedbackRatingVO userFeedBackRating = feedBackRemoteService.getUserFeedBackRating(queryRpcDTO);
                String feedBackPositiveRating = "";
                if (Objects.nonNull(userFeedBackRating) && userFeedBackRating.getTotal() > 0) {
                    if (userFeedBackRating.getTotal() >= 3) {
                        feedBackPositiveRating = "好评率：" + userFeedBackRating.getRating() + "%";
                    } else if (userFeedBackRating.getTotal() < 3 && userFeedBackRating.getTotal() > 0) {
                        feedBackPositiveRating = "近期有好评";
                    } else {
                        feedBackPositiveRating = "近期无好评";
                    }
                } else {
                   feedBackPositiveRating = "暂无评价";
                }
                userVO.setFeedbackPositiveRating(feedBackPositiveRating);
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置好评率异常，{}", srcMsgId, e);
        }
    }

    /**
     * 设置企业名称
     *
     * @param detailUserId
     * @param userVO
     */
    private void setEnterpriseName(Long detailUserId, TransportUserVO userVO) {
        InvoiceEnterpriseDO invoiceEnterpriseDO = invoiceEnterpriseService.getByCertigierUserId(detailUserId);
        if (Objects.nonNull(invoiceEnterpriseDO)) {
            userVO.setEnterpriseName(invoiceEnterpriseDO.getEnterpriseName());
        }
    }

    /**
     * 设置用户名称和认证状态
     *
     * @param userVO
     * @param detailUser
     */
    private static void setUserShowName(TransportUserVO userVO, UserRpcVO detailUser, TransportMainDO transportMain) {
        try {
            String userShowName = "";
            if (StringUtils.isAnyBlank(detailUser.getIdCard(), detailUser.getTrueName())) {
                userShowName = detailUser.getUserName();
            } else {
                userShowName = detailUser.getTrueName().charAt(0) + IdCardUtil.getCallGender(detailUser.getIdCard());
            }
            userVO.setUserShowName(userShowName);
            userVO.setSex(detailUser.getSex());
            // 用户认证状态,未认证通过的名称"-"标识
            if (Objects.equals(detailUser.getVerifyFlag(), 1)) {
                userVO.setAuthed("1");
            } else {
                userVO.setUserShowName("--");
            }
            // YMM货源不展示货主真实姓名，因为YMM货源的货主真实姓名是调度的
            if (Objects.equals(transportMain.getSourceType(), SourceTypeEnum.YMM.getCode())) {
                userVO.setUserShowName("");
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置用户名称和认证状态异常，{}", transportMain.getSrcMsgId(), e);
        }
    }

    /**
     * 设置信任分和评级
     *
     * @param detailUserId
     * @param userVO
     */
    private void setScoreAndRankLevel(Long detailUserId, TransportUserVO userVO, Long srcMsgId) {
        try {
            ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(detailUserId);
            if (Objects.nonNull(userCreditInfo)) {
                userVO.setTransportScore(userCreditInfo.getTransportScore());
                userVO.setTsRank(userCreditInfo.getTsRank());
                userVO.setCarCreditRankLevel(userCreditInfo.getCarCreditRankLevel());
                userVO.setRankLevel(userCreditInfo.getRankLevel());
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置信任分和评级异常，{}", srcMsgId, e);
        }
    }

    /**
     * 设置查看和联系次数
     *
     * @param userId
     * @param srcMsgId
     * @param userVO
     */
    private void setViewAndContactCount(Long userId, Long srcMsgId, TransportUserVO userVO) {
        try {
            userVO.setViewCount("0");
            userVO.setContactCount("0");
            AuthPermissionRpcDTO permissionRpcDTO = new AuthPermissionRpcDTO();
            permissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源拨打量显示);
            permissionRpcDTO.setUserId(userId);
            AuthPermissionRpcVO authPermissionRpcVO = userPermissionRemoteService.checkAuthPermission(permissionRpcDTO);
            if (Objects.nonNull(authPermissionRpcVO) && authPermissionRpcVO.isUse()) {
                TransportDispatchViewDO contactAndViewCount = dispatchViewService.getContactAndViewCount(srcMsgId);
                if (Objects.nonNull(contactAndViewCount)) {
                    userVO.setViewCount(Objects.isNull(contactAndViewCount.getViewCount()) ? "0" : contactAndViewCount.getViewCount().toString());
                    userVO.setContactCount(Objects.isNull(contactAndViewCount.getContactCount()) ? "0" : contactAndViewCount.getContactCount().toString());
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置查看和联系次数失败，{}", srcMsgId, e);
        }
    }

    /**
     * 交易次数和合作次数
     *
     * @param userId
     * @param detailUserId
     * @param userVO
     */
    private void setCoopNumsAndTradeNums(Long userId, Long detailUserId, TransportUserVO userVO, Long srcMsgId) {
        try {
            List<CreditUserRpcVO> payUserInfos = userRemoteService.getPayUserInfosByCarUserId(Collections.singletonList(detailUserId), userId);
            if (CollectionUtils.isNotEmpty(payUserInfos)) {
                CreditUserRpcVO creditUserRpcVO = payUserInfos.get(0);
                userVO.setCoopNums(creditUserRpcVO.getCoopNums());
                userVO.setTradeNums(creditUserRpcVO.getTradeNums());
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，获取交易次数和合作次数失败，{}", srcMsgId, e);
        }
    }

    /**
     * 设置用户分类和身份信息
     *
     * @param userVO
     * @param detailUser
     * @param srcMsgId
     */
    private void setUserClassAndIdentity(TransportUserVO userVO, UserRpcVO detailUser, Long srcMsgId) {
        try {
            Integer userClass = detailUser.getUserClass();
            if (Objects.isNull(userClass)) {
                userClass = tytConfigRemoteService.getIntValue("defaultIdentityUserClass");
            }
            if (Objects.nonNull(userClass)) {
                userVO.setUserClassCode(userClass);
                TytSourceVO userClassSource = tytSourceRemoteService.getByGroupCodeAndValue("user_class", userClass.toString());
                if (Objects.nonNull(userClassSource)) {
                    userVO.setUserClassName(userClassSource.getName());
                }
            }

            Integer identityType = detailUser.getIdentityType();
            if (Objects.isNull(identityType)) {
                identityType = tytConfigRemoteService.getIntValue("defaultIdentityType");
            }
            if (Objects.nonNull(identityType)) {
                userVO.setIdentityTypeCode(identityType);
                TytSourceVO identitySource = tytSourceRemoteService.getByGroupCodeValueSubValue("user_class", userClass.toString(), identityType.toString());
                if (Objects.nonNull(identitySource)) {
                    userVO.setIdentityTypeName(identitySource.getName());
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置用户分类和身份信息异常，{}", srcMsgId, e);
        }
    }

    /**
     * 设置销售审核身份
     *
     * @param userVO
     * @param detailUser
     * @param srcMsgId
     */
    private void setDeliverTypeOneCodeAndName(TransportUserVO userVO, UserRpcVO detailUser, Long srcMsgId) {
        try {
            userVO.setDeliverTypeOneCode(detailUser.getDeliverTypeOne());
            TytSourceVO userDeliverTypeOne = tytSourceRemoteService.getByGroupCodeAndValue("user_deliver_type_one", detailUser.getDeliverTypeOne());
            if (Objects.nonNull(userDeliverTypeOne)) {
                if (StringUtils.equals(userDeliverTypeOne.getName(), "待定") || StringUtils.equals(userDeliverTypeOne.getName(), "内部员工")
                        || StringUtils.equals(userDeliverTypeOne.getName(), "无效") || StringUtils.equals(userDeliverTypeOne.getName(), "未跟踪")) {
                    userVO.setDeliverTypeOneName("待定");
                } else {
                    userVO.setDeliverTypeOneName(userDeliverTypeOne.getName());
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置销售审核身份异常，{}", srcMsgId, e);
        }
    }

    /**
     * 组装SingleGoodsDetailVO
     *
     * @param userId
     * @param transportMain
     * @param dto
     * @return
     */
    private SingleGoodsDetailVO getSingleGoodsDetailVO(Long userId, TransportMainDO transportMain, SingleDetailDTO dto) {
        SingleGoodsDetailVO detailBean = new SingleGoodsDetailVO();
        BeanUtils.copyProperties(transportMain, detailBean);
        if (StringUtils.isBlank(detailBean.getTyreExposedFlag())) {
            detailBean.setTyreExposedFlag("0");
        }
        // 位置信息
        detailBean.setStartCoordX(String.valueOf(transportMain.getStartCoordX()));
        detailBean.setStartCoordY(String.valueOf(transportMain.getStartCoordY()));
        detailBean.setDestCoordX(String.valueOf(transportMain.getDestCoordX()));
        detailBean.setDestCoordY(String.valueOf(transportMain.getDestCoordY()));
        detailBean.setStartLatitude(String.valueOf(transportMain.getStartLatitude()));
        detailBean.setStartLongitude(String.valueOf(transportMain.getStartLongitude()));
        detailBean.setDestLatitude(String.valueOf(transportMain.getDestLatitude()));
        detailBean.setDestLongitude(String.valueOf(transportMain.getDestLongitude()));
        // 承运人 车辆信息
        setTransportDoneCarInfo(transportMain, detailBean);
        detailBean.setPublishTime(transportMain.getCtime());
        detailBean.setTelephoneOne(transportMain.getTel());
        detailBean.setTelephoneThree(transportMain.getTel4());
        detailBean.setTelephoneTwo(transportMain.getTel3());
        detailBean.setFirstPublishTime(transportMain.getReleaseTime());
        detailBean.setDistance(Objects.nonNull(transportMain.getDistance()) ? transportMain.getDistance().toString() : "");
        detailBean.setAndroidDistance(null);
        detailBean.setIosDistance(null);
        detailBean.setCellPhone(transportMain.getUploadCellphone());
        detailBean.initStartLatitudeStr();
        detailBean.initStartLongitudeStr();
        // 如果该用户前两个月没有发布过电议有价和一口价货源，提示限时体验标识
        setLimitTimeExperience(userId, detailBean);
        // v6210新增信用分/信用分等级字段
        setTotalScoreAndRankLevel(userId, detailBean);
        // 获取货主的会员信息
        setGoodsPermissionMember(userId, detailBean);
        // 获取官方授权账号
        setAuthNameTea(transportMain, detailBean);
        // 增加是否是小程序货源或者后台货源字段
        setIsBackendTransport(transportMain, detailBean);
        // 货源扩展表中的字段填充
        setMainExtendInfo(transportMain, detailBean);
        // 运满满字段填充
        setYmmInfo(transportMain, dto.getDetailType(), detailBean);
        // 专车货源是否大厅抢单字段
        setDeclareInPublic(transportMain, detailBean);
        // 无价货源是否存在有价相似货源
        setSimilarityTransportHavePrice(transportMain, detailBean);
        // 分段付字段
        setStagePayInfo(detailBean, transportMain.getSrcMsgId());
        // 重置标准货源ID
        resetMatchItem(detailBean);
        // 发布时间
        setPublishTimeNew(transportMain, detailBean);
        // 是否自动重发
        setIsAutoResend(transportMain, detailBean);
        if (Objects.equals(userId, transportMain.getUserId())) {
            // 发货人，货主
            // 订单数量
            setOrderExist(transportMain, detailBean);
            // 报价总人数，最近车方报价时间
            setQuotedPriceInfo(detailBean);
        } else {
            // 车主详情
            setCarOwnerDetailInfo(userId, transportMain, detailBean);
            // 隐藏手机号
            hideCellphone(detailBean, userId);
            // 一口价优车2.0二次查看货源详情挽留弹窗
            setGoodCarPriceTransportDetainment(detailBean, userId);
        }
        // 处理字段加密
        encryptFields(detailBean, dto);
        // 处理技术服务费相关字段
        setTecServiceFeeFields(detailBean, userId);
        // 设置开票主体相关信息
        setInvoiceInfo(detailBean, transportMain);
        // 报价组件是否可见
        setCarCanQuotedPriceToPriceTransport(detailBean);
        // 备注拼接固定文案
        setRemark(detailBean, dto);
        //专车货源签约合作商是否是平台
        setSpecialCarCooperativeIsNormal(detailBean, transportMain);
        //专车货源派单人数
        setSpecialCarDispatchCount(detailBean, transportMain);
        //是否同步到YMM
        setSyncYmm(detailBean, transportMain);
        //获取货源利益点标签
        setBenefitLabel(detailBean, transportMain.getSrcMsgId());
        return detailBean;
    }

    private void setSyncYmm(SingleGoodsDetailVO detailBean, TransportMainDO transportMain) {
        TransportSyncYmmDO transportSyncYmmDO = tytTransportSyncYmmMapper.selectTytTransportSyncYmmBySrcId(transportMain.getSrcMsgId());
        if (transportSyncYmmDO != null) {
            detailBean.setSyncYmm(true);
        }
    }

    private void setSpecialCarDispatchCount(SingleGoodsDetailVO detailBean, TransportMainDO transportMain) {
        Integer count = specialCarDispatchDetailMapper.selectCountByGoodsId(transportMain.getSrcMsgId());
        if (count != null && count > 0) {
            detailBean.setSpecialCarDispatchCount(count);
        }
    }

    private void setSpecialCarCooperativeIsNormal(SingleGoodsDetailVO detailBean, TransportMainDO transportMain) {
        detailBean.setSpecialCarCooperativeIsNormal(0);
        if (transportMain.getCargoOwnerId() != null && transportMain.getCargoOwnerId() == 1) {
            detailBean.setSpecialCarCooperativeIsNormal(1);
        }
    }

    /**
     * 设置分段付相关字段
     *
     * @param detailBean
     * @param srcMsgId
     */
    private void setStagePayInfo(SingleGoodsDetailVO detailBean, Long srcMsgId) {
        TransportEnterpriseLogDO logDO = transportEnterpriseLogService.getBySrcMsgId(srcMsgId);
        if (logDO != null) {
            detailBean.setPaymentsType(logDO.getPaymentsType());
            if (Objects.equals(logDO.getPaymentsType(), YesOrNoEnum.YES.getId())) {
                detailBean.setPrepaidPrice(logDO.getPrepaidPrice());
                detailBean.setCollectedPrice(logDO.getCollectedPrice());
                detailBean.setReceiptPrice(logDO.getReceiptPrice());
            }
        }
    }

    /**
     * 货源备注拼接固定文案，但编辑发布(viewSource=0)时不拼接，只设计车货APP。 @since 6600
     *
     * @param detailBean
     * @param dto
     */
    private void setRemark(SingleGoodsDetailVO detailBean, SingleDetailDTO dto) {
        if (dto.getClientSign() != null && (ClientSignEnum.isCar(dto.getClientSign()) || ClientSignEnum.isGoods(dto.getClientSign()))
                && (dto.getClientVersion() != null && Integer.parseInt(dto.getClientVersion()) >= 6600)
                && !Objects.equals(dto.getViewSource(), 0)) {
            detailBean.setRemark((detailBean.getRemark() == null ? "" : detailBean.getRemark()) + "（订金请优先通过平台支付）");
        }
    }

    /**
     * 设置报价组件是否可见
     *
     * @param detailBean
     */
    private void setCarCanQuotedPriceToPriceTransport(SingleGoodsDetailVO detailBean) {
        try {
            detailBean.setCarCanQuotedPriceToPriceTransport(0);
            // 车方查看有价电议、一口价货源是判断是否可以看到报价相关组件，无价货源由app来判断
            TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(detailBean.getSrcMsgId());
            if ((detailBean.getInvoiceTransport() == null || detailBean.getInvoiceTransport() != 1)
                    && (detailBean.getExcellentGoods() == null || detailBean.getExcellentGoods() != 2 ||
                    (mainExtendDO != null && Objects.equals(mainExtendDO.getPriceType(), 2)))) {
                // 非开票货源、除了灵活运价的专车货源才可报价
                detailBean.setCarCanQuotedPriceToPriceTransport(1);
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置报价组件是否可见异常，{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 设置开票主体信息
     *
     * @param detailBean
     */
    private void setInvoiceInfo(SingleGoodsDetailVO detailBean, TransportMainDO transportMainDO) {
        try {
            if (Objects.equals(transportMainDO.getInvoiceTransport(), 1)) {
                // 开票货源额外返回开票主体ID和开票主体服务商code、是否是本平台开票主体
                detailBean.setMainSubjectId(false);
                TransportEnterpriseLogDO transportEnterpriseLog = transportEnterpriseLogService.getBySrcMsgId(transportMainDO.getSrcMsgId());
                if (transportEnterpriseLog == null) {
                    transportEnterpriseLog = new TransportEnterpriseLogDO();
                }
                detailBean.setInvoiceSubjectId(transportEnterpriseLog.getInvoiceSubjectId());
                detailBean.setServiceProviderCode(transportEnterpriseLog.getServiceProviderCode());
                String invoiceSubjectData = tytConfigRemoteService.getStringValue("invoice_subject_data", "1,JCZY");
                String[] split = invoiceSubjectData.split(",");
                if (transportEnterpriseLog.getInvoiceSubjectId() != null && transportEnterpriseLog.getInvoiceSubjectId().compareTo(Long.valueOf(split[0])) == 0) {
                    detailBean.setMainSubjectId(true);
                }

                //XHL开票回显收货人信息
                detailBean.setConsigneeName(transportEnterpriseLog.getConsigneeName());
                detailBean.setConsigneeTel(transportEnterpriseLog.getConsigneeTel());
                detailBean.setConsigneeEnterpriseName(transportEnterpriseLog.getConsigneeEnterpriseName());

                // 开票网货主体标签
                WebResult<ThirdDominantInfoVo> result =
                        thirdEnterpriseRemoteService.getDominantInfoById(transportEnterpriseLog.getInvoiceSubjectId());
                if (Objects.nonNull(result) && result.ok()) {
                    ThirdDominantInfoVo dominantInfoVo = result.getData();
                    if (Objects.nonNull(dominantInfoVo)) {
                        detailBean.setInvoiceSubjectTag(dominantInfoVo.getDominantShortName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置开票主体信息异常，{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 设置技术服务费相关字段
     *
     * @param detailBean
     * @param userId
     */
    private void setTecServiceFeeFields(SingleGoodsDetailVO detailBean, Long userId) {
        try {
            TransportLabelJson labelJson = transportMainService.getTransportLabelJson(detailBean.getSrcMsgId());
            if (Objects.isNull(labelJson) || !labelJson.isCommissionTransport()) {
                // 非抽佣货源技术服务费正常展示
                detailBean.setTecServiceFeeAfterDiscount(detailBean.getTecServiceFee());
                detailBean.setTecServiceFeeAfterDiscountDValue(BigDecimal.ZERO);
            } else {
                TransportTecServiceFeeDO tecServiceFeeDO = transportTecServiceFeeService.getBySrcMsgId(detailBean.getSrcMsgId());
                if (Objects.isNull(tecServiceFeeDO)) {
                    return;
                }

                List<UserPermissionRpcVO> carPermission = getEfficientUserPermission(userId, UserPermissionTypeEnum.CAR_VIP.getTypeId());
                List<UserPermissionRpcVO> smallMemberPermission = getEfficientUserPermission(userId, UserPermissionTypeEnum.CAR_NUM_MEAL.getTypeId());
                if (CollectionUtils.isNotEmpty(carPermission) || CollectionUtils.isNotEmpty(smallMemberPermission)) {
                    // 有车会员（包含小会员），展示车会员技术服务费
                    detailBean.setTecServiceFee(tecServiceFeeDO.getMemberAfterFee());
                    detailBean.setTecServiceFeeAfterDiscount(tecServiceFeeDO.getMemberBeforeFee());
                    detailBean.setTecServiceFeeInterestsWord(tecServiceFeeDO.getMemberInterestsWord());
                    detailBean.setTecServiceFeeInterestsUrl(tecServiceFeeDO.getMemberInterestsUrl());
                    if (tecServiceFeeDO.getMemberAfterFee() != null && tecServiceFeeDO.getMemberBeforeFee() != null) {
                        detailBean.setTecServiceFeeAfterDiscountDValue(tecServiceFeeDO.getMemberBeforeFee().subtract(tecServiceFeeDO.getMemberAfterFee()));
                    }
                } else {
                    // 无车会员，展示车会员技术服务费
                    detailBean.setTecServiceFee(tecServiceFeeDO.getNoMemberAfterFee());
                    detailBean.setTecServiceFeeAfterDiscount(tecServiceFeeDO.getNoMemberBeforeFee());
                    detailBean.setTecServiceFeeInterestsWord(tecServiceFeeDO.getNoMemberInterestsWord());
                    detailBean.setTecServiceFeeInterestsUrl(tecServiceFeeDO.getNoMemberInterestsUrl());
                    if (tecServiceFeeDO.getNoMemberAfterFee() != null && tecServiceFeeDO.getNoMemberBeforeFee() != null) {
                        detailBean.setTecServiceFeeAfterDiscountDValue(tecServiceFeeDO.getNoMemberBeforeFee().subtract(tecServiceFeeDO.getNoMemberAfterFee()));
                    }
                    if (tecServiceFeeDO.getMemberAfterFee() != null && tecServiceFeeDO.getNoMemberAfterFee() != null
                            && tecServiceFeeDO.getNoMemberAfterFee().subtract(tecServiceFeeDO.getMemberAfterFee()).compareTo(BigDecimal.ZERO) > 0) {
                        detailBean.setTecServiceFeeWord("成为会员本单为您省"
                                + tecServiceFeeDO.getNoMemberAfterFee().subtract(tecServiceFeeDO.getMemberAfterFee()).setScale(0).toString()
                                + "元 去购买");
                        // 有小会员
                        SmallMealListVO smallMeal = smallMealRemoteService.getList();
                        if (Objects.nonNull(smallMeal) && CollectionUtils.isNotEmpty(smallMeal.getMealList())
                                && Objects.nonNull(smallMeal.getMealList().get(0).getId())) {
                            detailBean.setTecServiceFeeBuySmallMemberId(smallMeal.getMealList().get(0).getId());
                        } else {
                            detailBean.setTecServiceFeeBuySmallMemberId(-1L);
                        }
                    }
                }

                // 判断是否符合直接免佣条件
                CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = transportMainService.checkIsNeedFreeTecSericeFeeByCarUser(userId, detailBean.getSrcMsgId());
                if (checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
                    log.info("getSingleDetail货源详情, 判断是否符合直接免佣条件，{}, {}", detailBean.getSrcMsgId(), JSONObject.toJSONString(checkIsNeedFreeTecServiceFeeVO));
                    detailBean.setTecServiceFeeWord(checkIsNeedFreeTecServiceFeeVO.getWord());
                    detailBean.setTecServiceFee(BigDecimal.ZERO);
                    //折前折后技术服务费差值直接使用折前技术服务费
                    detailBean.setTecServiceFeeAfterDiscountDValue(detailBean.getTecServiceFeeAfterDiscount());
                }
            }

            //货主端只有代调专车非平台才展示技术服务费，其他的不展示技术服务费
            if (ClientSignEnum.isGoods(LoginHelper.getBaseParam().getClientSign())) {
                if (!(SourceTypeEnum.isDispatch(detailBean.getSourceType())
                        && ExcellentGoodsEnums.SPECIAL.getCode().equals(detailBean.getExcellentGoods())
                        && !Objects.equals(detailBean.getCargoOwnerId(), 1L))) {
                    detailBean.setTecServiceFee(null);
                    detailBean.setTecServiceFeeAfterDiscount(null);
                    detailBean.setTecServiceFeeWord(null);
                    detailBean.setTecServiceFeeAfterDiscountDValue(null);
                    detailBean.setTecServiceFeeInterestsUrl(null);
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情, 设置技术服务费相关字段异常，{}", detailBean.getSrcMsgId(), e);
        }
    }

    private List<UserPermissionRpcVO> getEfficientUserPermission(Long userId, String servicePermissionTypeId) {
        UserPermissionRpcDTO permissionRpcDTO = new UserPermissionRpcDTO();
        permissionRpcDTO.setServicePermissionTypeId(servicePermissionTypeId);
        permissionRpcDTO.setUserId(userId);
        permissionRpcDTO.setStatus(UserPermissionStatusEnum.EFFECTIVE.getStatus());
        List<UserPermissionRpcVO> carPermission = userPermissionRemoteService.getUserPermissionByUserId(permissionRpcDTO);
        return carPermission;
    }

    /**
     * 一口价优车2.0二次查看货源详情挽留弹窗
     *
     * @param detailBean
     */
    private void setGoodCarPriceTransportDetainment(SingleGoodsDetailVO detailBean, Long userId) {
        try {
            TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(detailBean.getSrcMsgId());
            if (Objects.equals(detailBean.getPublishType(), PublishTypeEnum.FIXED.getCode()) // 一口价货源
                    && !Objects.equals(detailBean.getInvoiceTransport(), 1) && // 非开票货源
                    (!Objects.equals(detailBean.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) ||
                            Objects.equals(mainExtendDO.getPriceType(), PriceTypeEnum.FLEXIBLE.getCode()))) { // 非专车货源或者专车灵活运价货源
                String transportViewCountKey = RedisKeyConstant.TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY + userId + "-" + detailBean.getSrcMsgId();
                String transportViewCount = stringRedisTemplate.opsForValue().get(transportViewCountKey);
                if (Objects.nonNull(transportViewCount)) {
                    int count = Integer.parseInt(transportViewCount);
                    if (count == 1) {
                        //弹过窗的不再弹，与等10秒弹窗共用同一个缓存
                        Boolean hasKey = stringRedisTemplate.opsForHash().hasKey(RedisKeyConstant.TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + userId, detailBean.getSrcMsgId().toString());
                        if (!hasKey) {
                            //车方第二次浏览一口价优车2.0货源并且从未对该货源报过价的，货源详情增加挽留弹窗字段
                            if (!transportQuotedPriceService.getCarIsHaveQuotedPriceToTransport(userId, detailBean.getSrcMsgId())) {
                                detailBean.setGoodCarPriceTransportDetainment(1);
                                transportQuotedPriceService.recordCarLeaveTransportSingleDetail(userId, detailBean.getSrcMsgId());
                            }
                        }
                    }
                    stringRedisTemplate.opsForValue().set(transportViewCountKey, String.valueOf(count + 1), 60 * 60 * 24, TimeUnit.SECONDS);
                } else {
                    stringRedisTemplate.opsForValue().set(transportViewCountKey, "1", TimeUtil.getTomorrowZeroSeconds(), TimeUnit.SECONDS);
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情,设置优车2.0挽留弹窗异常，{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 设置报价总人数，最近车方报价时间
     *
     * @param detailBean
     */
    private void setQuotedPriceInfo(SingleGoodsDetailVO detailBean) {
        List<TransportQuotedPriceDO> list = transportQuotedPriceMapper.getQuotedPriceListByTransportMainId(detailBean.getSrcMsgId());
        if (CollectionUtils.isNotEmpty(list)) {
            detailBean.setQuotedPriceCount(list.size());
            detailBean.setLastQuotedPriceTime(list.get(0).getCarQuotedPriceTime());
        }
    }

    /**
     * 隐藏手机号
     * 2018-1-12日，修改货源详情当是找货详情、收藏详情、精准货源、有好货的上传 电话 (除了IOS都屏蔽)
     * 赋值电话号为11个空格，优化投诉时回显11个0
     *
     * @param detailBean
     * @param userId
     */
    private void hideCellphone(SingleGoodsDetailVO detailBean, Long userId) {
        try {
            String tel = "           ";
            AuthPermissionRpcDTO rpcDTO = new AuthPermissionRpcDTO();
            rpcDTO.setServicePermissionEnum(ServicePermissionEnum.查信用);
            rpcDTO.setUserId(userId);
            AuthPermissionRpcVO permission = userPermissionRemoteService.checkAuthPermission(rpcDTO);
            if (Objects.isNull(permission) || !permission.isUse()) {
                detailBean.setCellPhone(tel);
            }
            detailBean.setTelephoneOne(tel);
            detailBean.setTelephoneTwo(tel);
            detailBean.setTelephoneThree(tel);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情, 隐藏手机号异常,{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 处理需要加密的字段
     *
     * @param detailBean
     * @param dto
     */
    private void encryptFields(SingleGoodsDetailVO detailBean, SingleDetailDTO dto) {
        try {
            if (!StringUtils.equals(TransportConstant.PC_WEB, dto.getIsEncypt())) {
                // 是否需要对电话加密 1 需要 2 不需要
                int isNeedEncrypt = tytConfigRemoteService.getIntValue(TransportConstant.IS_NEED_ENCYPT_KEY, 1);
                detailBean.setIsNeedDecrypt(isNeedEncrypt);
                if (isNeedEncrypt == 1) {
                    if (StringUtils.isNotBlank(detailBean.getNickName())) {
                        String nickName = TransportUtil.hidePhoneInStr(detailBean.getNickName());
                        detailBean.setNickName(XXTea.Encrypt(nickName, xxTeaProperties.getKey()));
                        detailBean.setNickNameByAes(AESUtil.enCode(nickName, aesProperties.getKey()));
                    } else {
                        detailBean.setIsNeedDecrypt(2);
                    }
                }
            } else {
                String goodIdEncrypt = XXTea.Encrypt(String.valueOf(detailBean.getId()), xxTeaProperties.getKey());
                detailBean.setGoodsIdEncrypt(goodIdEncrypt);
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情, 处理字段加密异常,{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 无价货源是否存在有价相似货源
     *
     * @param transportMain
     * @param detailBean
     */
    private void setSimilarityTransportHavePrice(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        if (StringUtils.isBlank(detailBean.getPrice()) || "0".equals(detailBean.getPrice())) {
            Boolean havePriceCount = transportMainService.getSimilarityTransportHavePriceCount(transportMain.getSimilarityCode());
            if (havePriceCount) {
                detailBean.setSimilarityTransportHavePrice(true);
            }
        }
    }

    /**
     * 设置是否自动重发
     *
     * @param transportMain
     * @param detailBean
     */
    private void setIsAutoResend(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        TransportAutoResendRecordDO autoResendRecordDO = transportAutoResendService.getRecordByOldSrcMsgId(transportMain.getSrcMsgId());
        detailBean.setIsAutoResend(Objects.nonNull(autoResendRecordDO) ? 1 : 0);
    }

    /**
     * 设置发布时间
     *
     * @param transportMain
     * @param detailBean
     */
    private void setPublishTimeNew(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        TransportDO transport = transportService.getLastBySrcMygId(transportMain.getSrcMsgId());
        detailBean.setPublishTimeNew((null == transport || null == transport.getPubDate())? detailBean.getFirstPublishTime() : transport.getCtime());
    }

    /**
     * 重新设置标准货源ID
     *
     * @param detailBean
     */
    private void resetMatchItem (SingleGoodsDetailVO detailBean){
        Integer matchItemId = detailBean.getMatchItemId();
        if(matchItemId != null && matchItemId == BACK_MATCH_ITEM_ID){
            detailBean.setIsStandard(1);
            detailBean.setMatchItemId(-1);
        }
    }

    /**
     * 设置承运人车辆信息
     *
     * @param transportMain
     * @param detailBean
     */
    private void setTransportDoneCarInfo(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        if (Objects.equals(GoodsStatusEnum.DONE.getCode(), transportMain.getStatus())){
            TransportDoneDO transportDone = transportDoneService.getByTsId(transportMain.getId());
            if (Objects.nonNull(transportDone)) {
                detailBean.setHeadCity(transportDone.getHeadCity());
                detailBean.setHeadNo(transportDone.getHeadNo());
                detailBean.setTailCity(transportDone.getTailCity());
                detailBean.setTailNo(transportDone.getTailNo());
            }
        }
    }

    /**
     * 车主详情信息填充
     *
     * @param userId
     * @param transportMain
     * @param detailBean
     */
    private void setCarOwnerDetailInfo(Long userId, TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        //车详情增加 是否被投诉标识
        try {
            ComplaintRecordRpcVO complaintRecord = complaintRecordRemoteService.getByMsgIdComplainantId(transportMain.getSrcMsgId(), userId);
            detailBean.setComplainedStatus(0);
            if (Objects.nonNull(complaintRecord)) {
                detailBean.setComplainedStatus(1);
            }

            //如果车主ID在 防飞单（隐藏货主信息）AB测试中，则将货源货主昵称修改为特运通老板
            Integer userType = abTestRemoteService.getUserType("not_show_transport_user_data", userId);
            if (Objects.equals(userType, 1)) {
                detailBean.setNickName("特运通老板");
            }

            // 详情页是否显示用户昵称
            nickNameAndAuthNameTeaJudge(userId, detailBean);

            // 详情页是否显示货源内容
            goodsInfoJudge(userId, detailBean);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，车主详情信息填充异常，{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 详情页是否显示货源内容
     *
     * @param userId
     * @param detailBean
     */
    private void goodsInfoJudge(Long userId, SingleGoodsDetailVO detailBean) {
        AuthPermissionRpcDTO rpcDTO = new AuthPermissionRpcDTO();
        rpcDTO.setServicePermissionEnum(ServicePermissionEnum.货物内容显示);
        rpcDTO.setUserId(userId);
        AuthPermissionRpcVO permission = userPermissionRemoteService.checkAuthPermission(rpcDTO);
        if (Objects.isNull(permission) || !permission.isUse()) {
            detailBean.setTaskContent(TransportConstant.REPLACEMENT_STARTS_CONTENT);
            detailBean.setWeight(null);
            detailBean.setCarLength(null);
            detailBean.setCarType(null);
            detailBean.setSpecialRequired(null);
        }
    }

    /**
     * 详情页是否显示用户昵称
     *
     * @param userId
     * @param detailBean
     */
    private void nickNameAndAuthNameTeaJudge(Long userId, SingleGoodsDetailVO detailBean) {
        AuthPermissionRpcDTO rpcDTO = new AuthPermissionRpcDTO();
        rpcDTO.setServicePermissionEnum(ServicePermissionEnum.用户昵称显示);
        rpcDTO.setUserId(userId);
        AuthPermissionRpcVO permission = userPermissionRemoteService.checkAuthPermission(rpcDTO);
        if (Objects.isNull(permission) || !permission.isUse()) {
            detailBean.setNickName(TransportConstant.REPLACEMENT_STARTS);
            detailBean.setAuthNameTea(null);
        }
    }

    /**
     * 查询订单数量
     *
     * @param transportMain
     * @param detailBean
     */
    private void setOrderExist(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        try {
            DateTime dateTime = DateUtil.beginOfDay(new Date());
            if (detailBean.getPublishTime().compareTo(dateTime) < 0 &&
                    (!Objects.equals(detailBean.getStatus(), GoodsStatusEnum.DONE.getCode()) &&
                            !Objects.equals(detailBean.getStatus(), GoodsStatusEnum.CANCEL.getCode()))) {
                detailBean.setStatus(0);
            }

            //我的货源详情查询订单数量
            Long srcMsgId = transportMain.getSrcMsgId();
            TransportOrdersVO transportOrdersVO = ordersRemoteService.checkUsefulOrderExist(srcMsgId);
            detailBean.setOrderExist(Objects.isNull(transportOrdersVO) ? 0 : 1);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，货主详情信息填充异常,{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 是否可大厅抢单字段填充
     *
     * @param transportMain
     * @param detailBean
     */
    private void setDeclareInPublic(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        if (Objects.equals(detailBean.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {
            TytSpecialCarDispatchFailureDO tytSpecialCarDispatchFailure = specialCarDispatchFailureService.selectBySrcMsgId(transportMain.getSrcMsgId());
            if (Objects.nonNull(tytSpecialCarDispatchFailure)) {
                detailBean.setDeclareInPublic(tytSpecialCarDispatchFailure.getDeclareInPublic());
            }
        }
    }

    /**
     * 运满满货源信息填充
     *
     * @param transportMain
     * @param detailType
     * @param detailBean
     */
    private void setYmmInfo(TransportMainDO transportMain, Integer detailType, SingleGoodsDetailVO detailBean) {
        if (Objects.equals(detailType, 2) && SourceTypeEnum.YMM.getCode().equals(transportMain.getSourceType())) {
            /**
             *  YMM 货源编辑发布 查询最新 修改后数据组装
             */
            log.info("getSingleDetail[YMM-编辑发布] 回显数据处理之前，结果：{}", detailBean);
            Long detailId = detailBean.getId();
            TransportYmmListDTO transportYmmListDTO = transportMbMergeService.selectBeforeVersionJson(transportMain.getSrcMsgId());
            if (Objects.nonNull(transportYmmListDTO)) {
                MbCargoSyncLogDO syncLogDO = mbCargoSyncLogService.selectLastVersionJson(transportYmmListDTO.getCargoId());
                if (Objects.nonNull(syncLogDO)) {
                    //判断最新版本号是否大于已发布版本号
                    MbCargoSyncInfoDO tytMbCargoSyncInfo = JSONObject.parseObject(syncLogDO.getInfoJson(), MbCargoSyncInfoDO.class);
                    if (syncLogDO.getCargoVersion() > transportYmmListDTO.getCargoVersion()) {
                        copyProperties(tytMbCargoSyncInfo, detailBean);
                    }
                    detailBean.setId(detailId);
                    detailBean.setGiveGoodsPhone(tytMbCargoSyncInfo.getContactTelephone());
                    detailBean.setCargoId(transportYmmListDTO.getCargoId());
                    detailBean.setCargoVersion(Objects.isNull(syncLogDO.getCargoVersion()) ? "" : syncLogDO.getCargoVersion().toString());
                    log.info("getSingleDetail[YMM-编辑发布] 回显数据处理完成后，结果：{}", detailBean);
                }
            }
        }
    }

    /**
     * 货源扩展表信息填充
     *
     * @param transportMain
     * @param detailBean
     */
    private void setMainExtendInfo(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        try {
            TransportMainExtendDO extendInfo = transportMainExtendService.getBySrcMsgId(transportMain.getSrcMsgId());
            if (Objects.nonNull(extendInfo)) {
                if (Objects.equals(extendInfo.getSeckillGoods(), 1)) {
                    boolean seckillGoodsIsLock = seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(transportMain.getSrcMsgId());
                    detailBean.setHidePriceBox(seckillGoodsIsLock ? 1 : 0);
                    detailBean.setSeckillGoods(extendInfo.getSeckillGoods());
                }
                // 回价助手
                if (Objects.nonNull(extendInfo.getPriceCap())) {
                    if (StringUtils.isBlank(detailBean.getPrice()) ||
                            new BigDecimal(detailBean.getPrice()).compareTo(new BigDecimal(extendInfo.getPriceCap())) < 0) {
                        Integer userType = abTestRemoteService.getUserType(PUBLISH_PRICE_ASSISTANT_ABTEST_CODE, detailBean.getUserId());
                        if (Objects.equals(userType, 1)) {
                            detailBean.setPriceCap(extendInfo.getPriceCap());
                        }
                    }
                }
                detailBean.setUseCarType(extendInfo.getUseCarType());
                if (null != extendInfo.getPerkPrice()){
                    detailBean.setPerkPrice(new BigDecimal(extendInfo.getPerkPrice()));
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置货源拓展表信息失败,{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 是否是小程序货源填充
     *
     * @param transportMain
     * @param detailBean
     */
    private void setIsBackendTransport(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        TransportBackendDO transportBackendDO = transportBackendService.selectBySrcMsgId(transportMain.getSrcMsgId());
        if (Objects.nonNull(transportBackendDO)) {
            detailBean.setIsBackendTransport(1);
        }
    }

    /**
     * 官方授权昵称处理
     *
     * @param transportMain
     * @param detailBean
     */
    private void setAuthNameTea(TransportMainDO transportMain, SingleGoodsDetailVO detailBean) {
        try {
            String authNameTea = this.getAuthNameTea(transportMain.getAuthName());
            if (StringUtils.isNotBlank(authNameTea)) {
                detailBean.setAuthNameTea(authNameTea);
                detailBean.setAuthName(null);
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置官方授权昵称异常,{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 设置信用分和信用等级
     *
     * @param userId
     * @param detailBean
     */
    private void setTotalScoreAndRankLevel(Long userId, SingleGoodsDetailVO detailBean) {
        try {
            ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(userId);
            if (Objects.nonNull(userCreditInfo)) {
                detailBean.setTotalScore(userCreditInfo.getTotalScore() == null ? new BigDecimal(0) : userCreditInfo.getTotalScore());
                detailBean.setRankLevel(userCreditInfo.getRankLevel() == null ? 1 : userCreditInfo.getRankLevel());
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置信用分和信用等级异常，{}", detailBean.getSrcMsgId(), e);
        }
    }

    /**
     * 限时体验按钮处理
     *
     * @param userId
     * @param detailBean
     */
    private void setLimitTimeExperience(Long userId, SingleGoodsDetailVO detailBean) {
        Date publishTime = DateUtils.addMonths(new Date(), -2);
        List<String> priceList = transportMainService.selectOfPublishType(userId, publishTime);
        if (CollectionUtils.isEmpty(priceList)) {
            detailBean.setLimitTimeExperience(1);
        } else {
            priceList = priceList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(priceList)) {
                detailBean.setLimitTimeExperience(0);
            } else {
                detailBean.setLimitTimeExperience(1);
            }
        }
    }

    /**
     * 发货人会员状态处理
     *
     * @param userId
     * @param detailBean
     */
    private void setGoodsPermissionMember(Long userId, SingleGoodsDetailVO detailBean) {
        try {
            UserPermissionRpcDTO permissionRpcDTO = new UserPermissionRpcDTO();
            permissionRpcDTO.setUserId(userId);
            permissionRpcDTO.setServicePermissionTypeId(UserPermissionTypeEnum.GOODS_VIP.getTypeId());
            permissionRpcDTO.setStatus(UserPermissionStatusEnum.EFFECTIVE.getStatus());
            List<UserPermissionRpcVO> userPermissionByUserId = userPermissionRemoteService.getUserPermissionByUserId(permissionRpcDTO);
            detailBean.setGoodsPermissionMember(USER_TYPE_ZERO);
            if (CollectionUtils.isNotEmpty(userPermissionByUserId)) {
                detailBean.setGoodsPermissionMember(USER_TYPE_ONE);
            }
        } catch (Exception e) {
            // 异常降级返回有会员
            detailBean.setGoodsPermissionMember(USER_TYPE_ONE);
            log.error("getSingleDetail货源详情，设置发货人会员状态失败,{}", detailBean.getSrcMsgId(), e);
        }
    }



    /**
     *  运满满货源信息与特运通信息对应处理
     *
     * @param tytMbCargoSyncInfo
     * @param detailBean
     */
    private void copyProperties(MbCargoSyncInfoDO tytMbCargoSyncInfo, SingleGoodsDetailVO detailBean) {
        BeanUtils.copyProperties(tytMbCargoSyncInfo,detailBean);

        detailBean.setLoadingTime(tytMbCargoSyncInfo.getLoadTime());
        detailBean.setGiveGoodsPhone(tytMbCargoSyncInfo.getContactTelephone());
        detailBean.setWeight(String.valueOf(tytMbCargoSyncInfo.getMaxWeight() != 0 ? tytMbCargoSyncInfo.getMaxWeight() : tytMbCargoSyncInfo.getMinWeight()));
        detailBean.setHigh(String.valueOf(tytMbCargoSyncInfo.getMinHeight()));
        detailBean.setWide(String.valueOf(tytMbCargoSyncInfo.getMinWidth()));
        detailBean.setLength(String.valueOf(tytMbCargoSyncInfo.getMinLength()));
        detailBean.setInfoFee(BigDecimal.valueOf(tytMbCargoSyncInfo.getDepositAmt() / 100));
        detailBean.setPrice(String.valueOf(tytMbCargoSyncInfo.getExpectFreight() / 100));
        detailBean.setTaskContent(String.valueOf(tytMbCargoSyncInfo.getCargoName()));
        detailBean.setShuntingQuantity(tytMbCargoSyncInfo.getTruckCount());

        detailBean.setStartProvinc(tytMbCargoSyncInfo.getLoadProvinceName().replace("省","").replace("市",""));
        detailBean.setStartCity(tytMbCargoSyncInfo.getLoadCityName());
        detailBean.setStartArea(tytMbCargoSyncInfo.getLoadDistrictName());
        detailBean.setStartDetailAdd(tytMbCargoSyncInfo.getLoadDetailAddress());
        detailBean.setStartLongitude(String.valueOf(tytMbCargoSyncInfo.getLoadLon()));
        detailBean.setStartLatitude(String.valueOf(tytMbCargoSyncInfo.getLoadLat()));

        detailBean.setDestProvinc(tytMbCargoSyncInfo.getUnloadProvinceName().replace("省","").replace("市",""));
        detailBean.setDestCity(tytMbCargoSyncInfo.getUnloadCityName());
        detailBean.setDestArea(tytMbCargoSyncInfo.getUnloadDistrictName());
        detailBean.setDestDetailAdd(tytMbCargoSyncInfo.getUnloadDetailAddress());
        detailBean.setDestLongitude(String.valueOf(tytMbCargoSyncInfo.getUnloadLon()));
        detailBean.setDestLatitude(String.valueOf(tytMbCargoSyncInfo.getUnloadLat()));
        detailBean.setRefundFlag(tytMbCargoSyncInfo.getDepositReturn());

        detailBean.setExcellentGoods(YesOrNoEnum.YES.getId());
        detailBean.setPublishType(tytMbCargoSyncInfo.getDealMode() == 1 ? 2 : 1);


        /**
         * 挂车样式
         */
        JSONArray jsonArray = JSONArray.parseArray(tytMbCargoSyncInfo.getTruckTypes());
        List<Map> list = JSONArray.parseArray(jsonArray.toJSONString(), Map.class);
        List<String> carStyles = new ArrayList<>();
        list.stream().forEach(item -> {
            if(CarStyleEnum.FLAT_BOARD.getStyleName().equals(item.get("msg"))){
                carStyles.add(CarStyleEnum.FLAT.getStyleName());
            } else if(CarStyleEnum.HIGH_LOW_BOARD.getStyleName().equals(item.get("msg"))){
                carStyles.add(CarStyleEnum.HIGH.getStyleName());
            } else if(Objects.nonNull(item.get("msg"))&&String.valueOf(item.get("msg")).contains(CarStyleEnum.LADDER.getStyleName()) ){
                carStyles.add(CarStyleEnum.FLAT.getStyleName());
                carStyles.add(CarStyleEnum.HIGH.getStyleName());
                detailBean.setClimb("1");
            }
        });
        detailBean.setCarStyle(org.apache.commons.lang3.StringUtils.join(carStyles,"/"));

        /**
         * 所需车辆长度
         */
        JSONArray truckLengths = JSONArray.parseArray(tytMbCargoSyncInfo.getTruckLengths());
        List<String> array = JSONArray.parseArray(truckLengths.toJSONString(), String.class);
        detailBean.setCarLengthLabels(org.apache.commons.lang3.StringUtils.join(array,","));
    }

    /**
     * 官方授权昵称加密
     *
     * @param authName
     * @return
     */
    private String getAuthNameTea(String authName) {
        if (StringUtils.isNotBlank(authName)) {
            String checkAuthName = tytConfigRemoteService.getStringValue("tyt:plat:config:checkAuthName");
            if (StringUtils.isNotBlank(checkAuthName) && "1".equals(checkAuthName)) {
                return XXTea.Encrypt(authName, xxTeaProperties.getKey());
            }
        }
        return null;
    }

    /**
     * 详情页设置提示信息
     */
    private GoodsSingleDetailResultVO.TipInfoCollective getTipInfoCollective(Long userId, TransportMainDO transportMain) {
        GoodsSingleDetailResultVO.TipInfoCollective tipInfoCollective = new GoodsSingleDetailResultVO.TipInfoCollective();
        try {
            // 顶部文案，规则：晚上20点-24点展示
            tipInfoCollective.setTopPageTip(DateUtil.hour(new Date(), true) >= 20 ? "夜间车辆较少，请耐心等待" : "");

            // 中部显示装卸货电话按钮：条件：专车货源 & 未成交 & 开关开启 & 货源有装卸货联系电话
            if (Objects.equals(transportMain.getExcellentGoods(), 2)
                    && Objects.equals(transportMain.getStatus(), 1)
                    && Objects.equals(tytConfigRemoteService.getIntValue("detail_show_load_phone_btn"), 1)
                    && (StringUtils.isNotBlank(transportMain.getLoadCellPhone())
                    || StringUtils.isNotBlank(transportMain.getUnloadCellPhone()))
            ) {
                tipInfoCollective.setMiddlePageTip("可联系具体装卸货人，避免无法承运");
            }

            List<ExposureCardGiveawayRecordDO> recordList = exposureCardGiveawayService.getGiveawayRecord(transportMain.getSrcMsgId(), 1);
            if (CollectionUtils.isNotEmpty(recordList)) {
                Integer giveawayNum = recordList.get(0).getGiveawayNum();
                tipInfoCollective.setExposureContent("线上成交额外送" + giveawayNum + "张曝光卡");
                if (stringRedisTemplate.opsForValue().get(GOODS_DETAIL_EXPOSURE_TOAST_KEY + transportMain.getSrcMsgId()) == null) {
                    tipInfoCollective.setToastType(1);
                    tipInfoCollective.setToastTitle("该货源线上成交");
                    tipInfoCollective.setGiveawayNum(giveawayNum);
                    stringRedisTemplate.opsForValue().set(GOODS_DETAIL_EXPOSURE_TOAST_KEY + transportMain.getSrcMsgId(), "1", 86400, TimeUnit.SECONDS);
                }
            }

            String lastCallFeedback = callFeedbackLogService.getLastFeedback(userId, transportMain.getSrcMsgId());
            tipInfoCollective.setLastCallFeedBack(lastCallFeedback == null ? null : "上次沟通结果：" + lastCallFeedback);
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置提示信息异常，{}", transportMain.getSrcMsgId(), e);
        }

        return tipInfoCollective;
    }

    /**
     * 获取是否显示优惠券标签
     * 1. 条件：直客（个人货主或企业货主）发布的货源 或优车2.0 或抽佣货源
     * 2. 需要开关关闭
     */
    private Integer getShowDepositCouponsLabel(TransportMainDO transportMain) {
        try {
            Integer labelSwitch = tytConfigRemoteService.getIntValue("show_deposit_coupons_label_switch", 0);
            if (labelSwitch == 1) {
                // 直客发布的货源或优车2.0的货源或有抽佣标签的货源才提示
                TransportLabelJson labelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
                // 优车2.0 或 抽佣货源
                if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                        || Objects.equals(labelJson.getCommissionTransport(), 1))) {
                    return 1;
                }
                DwsNewIdentiwoDataRpcVO newIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(transportMain.getUserId());
                // 或 直客发布货源（直客=个人货主或企业货主）
                if (newIdentity != null && (Objects.equals(newIdentity.getType(), 3)
                        || Objects.equals(newIdentity.getType(), 4))) {
                    return 1;
                }
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，获取是否显示优惠券标签异常，{}", transportMain.getSrcMsgId(), e);
        }
        return 0;
    }


    /**
     * 设置显示超额保障提示文案：
     * 1. 开关控制，并可以修改文案
     * 2. 一人+一个货源只弹一次
     * 3. 直客发布的货源或优车2.0的货源或有抽佣标签的货源才提示
     */
    private String getGoodsDetailExcessCoverageTip(Long userId, TransportMainDO transportMain) {
        try {
            String excessCoverageTip = tytConfigRemoteService.getStringValue("excess_coverage_tip");
            // 为空代表开关关了
            if (StringUtils.isEmpty(excessCoverageTip)) {
                return "";
            }
            // 已浏览返回空
            boolean viewed = transportViewLogService.isViewed(userId, transportMain.getSrcMsgId());
            if (viewed) {
                return "";
            }
            // 直客发布的货源或优车2.0的货源或有抽佣标签的货源才提示
            TransportLabelJson labelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            // 优车2.0 或 抽佣货源
            if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                    || Objects.equals(labelJson.getCommissionTransport(), 1))) {
                return excessCoverageTip;
            }
            DwsNewIdentiwoDataRpcVO newIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(transportMain.getUserId());
            // 或 直客发布货源（直客=个人货主或企业货主）
            if (newIdentity != null && (Objects.equals(newIdentity.getType(), 3)
                    || Objects.equals(newIdentity.getType(), 4))) {
                return excessCoverageTip;
            }
        } catch (Exception e) {
            log.error("getSingleDetail货源详情，设置显示超额保障提示文案异常，{}", transportMain.getSrcMsgId(), e);
        }
        return "";
    }

    /**
     * 货源详情页货源诊断接口
     */
    @Override
    public GoodsDiagnosisDTO goodsDiagnosis(Long srcMsgId) {
        TransportMainDO transport = transportMainService.getTransportMainForId(srcMsgId);
        TransportMainExtendDO transportMainExtendDO = transportMainExtendService.getBySrcMsgId(srcMsgId);
        // 货源不是发布中 或 已过期，不显示货源诊断
        if (transport == null || transport.getStatus() != 1 || transport.getCtime().before(DateUtil.beginOfDay(new Date()))) {
            return null;
        }

        // 用户是否在新版发货ab测
        boolean inNewPublishPageAbTest = checkUserInNewPublishPageAbTest();

        // 是否有价
        boolean hasPrice = TransportUtil.hasPrice(transport.getPrice());
        // 是否一口价
        boolean isFixed = PublishTypeEnum.isFixed(transport.getPublishType());
        // 目的地是否有详细地址，条件： 目的地无详细地址或详细地址为区
        boolean hasDestDetail = StringUtils.isNotBlank(transport.getDestDetailAdd())
                && !Objects.equals(transport.getDestDetailAdd(), transport.getDestArea());
        // 相似货源数
        int similarityCount = transportService.countSimilarityTransport(transport.getSimilarityCode(), srcMsgId);
        // 浏览&联系次数
        TransportDispatchViewDO viewContactCountDO = dispatchViewService.getContactAndViewCount(srcMsgId);
        int viewTimes = viewContactCountDO == null ? 0 : viewContactCountDO.getViewCount(); // 浏览次数
        int contactTimes = viewContactCountDO == null ? 0 : viewContactCountDO.getContactCount(); // 联系次数
        // log.info("货源详情页诊断接口，货源id:{}, 浏览次数：{}, 联系次数：{}", srcMsgId, viewTimes, contactTimes);

        // 封装返回体
        GoodsDiagnosisDTO goodsDiagnosisDTO = new GoodsDiagnosisDTO();

        //传递原始运费
        goodsDiagnosisDTO.setPrice(StringUtils.isNotBlank(transport.getPrice()) ? new BigDecimal(transport.getPrice()) : null);

        goodsDiagnosisDTO.setSubTitle("优化货源，提升找车速度");
        if (!isFixed && !hasPrice) {
            //无价电议副标题
            Date now = new Date();
            Date endDate = DateUtil.beginOfDay(DateUtil.offsetDay(now, 1)).toJdkDate();
            Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(now, -7)).toJdkDate();
            SameCityTransportCountVO lowSameCityTransportCount = new SameCityTransportCountVO();
            Integer sameCityTransportAllCount = transportMainMapper.getSameCityTransportAllCount(transport.getStartCity(), transport.getDestCity(), 1, startDate, endDate);
            Integer sameCityTransportBargainCont = transportMainMapper.getSameCityTransportBargainCont(transport.getStartCity(), transport.getDestCity(), 1, startDate, endDate);
            lowSameCityTransportCount.setAllCount(sameCityTransportAllCount);
            lowSameCityTransportCount.setBargainCont(sameCityTransportBargainCont);
            if (lowSameCityTransportCount.getAllCount() != null && lowSameCityTransportCount.getBargainCont() != null) {
                BigDecimal lowRatio = new BigDecimal(0);
                if (lowSameCityTransportCount.getAllCount() > 0 && lowSameCityTransportCount.getBargainCont() > 0) {
                    lowRatio = new BigDecimal(lowSameCityTransportCount.getBargainCont()).divide(new BigDecimal(lowSameCityTransportCount.getAllCount()), 2, RoundingMode.UP);
                }
                lowRatio = lowRatio.multiply(new BigDecimal(100)).add(new BigDecimal(10));
                if (lowRatio.compareTo(new BigDecimal(40)) > 0) {
                    lowRatio = new BigDecimal(40);
                }
                goodsDiagnosisDTO.setSubTitle("您未填写运费，找车成功约<span style=\"color:#FF5B00;\">" + lowRatio.setScale(0, RoundingMode.DOWN).toPlainString() + "%</span>");
                goodsDiagnosisDTO.setSubTitleType(1);
            }
        }

        // 设置标题，根据浏览数判断不同文案
        goodsDiagnosisDTO.setTitle(getDiagnosisPrompt(0, "title", viewTimes, 0, 0, null));
        // 设置进度条比例
        goodsDiagnosisDTO.setProgressBarRatio(getProgressBarRatio(hasPrice, isFixed, hasDestDetail));
        goodsDiagnosisDTO.setSrcMsgId(transport.getSrcMsgId());
        goodsDiagnosisDTO.setHasSimilarityGoods(similarityCount > 0 ? 1 : 0);
        if (goodsDiagnosisDTO.getHasSimilarityGoods() == 1) {
            //是否相似货源首位
            Boolean isTop = similarityGoodsRemoteService.similarityGoodsIsTop(srcMsgId, transport.getSimilarityCode());
            log.info("货源诊断 相似货源是否首位 srcMsgId:{} similarityCode:{}，结果: {}", srcMsgId, transport.getSimilarityCode(), isTop);
            goodsDiagnosisDTO.setSimilarityGoodsTop(isTop ? 1 : 0);
        }

        CarryPriceVO thPrice = thPriceService.getThPrice(transport);
        log.info("货源诊断 优车2.0建议价 结果: {}", JSON.toJSONString(thPrice));
        if (thPrice != null) {
            //低中高极速价格
            goodsDiagnosisDTO.setSlowPrice(new BigDecimal(thPrice.getThMinPrice()));
            goodsDiagnosisDTO.setMediumPrice(new BigDecimal(thPrice.getFixPriceMin()));
            goodsDiagnosisDTO.setHigherPrice(new BigDecimal(thPrice.getFixPriceFast()));
            goodsDiagnosisDTO.setTopPrice(new BigDecimal(thPrice.getFixPriceMax()));
        }

        if (transport.getExcellentGoods() != null && transport.getExcellentGoods() == 2) {
            goodsDiagnosisDTO.setSpecialCar(1);
            //专车只有高速和极速,高速为专车价格,极速为专车价格的120%
            goodsDiagnosisDTO.setSlowPrice(null);
            goodsDiagnosisDTO.setMediumPrice(null);
            CalculatePriceDTO calculatePriceDTO = new CalculatePriceDTO();
            calculatePriceDTO.setDistanceKilometer(transport.getDistance());
            calculatePriceDTO.setStartCity(transport.getStartCity());
            calculatePriceDTO.setDestCity(transport.getDestCity());
            calculatePriceDTO.setWeight(transport.getWeight());
            calculatePriceDTO.setUseCarType(transportMainExtendDO.getUseCarType());
            calculatePriceDTO.setClientVersion(transport.getClientVersion());
            calculatePriceDTO.setCargoOwnerId(transport.getCargoOwnerId() == null || transport.getCargoOwnerId().equals(0L) ? 1L : transport.getCargoOwnerId());
            calculatePriceDTO.setUserId(transport.getUserId());

            CalcSpecialGoodsPriceResultDTO calcSpecialGoodsPriceResultDTO = calcSpecialGoodsPriceService.calculatePriceV2(calculatePriceDTO);
            log.info("货源诊断 计算专车运费 请求参数:{}，结果: {}", JSON.toJSONString(calculatePriceDTO), JSON.toJSONString(calcSpecialGoodsPriceResultDTO));
            if (calcSpecialGoodsPriceResultDTO != null && calcSpecialGoodsPriceResultDTO.getPrice() != null) {
                if (calcSpecialGoodsPriceResultDTO.getPriceType() != null && calcSpecialGoodsPriceResultDTO.getPriceType() == 2) {
                    log.info("货源诊断 专车灵活运价高速运费使用下限最低值");
                    goodsDiagnosisDTO.setHigherPrice(calcSpecialGoodsPriceResultDTO.getLowerLimitPrice().compareTo(calcSpecialGoodsPriceResultDTO.getPrice()) <= 0
                            ? calcSpecialGoodsPriceResultDTO.getLowerLimitPrice() : calcSpecialGoodsPriceResultDTO.getPrice());
                } else {
                    goodsDiagnosisDTO.setHigherPrice(calcSpecialGoodsPriceResultDTO.getPrice());
                }
                goodsDiagnosisDTO.setTopPrice(goodsDiagnosisDTO.getHigherPrice().multiply(new BigDecimal("1.2")));
            }
        }

        int viewCount = transportViewLogMapper.getViewNumOfTsId(srcMsgId);
        goodsDiagnosisDTO.setViewCount(viewCount);

        // 无价且有相似货源返回相似货源最高价
        if (!hasPrice && goodsDiagnosisDTO.getHasSimilarityGoods() == 1) {
            BigDecimal similarityGoodsTopPrice = transportMainService.getSimilarityGoodsTopPrice(transport.getSimilarityCode());
            log.info("货源诊断 无价货源相似货源最高价 请求参数:{}，结果: {}", transport.getSimilarityCode(), similarityGoodsTopPrice);
            goodsDiagnosisDTO.setSimilarityGoodsTopPrice(similarityGoodsTopPrice);
        }

        // 填价，条件：无价货源
        addDiagnosisTask(goodsDiagnosisDTO, FILL_PRICE.getCode(), !hasPrice, similarityCount, contactTimes, transport);
        // 加价，条件：有价货源
        addDiagnosisTask(goodsDiagnosisDTO, ADD_PRICE.getCode(), hasPrice, similarityCount, 0, transport);
        // 曝光任务
        addDiagnosisTask(goodsDiagnosisDTO, REFRESH.getCode(), true, similarityCount, 0, transport);
        // 补全货物信息，条件：当货物的长宽高中存在无值的情况 或者 存在“1”时展示
        addDiagnosisTaskOfGoodsInfo(goodsDiagnosisDTO, transport, viewTimes);
        // 转一口价，条件：电议货源
        // 新版发货，货源详情不展示转一口价任务
        if (!inNewPublishPageAbTest) {
            addDiagnosisTask(goodsDiagnosisDTO, CHANGE_TO_FIX_PRICE.getCode(), !isFixed, similarityCount, 0, transport);
        }
        // 同步YMM授权（写在这里是防止代码冲突）
        if (checkCanAgreeSyncYmm(transport.getUserId())) {
            //目前没有授权同步YMM且不在黑名单中且当前版本大于等于6690才显示授权任务
            addDiagnosisTask(goodsDiagnosisDTO, SYNC_YMM.getCode(), true, 0, 0, transport);
        }

        Integer freightDepot = transportQuotedPriceMapper.isFreightDepot(transport.getUserId());
        if (freightDepot != null && freightDepot > 0) {
            GoodCarPriceTransportTabAndBIPriceDTO showGoodCarPriceTransportTab = goodCarPriceTransportService.isShowGoodCarPriceTransportTab(goodCarPriceTransportService.buildCarryDTOByTransport(transport));
            if (showGoodCarPriceTransportTab != null && showGoodCarPriceTransportTab.getShowTab()) {
                //如果货主身份是货站但是货源满足优车2.0条件，则重新按12345排序
                goodsDiagnosisDTO.setTaskList(goodsDiagnosisDTO.getTaskList().stream().sorted(Comparator.comparingInt(GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO::getType)).collect(Collectors.toList()));
            }
        } else {
            //如果货主身份不是货站则重新按12345排序
            goodsDiagnosisDTO.setTaskList(goodsDiagnosisDTO.getTaskList().stream().sorted(Comparator.comparingInt(GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO::getType)).collect(Collectors.toList()));
        }
        // 回价助手任务
        addPriceAssistantDiagnosisTask(transport, goodsDiagnosisDTO);

        //相似货源非首位只有补全参数任务、填价任务、加价任务
        if (goodsDiagnosisDTO.getHasSimilarityGoods() == 1 && goodsDiagnosisDTO.getSimilarityGoodsTop() == 0) {
            log.info("相似货源非首位只显示补全参数任务、填价任务、加价任务");
            goodsDiagnosisDTO.setTaskList(goodsDiagnosisDTO.getTaskList().stream().filter(task -> task.getType() == 4 || task.getType() == 1 || task.getType() == 2).collect(Collectors.toList()));
            //相似货源非首位，有运费且价格低于中速或者没有中速价格，才显示加价任务
            if (goodsDiagnosisDTO.getMediumPrice() != null && goodsDiagnosisDTO.getPrice() != null && goodsDiagnosisDTO.getPrice().compareTo(goodsDiagnosisDTO.getMediumPrice()) >= 0) {
                log.info("相似货源非首位 有运费且价格低于中速或者没有中速价格，才显示加价任务");
                goodsDiagnosisDTO.setTaskList(goodsDiagnosisDTO.getTaskList().stream().filter(task -> task.getType() != 2).collect(Collectors.toList()));
            }
        } else {
            //无相似货源或者处于相似货源首位并且有查看司机
            //修改填价任务的文案
            if (goodsDiagnosisDTO.getViewCount() != null && goodsDiagnosisDTO.getViewCount() > 0) {
                Integer goodsDiagnosisNoSimilarityViewCountCoefficient = tytConfigRemoteService.getIntValue("goods_diagnosis_view_count_coefficient", 1);
                goodsDiagnosisDTO.getTaskList().stream().filter(task -> task.getType() == 1).forEach(task -> {
                    task.setPrompt("已推荐" + (goodsDiagnosisDTO.getViewCount() * goodsDiagnosisNoSimilarityViewCountCoefficient) + "位司机，焦急等待您的出价");
                });
            } else {
                goodsDiagnosisDTO.getTaskList().stream().filter(task -> task.getType() == 1).forEach(task -> {
                    task.setPrompt("查看司机较少，填价后找车更快");
                });
            }

            //获取最近加价30分钟缓存，如果缓存存在代表最新一次加价距离现在未超过30分钟，判断当前运费是否超过极速，如果超过极速则不展示加价任务
            String addPrice30MinuteCache = stringRedisTemplate.opsForValue().get(RedisKeyConstant.ADD_PRICE_30_MINUTE + srcMsgId);
            if (addPrice30MinuteCache != null && goodsDiagnosisDTO.getTopPrice() != null && goodsDiagnosisDTO.getPrice() != null && goodsDiagnosisDTO.getPrice().compareTo(goodsDiagnosisDTO.getTopPrice()) >= 0) {
                GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO goodsDiagnosisTaskDTO = goodsDiagnosisDTO.getTaskList().stream().filter(task -> task.getType() == 2).findFirst().orElse(null);
                if (goodsDiagnosisTaskDTO != null) {
                    goodsDiagnosisDTO.getTaskList().remove(goodsDiagnosisTaskDTO);
                    log.info("非相似货源或相似货源处于首位 且 最新一次加价不超过30分钟 且 当前运费超过极速运费，不展示加价任务");
                }
            }
        }

        //补全货物信息任务强制挪到列表第一位,补全货物信息任务的type是4
        GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO goodsDiagnosisTaskDTO = goodsDiagnosisDTO.getTaskList().stream().filter(task -> task.getType() == 4).findFirst().orElse(null);
        if (goodsDiagnosisTaskDTO != null) {
            goodsDiagnosisDTO.getTaskList().remove(goodsDiagnosisTaskDTO);
            goodsDiagnosisDTO.getTaskList().add(0, goodsDiagnosisTaskDTO);
        }

        return goodsDiagnosisDTO;
    }

    /**
     * 用户是否在新版发货ab测
     *
     * @return
     */
    private boolean checkUserInNewPublishPageAbTest() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (Objects.nonNull(loginUser)) {
            Integer userType = abTestRemoteService.getUserType(AbtestKeyConstant.NEW_PUBLISH_PAGE, loginUser.getUserId());
            return Objects.equals(YesOrNoEnum.YES.getId(), userType);
        }
        return false;
    }

    private boolean checkCanAgreeSyncYmm(Long userId) {
        int type = 6;
        String sinceVersion = promptProperties.getDiagnosisPrompt(type, "since-version");
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        // 低于6690版本不显示该任务
        if (baseParam == null || baseParam.getClientVersion() == null
                || Integer.parseInt(sinceVersion) > Integer.parseInt(baseParam.getClientVersion())) {
            return false;
        }

        TytUserempowerSyncgoodsDO tytUserempowerSyncgoodsDO = tytUserempowerSyncgoodsMapper.selectByUserId(userId);
        if (tytUserempowerSyncgoodsDO != null && tytUserempowerSyncgoodsDO.getStatus() == 0) {
            //已授权
            return false;
        }

        TytSyncgoodsUserBlackDO tytSyncgoodsUserBlackDO = tytSyncgoodsUserBlackMapper.selectByUserId(userId);
        //在黑名单中
        return tytSyncgoodsUserBlackDO == null;
    }

    /**
     * 返回进度条比例
     * 1. 极低：进度0%，货源无价 + 非一口价 + 目的地无详细地址
     * 2. 较低：进度25%，货物无价 + 非一口价 + 目的地有详细地址，
     * 3. 高1：进度50%，货源有价 + 非一口价 + 目的地无详细地址
     * 4. 高2：进度75%，货源有价 + 非一口价 + 目的地有详细地址 或 货源有价 + 一口价 + 目的地无详细地址
     * 5. 极高：进度100%，有价货源 + 一口价 + 目的地有详细地址
     *
     * @param hasPrice      是否有价
     * @param isFixed       是否一口价
     * @param hasDestDetail 目的地是否有详细地址
     */
    private Integer getProgressBarRatio(boolean hasPrice, boolean isFixed, boolean hasDestDetail) {
        int score = 0;
        score += hasPrice ? 50 : 0;
        score += hasDestDetail ? 25 : 0;
        score += isFixed ? 25 : 0;
        return score;
    }

    /**
     * 添加回价助手任务
     *
     * @param transport
     * @param goodsDiagnosisDTO
     */
    private void addPriceAssistantDiagnosisTask(TransportMainDO transport, GoodsDiagnosisDTO goodsDiagnosisDTO) {
        int type = ADD_PRICE_ASSISTANT.getCode();
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        String sinceVersion = promptProperties.getDiagnosisPrompt(type, "since-version");
        if (baseParam == null || baseParam.getClientVersion() == null
                || Integer.parseInt(sinceVersion) > Integer.parseInt(baseParam.getClientVersion())) {
            return;
        }
        // 普货、优车、非专票
        if (Objects.equals(transport.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) ||
                Objects.equals(transport.getInvoiceTransport(), InvoiceTransportEnum.YES.getCode())) {
            return;
        }
        // 货源未设置回价助手
        TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(transport.getSrcMsgId());
        if (Objects.nonNull(mainExtendDO) && Objects.nonNull(mainExtendDO.getPriceCap()) && mainExtendDO.getPriceCap() > 0) {
            return;
        }
        // 货主在回价助手ab测
        Integer userType = abTestRemoteService.getUserType(PUBLISH_PRICE_ASSISTANT_ABTEST_CODE, transport.getUserId());
        if (!Objects.equals(userType, 1)) {
            return;
        }

        addDiagnosisTask(goodsDiagnosisDTO, type, true, 0, 0, transport);
    }

    /**
     * 添加任务
     *
     * @param type            1去填价，2加价，3转一口价，4补全货物信息，5调整装货时间
     * @param condition       条件
     * @param similarityCount 如果大于1，就说明有相似货源
     */
    private void addDiagnosisTask(GoodsDiagnosisDTO goodsDiagnosisDTO, int type, boolean condition, int similarityCount,
                                  int contactCount, TransportMainDO transportMainDO) {
        if (condition) {
            GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO task = new GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO();
            task.setType(type);
            int sameTransportPriceType = 0;
            String priceDifference = null;
            if (type == 2) {
                // 加价任务中需要细分: 1-货源当前运费，低于近1个月相似货源平均成交价*80%; 2-货源当前运费，在近1个月相似货源平均成交价的80%（含）-90%（含）之间
                BigDecimal sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPriceByDays(transportMainDO.getSrcMsgId(), 30);
                if (Objects.nonNull(sameTransportAvgPrice)) {
                    BigDecimal price = new BigDecimal(transportMainDO.getPrice());
                    BigDecimal avgPrice80Percent = sameTransportAvgPrice.multiply(new BigDecimal("0.8"));
                    if (price.compareTo(avgPrice80Percent) < 0) {
                        sameTransportPriceType = 1;
                        priceDifference = sameTransportAvgPrice.subtract(price).toString();
                        task.setShowTagMsg("推荐");
                    } else {
                        BigDecimal avgPrice90Percent = sameTransportAvgPrice.multiply(new BigDecimal("0.9"));
                        if (price.compareTo(avgPrice90Percent) <= 0) {
                            sameTransportPriceType = 2;
                        }
                    }
                }
            }
            task.setTitle(getDiagnosisPrompt(type, "title", similarityCount, contactCount, sameTransportPriceType, priceDifference));
            task.setPrompt(getDiagnosisPrompt(type, "prompt", similarityCount, contactCount, sameTransportPriceType, priceDifference));
            if (type == 5) {
                // 权益查询
                AuthPermissionRpcDTO authPermissionRpcDTO = new AuthPermissionRpcDTO();
                authPermissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源曝光权益);
                TransportDO transport = transportService.getLastBySrcMygId(goodsDiagnosisDTO.getSrcMsgId());
                authPermissionRpcDTO.setUserId(transport.getUserId());
                AuthPermissionRpcVO authPermissionRpcVO = userPermissionRemoteService.checkAuthPermission(authPermissionRpcDTO);
                // 最大刷新次数
                Integer maxResendCounts = tytConfigRemoteService.getIntValue("tyt:plat:config:transport_top_max_count", 16);
                MyTransportListVO myTransportListVO = new MyTransportListVO();
                TransportMainServiceImpl.propertiesCopy(transport, myTransportListVO);
                transportMainService.initGoodsRefresh(authPermissionRpcVO, maxResendCounts, myTransportListVO);
                if (myTransportListVO.getShowGoodsRefresh() == 1) {
                    task.setBottonCanClick(false);
                }
            }
            goodsDiagnosisDTO.addTask(task);
        }
    }

    /**
     * 获取开关管理里配置的标题和提示，
     * key: goods_diagnosis_title_1
     * key: goods_diagnosis_prompt_1_similar
     *
     * @param module          1:title标题；2:prompt提示
     * @param type            类型：0模块标题，1去填价，2加价，3转一口价，4补全货物信息，5调整装货时间
     * @param similarityCount 相似货源数
     * @param contactCount    联系次数
     * @param sameTransportPriceType 相似货源平均成交价类型：1-低于近1个月相似货源平均成交价*80%，2-在近1个月相似货源平均成交价的80%（含）-90%（含）之间
     * @param priceDifference 运费差价
     */
    private String getDiagnosisPrompt(int type, String module, int similarityCount, int contactCount,
                                      int sameTransportPriceType, String priceDifference) {
        String key = module;
        if (sameTransportPriceType > 0) {
            key += "-s-" + sameTransportPriceType;
        } else {
            if (contactCount > 0) {
                key += "-p";
            } else if (similarityCount > 0) {
                key += "-x";
            }
        }
        String prompt = promptProperties.getDiagnosisPrompt(type, key);
        if (sameTransportPriceType == 1) {
            return prompt.replace("X", StringUtils.isBlank(priceDifference) ? "0" : priceDifference);
        }
        if (similarityCount > 0) {
            return prompt.replace("X", String.valueOf(similarityCount));
        }
        return prompt;
    }

    /**
     * 补全货物信息任务
     */
    private void addDiagnosisTaskOfGoodsInfo(GoodsDiagnosisDTO goodsDiagnosisDTO, TransportMainDO transport, int viewTimes) {
        int type = 4;
        String sinceVersion = promptProperties.getDiagnosisPrompt(type, "since-version");
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        // 低于6630版本不显示该任务
        if (baseParam == null || baseParam.getClientVersion() == null
                || Integer.parseInt(sinceVersion) > Integer.parseInt(baseParam.getClientVersion())) {
            return;
        }
        // 条件：当货物的长宽高中存在无值的情况显示 或者 存在“1”时展示
        boolean condition = TransportUtil.isInvalidSize(transport.getLength())
                || TransportUtil.isInvalidSize(transport.getWide())
                || TransportUtil.isInvalidSize(transport.getHigh())
                || TransportUtil.isInvalidSize(transport.getWeight());
        if (!condition) {
            return;
        }
        GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO task = new GoodsDiagnosisDTO.GoodsDiagnosisTaskDTO();
        task.setType(type);
        task.setTitle(promptProperties.getDiagnosisPrompt(type, "title"));
        if (viewTimes <= 10) {
            task.setPrompt(promptProperties.getDiagnosisPrompt(type, "prompt"));
        } else {
            task.setPrompt(promptProperties.getDiagnosisPrompt(type, "prompt-x"));
        }
        goodsDiagnosisDTO.addTask(task);
    }

    /**
     * 货源详情是否展示加价弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public ShowAddPriceDTO showAddPrice(Long srcMsgId) {
        ShowAddPriceDTO priceDTO = new ShowAddPriceDTO();
        // 每个货源只弹一次
        String cacheValue = stringRedisTemplate.opsForValue().get(RedisKeyConstant.GOODS_DETAIL_AUTO_ADD_PRICE_POPUP_KEY + srcMsgId);
        if (StringUtils.isNotBlank(cacheValue)) {
            return priceDTO;
        }
        TransportMainDO main = transportMainService.getTransportMainForId(srcMsgId);
        if (Objects.isNull(main) || !Objects.equals(main.getStatus(), TransportStatusEnum.VALID.getCode())
                || main.getCtime().before(TimeUtil.getStartOfDay(new Date()))) {
            return priceDTO;
        }
        Date minutesBefore = TimeUtil.addMinute(new Date(), -30);
        // 超过30分钟未成交
        if (!main.getCtime().before(minutesBefore)) {
            return priceDTO;
        }
        // 货源查看司机数>=10
        TransportDispatchViewDO contactAndViewCount = dispatchViewService.getContactAndViewCount(srcMsgId);
        if (contactAndViewCount.getViewCount() < 10) {
            return priceDTO;
        }
        // 30分钟内未加价
        Date lastAddMoneyTime = backoutReasonService.getLastAddMoneyTime(srcMsgId);
        if (Objects.nonNull(lastAddMoneyTime) && !lastAddMoneyTime.before(minutesBefore)) {
            return priceDTO;
        }
        priceDTO.setShowPopup(true);
        priceDTO.setTitle("有" + contactAndViewCount.getViewCount() + "名司机查看但未接单\n适当加价成交更快");
        stringRedisTemplate.opsForValue().set(RedisKeyConstant.GOODS_DETAIL_AUTO_ADD_PRICE_POPUP_KEY + srcMsgId, "1", 24, TimeUnit.HOURS);
        return priceDTO;
    }

    /**
     * 货源详情页货物保障文案
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public GoodsExcessCoveragePromptDTO getGoodsExcessCoveragePrompt(Long srcMsgId) {
        TransportMainDO transportMain = transportMainService.getById(srcMsgId);
        if (transportMain == null) {
            return null;
        }

        // 是否是专属保障货源（抽佣&直客）
        boolean isExcessCoverageGoods = false;
        TransportLabelJson labelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
        // 抽佣货源
        if (labelJson != null && Objects.equals(labelJson.getCommissionTransport(), 1)) {
            isExcessCoverageGoods = true;
        } else {
            DwsNewIdentiwoDataRpcVO userIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(transportMain.getUserId());
            // 直客发布货源（直客=个人货主或企业货主）
            if (userIdentity != null && UserBiIdentityEnum.isCargoOwner(userIdentity.getType())) {
                isExcessCoverageGoods = true;
            }
        }

        // 返回货源详情页货物保障文案
        GoodsExcessCoveragePromptDTO excessCoveragePrompt = new GoodsExcessCoveragePromptDTO();
        Integer excellentGoods = transportMain.getExcellentGoods();
        if (excellentGoods == 0 && !isExcessCoverageGoods) {
            excessCoveragePrompt.setType(1);
            excessCoveragePrompt.setTitle("爽约保障 延误保障 加价管控");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
        } else if (excellentGoods == 0) {
            excessCoveragePrompt.setType(2);
            excessCoveragePrompt.setTitle("挟货保障 延误保障 实时追踪");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
            excessCoveragePrompt.setUrl(publicResourceRemoteService.getByName("goods_guarantee_page_url").getValue());
        } else if (excellentGoods == 1) {
            excessCoveragePrompt.setType(3);
            excessCoveragePrompt.setTitle("挟货保障 延误保障 实时追踪");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
            excessCoveragePrompt.setUrl(publicResourceRemoteService.getByName("youche_landing_page_url").getValue());
        } else if (excellentGoods == 2) {
            excessCoveragePrompt.setType(4);
            excessCoveragePrompt.setTitle("极速接单 专人服务 优选司机");
            excessCoveragePrompt.setContent("线下交易或取消订单将不再享受平台保障");
            excessCoveragePrompt.setUrl(publicResourceRemoteService.getByName("special_goods_guarantee_page_url").getValue());
        }
        return excessCoveragePrompt;
    }

    /**
     * 根据车主用户ID和货源ID获取技术服务费用
     *
     * @param carUserId
     * @param srcMsgId
     */
    @Override
    public TecServiceFeeVO tecServiceFee(Long carUserId, Long srcMsgId) {
        TransportTecServiceFeeDO tytTransportTecServiceFee = transportTecServiceFeeService.getBySrcMsgId(srcMsgId);
        TecServiceFeeVO tecServiceFeeVO = new TecServiceFeeVO();
        if (tytTransportTecServiceFee != null) {
            List<UserPermissionRpcVO> userPermission = getEfficientUserPermission(carUserId, UserPermissionTypeEnum.CAR_VIP.getTypeId());
            List<UserPermissionRpcVO> userPermissionCarSmallMember = getEfficientUserPermission(carUserId, UserPermissionTypeEnum.CAR_NUM_MEAL.getTypeId());
            if (CollectionUtils.isNotEmpty(userPermission) || CollectionUtils.isNotEmpty(userPermissionCarSmallMember)) {
                // 有车会员（包含小会员），展示车会员技术服务费
                tecServiceFeeVO.setTecServiceFee(tytTransportTecServiceFee.getMemberAfterFee());
                tecServiceFeeVO.setTecServiceFeeAfterDiscount(tytTransportTecServiceFee.getMemberBeforeFee());
                if (tytTransportTecServiceFee.getMemberAfterFee() != null && tytTransportTecServiceFee.getMemberBeforeFee() != null) {
                    tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(tytTransportTecServiceFee.getMemberBeforeFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()));
                }
            } else {
                // 无车会员，展示车会员技术服务费
                tecServiceFeeVO.setTecServiceFee(tytTransportTecServiceFee.getNoMemberAfterFee());
                tecServiceFeeVO.setTecServiceFeeAfterDiscount(tytTransportTecServiceFee.getNoMemberBeforeFee());
                if (tytTransportTecServiceFee.getNoMemberAfterFee() != null && tytTransportTecServiceFee.getNoMemberBeforeFee() != null) {
                    tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(tytTransportTecServiceFee.getNoMemberBeforeFee().subtract(tytTransportTecServiceFee.getNoMemberAfterFee()));
                }
                if (tytTransportTecServiceFee.getMemberAfterFee() != null
                        && tytTransportTecServiceFee.getNoMemberAfterFee() != null
                        && tytTransportTecServiceFee.getNoMemberAfterFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()).compareTo(BigDecimal.ZERO) > 0) {
                    tecServiceFeeVO.setTecServiceFeeWord("成为会员本单为您省"
                            + tytTransportTecServiceFee.getNoMemberAfterFee().subtract(tytTransportTecServiceFee.getMemberAfterFee()).setScale(0).toString()
                            + "元 去购买");
                    // 有小会员
                    SmallMealListVO smallMeal = smallMealRemoteService.getList();
                    if (Objects.nonNull(smallMeal) && CollectionUtils.isNotEmpty(smallMeal.getMealList())
                            && Objects.nonNull(smallMeal.getMealList().get(0).getId())) {
                        tecServiceFeeVO.setTecServiceFeeBuySmallMemberId(smallMeal.getMealList().get(0).getId());
                    } else {
                        tecServiceFeeVO.setTecServiceFeeBuySmallMemberId(-1L);
                    }
                }
            }

            // 判断是否符合直接免佣条件
            CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = transportMainService.checkIsNeedFreeTecSericeFeeByCarUser(carUserId, srcMsgId);
            if (checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
                tecServiceFeeVO.setTecServiceFeeWord(checkIsNeedFreeTecServiceFeeVO.getWord());
                tecServiceFeeVO.setTecServiceFee(BigDecimal.ZERO);
                //折前折后技术服务费差值直接使用折前技术服务费
                tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(tecServiceFeeVO.getTecServiceFeeAfterDiscount());
            }

        } else {
            //非抽佣货源，技术服务费正常展示
            TransportMainDO bySrcMsgId = transportMainService.getById(srcMsgId);
            tecServiceFeeVO.setTecServiceFee(bySrcMsgId.getTecServiceFee());
            tecServiceFeeVO.setTecServiceFeeAfterDiscount(bySrcMsgId.getTecServiceFee());
            tecServiceFeeVO.setTecServiceFeeAfterDiscountDValue(BigDecimal.ZERO);
        }
        return tecServiceFeeVO;
    }

}
