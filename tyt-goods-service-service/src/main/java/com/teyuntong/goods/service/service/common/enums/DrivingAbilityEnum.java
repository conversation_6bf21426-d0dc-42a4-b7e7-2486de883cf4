package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 驾驶能力枚举
 * <AUTHOR>
 * @since 2024-06-25 18:16
 */
@Getter
public enum DrivingAbilityEnum {
    // 挖掘机、装载机、自卸车、高空作业车、收割机、压路机、起重机、推土机、打桩机、旋挖钻机、摊铺机、平地机、铣刨机
    EXCAVATOR("挖掘机"),
    LOADER("装载机"),
    DUMPER("自卸车"),
    AERIAL_WORK_VEHICLE("高空作业车"),
    HARVESTER("收割机"),
    ROAD_ROLLER("压路机"),
    CRANE("起重机"),
    BULLDOZER("推土机"),
    PILE_DRIVER("打桩机"),
    ROTARY_DRILL("旋挖钻机"),
    PAVER("摊铺机"),
    LAND_GRADER("平地机"),
    MILLING_MACHINE("铣刨机")
    ;

    /**
     * 是否在驾驶能力枚举中
     * @param name
     * @return
     */
    public static boolean haveAbility(String name) {
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        for (DrivingAbilityEnum item : DrivingAbilityEnum.values()) {
            if (item.getName().equals(name)) {
                return true;
            }
        }
        return false;
    }

    private String name;
    DrivingAbilityEnum(String name) {
        this.name = name;
    }
}
