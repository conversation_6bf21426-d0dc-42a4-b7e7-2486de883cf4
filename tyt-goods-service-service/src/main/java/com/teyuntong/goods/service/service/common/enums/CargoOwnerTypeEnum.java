package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 专车货主类型枚举  1企业 2个人
 *
 * <AUTHOR>
 * @since 2024-09-29 11:09
 */
@Getter
public enum CargoOwnerTypeEnum {
    ENTERPRISE(1, "企业"),
    PERSONAL(2, "个人")
    ;
    private Integer code;
    private String name;
    CargoOwnerTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
