package com.teyuntong.goods.service.service.rpc.publish.builder;

import com.teyuntong.goods.service.service.biz.cover.mybatis.entity.CoverGoodsConfigDO;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsConfigService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.enums.CoverGoodsEnum;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 设置优先推荐过期时间
 *
 * <AUTHOR>
 * @since 2025/02/21 15:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PriorityRecommendExpireBuilder {

    private final ABTestRemoteService abTestRemoteService;
    private final CoverGoodsConfigService coverGoodsConfigService;

    /**
     * 6500需求：只要勾选优推好车主，且走了好货模型就设置
     */
    public void build(DirectPublishProcessBO processBO) {
        TransportMainDO newMainDO = processBO.getTransportMain();
        TransportMainExtendDO mainExtend = processBO.getMainExtend();

        if (mainExtend.getGoodModelLevel() == null) {
            newMainDO.setPriorityRecommendExpireTime(null);
            return;
        }

        //判断是否是优推好车主如果是优推好车主，直接发布时重新计算有效时间
        boolean isPriorityRecommend;
        // 判断用户是否在白名单内
        if (Objects.equals(newMainDO.getExcellentGoods(), 1)) {
            // 优车货源
            Integer userType = abTestRemoteService.getUserType("recommend_excellent", newMainDO.getUserId());
            // 如果之前没捂货，判断优车货源是否默认为优推好车主，是则开启捂货
            if (Objects.equals(userType, 1) && newMainDO.getPriorityRecommendExpireTime() == null) {
                CoverGoodsConfigDO coverGoodsConfig = coverGoodsConfigService.getByCode(CoverGoodsEnum.EXCELLENT_COVER.getConfigCode());
                isPriorityRecommend = coverGoodsConfig != null && Objects.equals(coverGoodsConfig.getConfigItem(), 1);
            } else {
                isPriorityRecommend = Objects.equals(userType, 1);
            }
        } else {
            // 普通货源
            Integer userType = abTestRemoteService.getUserType("priority_recommend", newMainDO.getUserId());
            isPriorityRecommend = Objects.equals(userType, 1);
        }
        if (isPriorityRecommend) {
            // 昨天的货源直接发布是新货源，重新计算捂货时间
            if (processBO.getDirectPublishBO().isHistoryGoods()) {
                Integer xSecond = coverGoodsConfigService.selectXTime();
                Date priorityRecommendExpireTime = DateUtils.addSeconds(newMainDO.getPubDate(), xSecond);
                newMainDO.setPriorityRecommendExpireTime(priorityRecommendExpireTime);
            }
        } else {
            newMainDO.setPriorityRecommendExpireTime(null);
        }
    }
}
