package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 记录使用曝光卡
 *
 * <AUTHOR>
 * @since 2025/03/07 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecordUseExposureCardPostHandler {

    private final RedisUtil redisUtil;

    @Async("threadPoolExecutor")
    public void handler(DirectPublishProcessBO processBO) {
        // 如果使用曝光卡，记录15分钟，用于找货大厅相似货源置顶判断
        if (PublishOptEnum.REPUBLISH.equals(processBO.getOptEnum())) {
            Long srcMsgId = processBO.getTransportMain().getSrcMsgId();
            redisUtil.set(RedisKeyConstant.EXPOSURE_CARD_VALID + srcMsgId, System.currentTimeMillis() + "", Duration.ofMinutes(15L));

            // 记录曝光卡使用次数
            redisUtil.increment(RedisKeyConstant.EXPOSURE_CARD_USE_NUM_CACHE + srcMsgId, 1L);
            redisUtil.expire(RedisKeyConstant.EXPOSURE_CARD_USE_NUM_CACHE + srcMsgId, Duration.ofDays(1L));
        }
        // if (PublishOptEnum.REPUBLISH.equals(processBO.getOptEnum())) {
        //     Long srcMsgId = processBO.getTransportMain().getSrcMsgId();
        //     redisUtil.set(RedisKeyConstant.EXPOSURE_CARD_VALID + srcMsgId, System.currentTimeMillis() + "", Duration.ofMinutes(15L));
        // }
    }
}
