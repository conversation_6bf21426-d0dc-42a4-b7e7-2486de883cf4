package com.teyuntong.goods.service.service.rpc.specialcar;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.specialcar.dto.SpecialCarAutoLabelDTO;
import com.teyuntong.goods.service.client.specialcar.dto.SpecialCarBILabelDTO;
import com.teyuntong.goods.service.client.specialcar.dto.SpecialCarDTO;
import com.teyuntong.goods.service.client.specialcar.service.SpecialCarRpcService;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.BasePoiDistinceDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarRouteDO;
import com.teyuntong.goods.service.service.biz.specialcar.service.BasePoiDistinceService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarRouteService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarService;
import com.teyuntong.goods.service.service.common.enums.CarHasLadderEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.TytSourceRemoteService;
import com.teyuntong.goods.service.service.remote.user.CarRemoteService;
import com.teyuntong.goods.service.service.remote.user.DriverRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.car.vo.CarRpcVO;
import com.teyuntong.user.service.client.car.vo.CarTailDetailRpcVO;
import com.teyuntong.user.service.client.driver.vo.InvoiceDriverRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/06/30 09:40
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class SpecialCarRpcServiceImpl implements SpecialCarRpcService {


    private final SpecialCarService specialCarService;

    private final BasePoiDistinceService basePoiDistinceService;

    private final SpecialCarRouteService specialCarRouteService;

    private final DriverRemoteService driverRemoteService;

    private final UserRemoteService userRemoteService;

    private final CarRemoteService carRemoteService;

    private final TytSourceRemoteService tytSourceRemoteService;


    @Override
    public boolean saveSpecialCar(SpecialCarDTO specialCarDTO) {
        InvoiceDriverRpcVO invoiceDriverVO = driverRemoteService.getInfoByDriverId(specialCarDTO.getDriverId());
        if(Objects.isNull(invoiceDriverVO) || !specialCarDTO.getUserId().equals(invoiceDriverVO.getUserId())){
            throw new BusinessException(GoodsErrorCode.INVOICE_DRIVER_NOT_EXIST);
        }
        Long id = specialCarDTO.getId();
        Long userId = specialCarDTO.getUserId();
        Long carId = specialCarDTO.getCarId();
        Long driverId = specialCarDTO.getDriverId();
        String route = specialCarDTO.getRoute();
        Long driverUserId = invoiceDriverVO.getDriverUserId();
        List<SpecialCarDO> specialCarDOList = specialCarService.getSpecialCarByUserId(userId, driverId, driverUserId);
        if(CollUtil.isNotEmpty(specialCarDOList)){
            throw new BusinessException(GoodsErrorCode.INVOICE_DRIVER_SPECIAL_EXIST);
        }

        CarRpcVO car = carRemoteService.getCarById(carId);
        if(Objects.isNull(car)){
            throw new BusinessException(GoodsErrorCode.CAR_NOT_EXIST);
        }
        if(!userId.equals(car.getUserId())){
            throw new BusinessException(GoodsErrorCode.USER_CAR_NOT_EXIST);
        }

        String length = "";
        String height = "";
        String carStyle = "";
        String carType = "";

        List<CarTailDetailRpcVO> carTailList = carRemoteService.getCarTailList(List.of(carId));
        if(CollUtil.isNotEmpty(carTailList)){
            CarTailDetailRpcVO tail = carTailList.get(0);
            length = getLength(tail.getLength());
            height =getHeight(tail.getLoadSurfaceHeight());
            //挂车样式
            carStyle =  getCarStyle(tail.getIsPureFlat());
            //挂车型号---
            carType = getCarType(car.getCarType());
        }

        UserRpcVO user = userRemoteService.getUser(userId);

        SpecialCarDO specialCarDO =SpecialCarDO.builder()
                .userId(car.getUserId())
                .tytCellPhone(user.getCellPhone())
                .name(invoiceDriverVO.getName())
                .driverId(invoiceDriverVO.getId())
                .driverUserId(invoiceDriverVO.getDriverUserId())
                .driverPhone(invoiceDriverVO.getPhone())
                .phone(invoiceDriverVO.getPhone())
                .drivingAbility(specialCarDTO.getDrivingAbility())
                .city(specialCarDTO.getCity())
                .carId(specialCarDTO.getCarId())
                .carType(carType)
                .length(length)
                .ladderType(CarHasLadderEnum.getValueByKey(car.getHasLadder()))
                .headCityNo(car.getHeadCity() + car.getHeadNo())
                .tailCityNo(car.getTailCity() + car.getTailNo())
                .tableHeight(height)
                .otherPureFlat(carStyle)
                .modifyTime(new Date())
                .build();

        BasePoiDistinceDO basePoiDistinceDO = basePoiDistinceService.getBasePoiDistinceDO(String.valueOf(userId));
        log.info("经分获取用户距离标记userId:{}，BasePoiDistince{}",userId, JSON.toJSONString(basePoiDistinceDO));
        if(Objects.nonNull(basePoiDistinceDO)){
            specialCarDO.setBiDistance(basePoiDistinceDO.getPoiCity()+"_"+basePoiDistinceDO.getPoiArea()+"_"+basePoiDistinceDO.getWarehouse()+"_"+basePoiDistinceDO.getDistinct());
        }


        if(Objects.isNull(id)){
            specialCarDO.setCreateName(user.getUserName());
            specialCarDO.setCreateTime(new Date());
            specialCarService.saveSpecialCar(specialCarDO);
        }else {
            SpecialCarDO specialCar = specialCarService.getSpecialCarById(id);
            specialCarDO.setId(id);
            specialCarDO.setUpdateName(user.getUserName());
            specialCarDO.setStatus(0);
            specialCarDO.setReason(null);
            specialCarDO.setCreateTime(specialCar.getCreateTime());
            specialCarDO.setCreateName(specialCar.getCreateName());
            specialCarService.updateSpecialCar(specialCarDO);
            specialCarRouteService.deleteBySpecialId(id);
        }
        List<SpecialCarRouteDO> specialCarRouteDOList = buildSpecialCarRouteDO(route, id);
        if(CollUtil.isNotEmpty(specialCarRouteDOList)){
            specialCarRouteService.batchInsert(specialCarRouteDOList);
        }
        return true;
    }

    @Override
    public void autoLabel(SpecialCarAutoLabelDTO specialCarAutoLabelDTO) {
        specialCarService.autoLabel(specialCarAutoLabelDTO);
    }

    @Override
    public void autoLabelDelete(SpecialCarAutoLabelDTO specialCarAutoLabelDTO) {
        specialCarService.autoLabelDelete(specialCarAutoLabelDTO);
    }

    @Override
    public void allLabelDelete() {
        specialCarService.allLabelDelete();
    }

    @Override
    public void biLabel(SpecialCarBILabelDTO specialCarAutoLabelDTO) {
        specialCarService.biLabel(specialCarAutoLabelDTO);
    }


    private  List<SpecialCarRouteDO> buildSpecialCarRouteDO(String routes, Long specialId){
        String[] fruits = routes.split(",");
        List<SpecialCarRouteDO> specialCarRouteDOList = new ArrayList<>();
        for (String fruit : fruits) {
            String[] parts = fruit.split("-");
            SpecialCarRouteDO route = new SpecialCarRouteDO();
            route.setStartCity(parts[0]);
            route.setDestCity(parts[1]);
            route.setSpecialId(specialId);
            route.setCreateTime(new Date());
            route.setModifyTime(new Date());
            specialCarRouteDOList.add(route);
        }
        return specialCarRouteDOList;
    }



    private String getLength(String tailLength){
        String length = "0";
        try{
            if(StringUtils.isNotEmpty(tailLength)){
                BigDecimal bigDecimal = new BigDecimal(tailLength);
                BigDecimal divide = bigDecimal.divide(new BigDecimal(1000));
                length = divide.toString();
            }
        }catch (Exception e){
            log.error("处理车辆长度失败:",e);
        }

        return length;
    }

    private String getHeight(String tailHeight){
        String height = "0";
        try{
            if(StringUtils.isNotEmpty(tailHeight)){
                BigDecimal bigDecimal = new BigDecimal(tailHeight);
                BigDecimal divide = bigDecimal.multiply(new BigDecimal(100));
                height = divide.toString();
            }
        }catch (Exception e){
            log.error("处理车辆高度失败:",e);
        }

        return height;
    }

    private String getCarStyle(Integer pureFlat){
        String carStyle = "";
        if(null != pureFlat){
            TytSourceVO tytSource = tytSourceRemoteService.getByGroupCodeAndValue("tail_car_style", pureFlat.toString());
            if(null != tytSource){
                carStyle = tytSource.getName();
            }
        }
        return carStyle;
    }

    private String getCarType(Integer carType){
        String type = "";
        if(null != carType){
            TytSourceVO tytSource = tytSourceRemoteService.getByGroupCodeAndValue("tail_car_type",carType.toString());
            if(null != tytSource){
                type = tytSource.getName();
            }
        }
        return type;
    }
}
