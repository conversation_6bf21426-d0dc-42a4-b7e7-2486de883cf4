package com.teyuntong.goods.service.service.common.mq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "mq-topic")
public class MessageTopicProperties {

    private String topic;

    private String tag;

    private String mbTopic;

    private String mbTag;

    /**
     * 履约中心topic
     */
    private String tradeCenterTopic;

    /**
     * 履约中心订单操作相关tag
     */
    private String tradeOrderTag;

    /**
     * 履约中心支付相关tag
     */
    private String tradePayTag;

}