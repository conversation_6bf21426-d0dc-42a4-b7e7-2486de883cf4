package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型：0-平台用户，1-签约用户，2-代调
 *
 * <AUTHOR>
 * @since 2025-07-05 14:23
 */
@Getter
@AllArgsConstructor
public enum UserTypeEnum {
    /**
     * 平台用户，专车发货时使用平台运费规则
     */
    PLAT_USER(0, "平台用户"),
    /**
     * 签约用户，专车签约合作商（非平台）下绑定的货主
     */
    SIGNED_USER(1, "签约用户"),
    /**
     * 代调用户，公司内部代调用户
     */
    DISPATCH_USER(2, "代调用户"),
    ;
    private Integer code;
    private String name;
}
