package com.teyuntong.goods.service.service.rpc.publish.direct;

import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.saver.DirectPublishTransportSaver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保存发货
 *
 * <AUTHOR>
 * @since 2025/02/18 13:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportDirectPublishSaver {

    private final DirectPublishTransportSaver directPublishTransportSaver;

    public void saveDirect(DirectPublishProcessBO processBO) {
        directPublishTransportSaver.saveDirect(processBO);
    }


}
