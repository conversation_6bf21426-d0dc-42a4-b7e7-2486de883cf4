package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static org.dromara.easyes.common.constants.BaseEsConstants.ONE;

/**
 * 运满满货源校验
 *
 * <AUTHOR>
 * @since 2025/02/21 13:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FixedTransportChecker {

    /**
     * 一口价货源校验
     *
     * @param publishBO
     */
    public void checkFixedTransport(PublishBO publishBO) {
        // 一口价货源校验
        if (!Objects.equals(publishBO.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
            return;
        }
        if (StringUtils.isBlank(publishBO.getPrice())) {
            throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "一口价货源运费不能为空");
        }
        if (publishBO.getInfoFee() == null) {
            throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "一口价货源订金不能为空");
        }
        if (publishBO.getShuntingQuantity() == null) {
            throw BusinessException.createException(GoodsErrorCode.SHUNTING_QUANTITY_CHECK.getCode(), "一口价货源调车数量不可为空");
        }
        if (publishBO.getShuntingQuantity() > ONE) {
            throw BusinessException.createException(GoodsErrorCode.SHUNTING_QUANTITY_CHECK.getCode(), "一口价货源调车数量不可大于1");

        }
    }

}
