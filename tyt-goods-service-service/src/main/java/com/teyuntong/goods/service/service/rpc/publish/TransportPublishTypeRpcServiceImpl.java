package com.teyuntong.goods.service.service.rpc.publish;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.client.invoice.vo.HbwjInvoiceTransportOverrunCheckReq;
import com.teyuntong.goods.service.client.publish.CarouselGoodsDTO;
import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.client.publish.dto.CalculatePriceDTO;
import com.teyuntong.goods.service.client.publish.dto.PublishGoodsTypeDTO;
import com.teyuntong.goods.service.client.publish.dto.PublishGoodsTypeLabelDTO;
import com.teyuntong.goods.service.client.publish.service.TransportPublishTypeRpcService;
import com.teyuntong.goods.service.client.publish.vo.CarouselGoodsVO;
import com.teyuntong.goods.service.client.publish.vo.PublishGoodsTypeResultVO;
import com.teyuntong.goods.service.client.transport.dto.CheckNextStepDTO;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportConfigLogDTO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceEnterpriseDO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceTransportPriceEnterpriseConfigService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCargoOwnerService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCooperativeService;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserLimitRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.builder.CalcSpecialGoodsPriceService;
import com.teyuntong.goods.service.service.rpc.publish.checker.*;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.enterprise.vo.InvoiceDominantVo;
import com.teyuntong.user.service.client.limit.vo.DepositBlockCheckResultRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 货源发布类型数据RPC服务实现
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TransportPublishTypeRpcServiceImpl implements TransportPublishTypeRpcService {

    private final CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;
    private final TransportMainRpcService transportMainRpcService;
    private final UserPermissionRemoteService userPermissionRemoteService;
    private final DispatchCargoOwnerService dispatchCargoOwnerService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final ThPriceService thPriceService;
    private final UserLimitRemoteService userLimitRemoteService;
    private final UserAuthChecker userAuthChecker;
    private final UserRemoteService userRemoteService;
    private final PublishPermissionChecker publishPermissionChecker;
    private final CarpoolTransportChecker carpoolTransportChecker;
    private final SpecialTransportChecker specialTransportChecker;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final InvoiceTransportPriceEnterpriseConfigService invoiceTransportPriceEnterpriseConfigService;
    private final InvoiceEnterpriseService invoiceEnterpriseService;
    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;
    private final InvoiceTransportChecker invoiceTransportChecker;
    private final OrdersRemoteService ordersRemoteService;
    private final UserPublishLimitChecker userPublishLimitChecker;
    private final TransportMainService transportMainService;
    private final SensitiveWordsChecker sensitiveWordsChecker;



    @Resource(name = "threadPoolExecutor")
    private ExecutorService threadPoolExecutor;

    @Override
    public PublishGoodsTypeResultVO getPublishGoodsType(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        dealPublishGoodsTypeParam(publishGoodsTypeDTO);
        //专车
        CompletableFuture<CalcSpecialGoodsPriceResultDTO> calculateCar = specialCarTask(publishGoodsTypeDTO, UseCarTypeEnum.FULL.getCode());
        // 拼车数据
        CompletableFuture<PublishGoodsTypeResultVO> carpoolCar = carpoolTask(publishGoodsTypeDTO);
        //优车
        CompletableFuture<CarryPriceVO> excellentCar = excellentCarTask(publishGoodsTypeDTO);
        // 获取用户权益
        CompletableFuture<String> userPermission = userPermissionTask(publishGoodsTypeDTO.getUserId());

        CompletableFuture.allOf(calculateCar, carpoolCar, excellentCar, userPermission).join();

        PublishGoodsTypeResultVO resultVO = new PublishGoodsTypeResultVO();
        Map<Integer, PublishGoodsTypeVO> publishTypeMap = new HashMap<>();
        CalcSpecialGoodsPriceResultDTO calculateCarResult = null;
        try {
            calculateCarResult = calculateCar.get();
            if (calculateCarResult != null) {
                processCalcSpecialResult(calculateCarResult, publishTypeMap);
            }
        } catch (Exception e) {
            log.error("getPublishGoodsType 获取整车专车数据异常", e);
        }

        String nextStepDesc = "";
        try {
            nextStepDesc = userPermission.get();
        } catch (Exception e) {
            log.error("getPublishGoodsType 获取用户权益异常", e);
        }
        try {
            PublishGoodsTypeResultVO carpoolCarResult = carpoolCar.get();
            if (carpoolCarResult != null) {
                processCarpoolCarResult(carpoolCarResult, nextStepDesc, resultVO);
            }
        } catch (Exception e) {
            log.error("getPublishGoodsType 获取拼车数据异常", e);
        }
        CarryPriceVO excellentCarResult = null;
        try {
            excellentCarResult = excellentCar.get();
            if (excellentCarResult != null) {
                processExcellentGoodsResult(excellentCarResult, publishTypeMap);
            }
        } catch (Exception e) {
            log.error("getPublishGoodsType 获取优车数据异常", e);
        }
        //处理普通和用户出价
        processNormalPublishGoodsTypeList(nextStepDesc, excellentCarResult, publishTypeMap);
        List<PublishGoodsTypeVO> publishTypeList = new ArrayList<>(publishTypeMap.values());
        publishTypeList.sort(Comparator.comparingInt(PublishGoodsTypeVO::getPublishGoodsType).reversed());
        if (!publishTypeMap.containsKey(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
            publishTypeList.add(cannotChooseSpecialGoodsType());
        }
        resultVO.setPublishTypeList(publishTypeList);
        //处理锚点
        processGoodsPoint(publishGoodsTypeDTO, resultVO, excellentCarResult, publishTypeMap);

        return resultVO;
    }

    private void dealPublishGoodsTypeParam(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        if (publishGoodsTypeDTO.getGoodsLength() != null){
            publishGoodsTypeDTO.setGoodsLength(publishGoodsTypeDTO.getGoodsLength().setScale(2, RoundingMode.HALF_UP));
        }
        if (publishGoodsTypeDTO.getGoodsWide() != null){
            publishGoodsTypeDTO.setGoodsWide(publishGoodsTypeDTO.getGoodsWide().setScale(2, RoundingMode.HALF_UP));
        }
        if (publishGoodsTypeDTO.getGoodsHigh() != null){
            publishGoodsTypeDTO.setGoodsHigh(publishGoodsTypeDTO.getGoodsHigh().setScale(2, RoundingMode.HALF_UP));
        }
        if (publishGoodsTypeDTO.getGoodsWeight() != null){
            publishGoodsTypeDTO.setGoodsWeight(publishGoodsTypeDTO.getGoodsWeight().setScale(2, RoundingMode.HALF_UP));
        }
    }

    // 专车任务
    private CompletableFuture<CalcSpecialGoodsPriceResultDTO> specialCarTask(PublishGoodsTypeDTO dto, int useCarType) {
        return CompletableFuture.supplyAsync(() -> {
            if (dto.getDistance() == null){
                return null;
            }
            CalculatePriceDTO priceDTO = TytBeanUtil.convertBean(dto, CalculatePriceDTO.class);
            if (priceDTO != null) {
                priceDTO.setDistanceKilometer(dto.getDistance());
                priceDTO.setWeight(dto.getGoodsWeight().toString());
                priceDTO.setUseCarType(useCarType);
                return calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
            }
            return null;
        }, threadPoolExecutor);
    }

    // 拼车任务
    private CompletableFuture<PublishGoodsTypeResultVO> carpoolTask(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        return CompletableFuture.supplyAsync(() -> {
            if (publishGoodsTypeDTO.getDistance() == null) {
                return null;
            }
            return transportMainRpcService.processCarpool(publishGoodsTypeDTO);
        }, threadPoolExecutor);
    }

    // 优车任务
    private CompletableFuture<CarryPriceVO> excellentCarTask(PublishGoodsTypeDTO dto) {
        return CompletableFuture.supplyAsync(() -> {
            TransportCarryReq carryDTO = TytBeanUtil.convertBean(dto, TransportCarryReq.class);
            if (carryDTO != null) {
                carryDTO.setGoodsLength(dto.getGoodsLength() == null ? "" : dto.getGoodsLength().toString());
                carryDTO.setGoodsWide(dto.getGoodsWide() == null ? "" : dto.getGoodsWide().toString());
                carryDTO.setGoodsHigh(dto.getGoodsHigh() == null ? "" : dto.getGoodsHigh().toString());
                return thPriceService.getThPrice(carryDTO);
            }
            return null;
        }, threadPoolExecutor);
    }

    // 用户权益任务
    private CompletableFuture<String> userPermissionTask(Long userId) {
        return CompletableFuture.supplyAsync(() -> getUserPublishPermissionDesc(userId), threadPoolExecutor);
    }

    private void processExcellentGoodsResult(CarryPriceVO excellentCarResult, Map<Integer, PublishGoodsTypeVO> publishTypeMap) {
        for (PublishGoodsTypeEnum type : PublishGoodsTypeEnum.values()) {
            BigDecimal minPrice = getPriceByType(excellentCarResult, type, true);
            BigDecimal maxPrice = getPriceByType(excellentCarResult, type, false);

            if (minPrice == null || maxPrice == null) {
                continue; // 数据缺失跳过
            }

            PublishGoodsTypeVO vo = newPublishGoodsType(type, PublishTypeEnum.FIXED.getCode(), "免费发货", minPrice, maxPrice);

            publishTypeMap.put(type.getCode(), vo);
        }
    }

    private BigDecimal getPriceByType(CarryPriceVO excellentCarResult, PublishGoodsTypeEnum type, boolean isMin) {
        return switch (type) {
            case SUPER_QUICK_EXCELLENT_GOODS -> isMin ? new BigDecimal(excellentCarResult.getFixPriceMax()) :
                    new BigDecimal(excellentCarResult.getThMaxPrice());
            case QUICK_EXCELLENT_GOODS -> isMin ? new BigDecimal(excellentCarResult.getFixPriceFast()) :
                    new BigDecimal(excellentCarResult.getFixPriceMax()).subtract(BigDecimal.ONE);
            case EXCELLENT_GOODS -> isMin ? new BigDecimal(excellentCarResult.getFixPriceMin()) :
                    new BigDecimal(excellentCarResult.getFixPriceFast()).subtract(BigDecimal.ONE);
            default -> null;
        };
    }

    private void processCalcSpecialResult(CalcSpecialGoodsPriceResultDTO result, Map<Integer, PublishGoodsTypeVO> publishTypeMap) {
        if (result != null && result.getPrice() != null && result.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            PublishGoodsTypeVO goodsType = newPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS, PublishTypeEnum.FIXED.getCode(), "免费发货", result.getLowerLimitPrice(), null);
            goodsType.setFixPriceFast(result.getPrice().compareTo(BigDecimal.ONE) == 0 ? null : result.getPrice());
            goodsType.setPerkPrice(result.getPerkPrice());
            publishTypeMap.put(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode(), goodsType);
        }
    }

    private PublishGoodsTypeVO cannotChooseSpecialGoodsType() {
        PublishGoodsTypeVO goodsType = newPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS, PublishTypeEnum.FIXED.getCode(), "", null, null);
        goodsType.setChooseType(0);
        return goodsType;
    }

    private void processCarpoolCarResult(PublishGoodsTypeResultVO carpoolCarResult, String nextStepDesc, PublishGoodsTypeResultVO resultVO) {
        BeanUtils.copyProperties(carpoolCarResult, resultVO);
        if (Boolean.FALSE.equals(resultVO.getShowCarpool())) {
            return;
        }
        boolean haveSpecialGoods = false;
        List<PublishGoodsTypeVO> carpoolPublishGoodsTypeList = resultVO.getCarpoolPublishTypeList();
        if (CollectionUtils.isEmpty(carpoolPublishGoodsTypeList)) {
            return;
        }
        for (PublishGoodsTypeVO publishGoodsTypeVO : carpoolPublishGoodsTypeList) {
            if (publishGoodsTypeVO.getPublishGoodsType().equals(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode())
                    || publishGoodsTypeVO.getPublishGoodsType().equals(PublishGoodsTypeEnum.NORMAL_GOODS.getCode())) {
                publishGoodsTypeVO.setNextStepDesc(nextStepDesc);
            }
            if (publishGoodsTypeVO.getPublishGoodsType().equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
                haveSpecialGoods = true;
            }
        }
        if (!haveSpecialGoods) {
            carpoolPublishGoodsTypeList.add(cannotChooseSpecialGoodsType());
        }
    }


    private String getUserPublishPermissionDesc(Long userId) {
        Integer publishCount = userPermissionRemoteService.getPublishPermissionCount(userId);
        if (null == publishCount || publishCount == 0) {
            return "";
        }
        if (publishCount == -1) {
            return "不限次数";
        }
        return "剩余" + publishCount + "次";
    }

    private void processNormalPublishGoodsTypeList(String nextStepDesc, CarryPriceVO excellentCarResult, Map<Integer, PublishGoodsTypeVO> publishTypeMap) {

        BigDecimal fixPriceMin = excellentCarResult == null ? null : new BigDecimal(excellentCarResult.getThMinPrice());
        BigDecimal fixPriceMax = excellentCarResult == null ? null : new BigDecimal(excellentCarResult.getThMaxPrice());
        PublishGoodsTypeVO userPriceGoods = newPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS,PublishTypeEnum.TELE.getCode(), nextStepDesc, fixPriceMin, fixPriceMax);
        publishTypeMap.put(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode(), userPriceGoods);
        PublishGoodsTypeVO normalGoods = newPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS,PublishTypeEnum.TELE.getCode(), nextStepDesc, null, null);
        publishTypeMap.put(PublishGoodsTypeEnum.NORMAL_GOODS.getCode(), normalGoods);
    }

    private void processGoodsPoint(PublishGoodsTypeDTO publishGoodsTypeDTO, PublishGoodsTypeResultVO resultVO, CarryPriceVO excellentCarResult, Map<Integer, PublishGoodsTypeVO> publishTypeMap) {
        /*
         *         i. 若货主在【专车货主管理】中，且签约合作商为非空且非平台，且出现专车卡片时，默认勾选专车
         *         ii. 除i外，在专车、极速优车、快速优车、特惠优车中默认选中推荐运价最低的
         *         iii. 若无上述选项， 则默认选中用户出价
         *         iv. 使用编辑再发布、再发一单时，
         *             1. 默认勾选原货源的选项，以最终货源为准，例如特惠优车发货，后续加价升级为极速优车，默认勾选极速优车
         *             2. 若上一次选项当前不存在，则按照i、ii、iii规则来
         *         v. 若用户在优车黑名单内，不默认勾选极速优车、快速优车、特惠优车，有专车勾专车，没专车勾用户出价
         */
        Long userId = publishGoodsTypeDTO.getUserId();
        DepositBlockCheckResultRpcVO depositBlock = userLimitRemoteService.getDepositBlock(userId, false);
        boolean excellentBlock = depositBlock != null && depositBlock.isBlock();
        resultVO.setExcellentBlock(excellentBlock);
        if (null != publishGoodsTypeDTO.getSrcMsgId()) {
            Long srcMsgId = publishGoodsTypeDTO.getSrcMsgId();
            TransportMainVO mainVO = transportMainRpcService.queryById(srcMsgId);
            if (mainVO != null && mainVO.getPublishGoodsType() != null) {
                TransportMainExtendVO extendVO = transportMainRpcService.getExtendBySrcMsgId(srcMsgId);
                if (extendVO != null && Objects.equals(extendVO.getUseCarType(), UseCarTypeEnum.PART.getCode())) {
                    resultVO.setCarpoolGoodsPoint(mainVO.getPublishGoodsType());
                } else {
                    resultVO.setGoodsPoint(getGoodsPointForGoods(mainVO, publishTypeMap, excellentCarResult, excellentBlock));
                }
            }
        }

        DispatchCargoOwnerDO owner = dispatchCargoOwnerService.selectSignedByUserId(userId);
        DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName("平台");
        boolean ownerPoint = Objects.nonNull(owner) && Objects.nonNull(owner.getCooperativeId()) && owner.getCooperativeId() != 0 && !owner.getCooperativeId().equals(cooperative.getId());

        if (null == resultVO.getGoodsPoint()) {
            resultVO.setGoodsPoint(processPublishGoodsTypeResult(resultVO.getPublishTypeList(), ownerPoint, excellentBlock, excellentCarResult));
        }
        if (null == resultVO.getCarpoolGoodsPoint()) {
            resultVO.setCarpoolGoodsPoint(processPublishGoodsTypeResult(resultVO.getCarpoolPublishTypeList(), ownerPoint, false, null));
        }

    }

    private Integer processPublishGoodsTypeResult(List<PublishGoodsTypeVO> publishGoodsTypeList, boolean ownerPoint, boolean excellentBlock, CarryPriceVO excellentCarResult) {
        if (CollectionUtils.isEmpty(publishGoodsTypeList)) {
            return null;
        }
        BigDecimal specialGoodsPrice = null;
        for (PublishGoodsTypeVO goodsTypeVO : publishGoodsTypeList) {
            if (goodsTypeVO.getPublishGoodsType().equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
                if (Objects.equals(goodsTypeVO.getChooseType(), YesOrNoEnum.YES.getId()) && ownerPoint) {
                    return PublishGoodsTypeEnum.SPECIAL_GOODS.getCode();
                }
                specialGoodsPrice = goodsTypeVO.getFixPriceFast();
            }
            if (null == excellentCarResult || excellentBlock) {
                if (goodsTypeVO.getPublishGoodsType().equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode()) && goodsTypeVO.getChooseType().equals(YesOrNoEnum.YES.getId())) {
                    return PublishGoodsTypeEnum.SPECIAL_GOODS.getCode();
                }
                if (goodsTypeVO.getPublishGoodsType().equals(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode())) {
                    return PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode();
                }
            }
        }

        if (null != excellentCarResult) {
            if (specialGoodsPrice != null && specialGoodsPrice.compareTo(new BigDecimal(excellentCarResult.getFixPriceMin())) <= 0) {
                return PublishGoodsTypeEnum.SPECIAL_GOODS.getCode();
            }
            return PublishGoodsTypeEnum.EXCELLENT_GOODS.getCode();
        }
        return null;
    }

    private Integer getGoodsPointForGoods(TransportMainVO mainVO, Map<Integer, PublishGoodsTypeVO> publishTypeMap, CarryPriceVO excellentCarResult, boolean excellentBlock) {
        if (!publishTypeMap.containsKey(mainVO.getPublishGoodsType())) {
            return null;
        }
        PublishGoodsTypeEnum typeEnum = PublishGoodsTypeEnum.getByCode(mainVO.getPublishGoodsType());
        if (typeEnum == null) {
            return null;
        }
        if (typeEnum.getOldCode() != 1) {
            return mainVO.getPublishGoodsType();
        }
        if (StringUtils.isBlank(mainVO.getPrice()) || excellentCarResult == null || excellentBlock) {
            return null;
        }
        int price = Integer.parseInt(mainVO.getPrice());
        if (price >= excellentCarResult.getFixPriceMax()) {
            return PublishGoodsTypeEnum.SUPER_QUICK_EXCELLENT_GOODS.getCode();
        }
        if (price >= excellentCarResult.getFixPriceFast()) {
            return PublishGoodsTypeEnum.QUICK_EXCELLENT_GOODS.getCode();
        }
        return PublishGoodsTypeEnum.EXCELLENT_GOODS.getCode();
    }

    private PublishGoodsTypeVO newPublishGoodsType(PublishGoodsTypeEnum typeEnum,
                                                   Integer publishType,
                                                   String nextStepDesc,
                                                   BigDecimal fixPriceMin,
                                                   BigDecimal fixPriceMax) {
        PublishGoodsTypeVO publishGoodsTypeVO = new PublishGoodsTypeVO();
        publishGoodsTypeVO.setPublishGoodsType(typeEnum.getCode());
        publishGoodsTypeVO.setPublishGoodsTypeName(typeEnum.getName());
        publishGoodsTypeVO.setPublishType(publishType);
        publishGoodsTypeVO.setFixPriceMin(fixPriceMin);
        publishGoodsTypeVO.setFixPriceMax(fixPriceMax);
        publishGoodsTypeVO.setNextStepDesc(nextStepDesc);
        publishGoodsTypeVO.setChooseType(1);
        return publishGoodsTypeVO;
    }

    @Override
    public Boolean checkPublishNextStep(CheckNextStepDTO checkNextStepDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        UserRpcVO user = userRemoteService.getUser(loginUser.getUserId());
        Long srcMsgId = checkNextStepDTO.getSrcMsgId();
        dealNextStepParam(checkNextStepDTO);
        TransportMainVO oldTransportMain = null;
        if (srcMsgId != null && srcMsgId > 0L) {
            oldTransportMain = transportMainRpcService.queryById(srcMsgId);
        }
        PublishBO publishBO = new PublishBO();
        int publishStyle = getPublishStyle(oldTransportMain);
        publishBO.setPublishStyle(publishStyle);

        //校验用户是否在黑名单
        userPublishLimitChecker.check(user);

        //实名认证校验
        checkUserAuth(publishBO, user);
        //权益校验
        if (checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.NORMAL_GOODS.getCode())
                || checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode())) {
            checkUserPermission(publishBO, user, checkNextStepDTO);
        }
        //拼车校验
        if (checkNextStepDTO.getUseCarType().equals(UseCarTypeEnum.PART.getCode())) {
            checkCarpool(publishBO, user, checkNextStepDTO);
        }

        //专车校验
        if (checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
            if (null != checkNextStepDTO.getCargoOwnerId()) {
                publishBO.setCargoOwnerId(checkNextStepDTO.getCargoOwnerId());
            }
            checkSpecialCar(publishBO, user, checkNextStepDTO);
        }

        //开票校验
        if (checkNextStepDTO.getInvoiceTransport() != null && checkNextStepDTO.getInvoiceTransport().equals(YesOrNoEnum.YES.getId())) {
            checkInvoice(checkNextStepDTO, user);
        }

        //优车、用户出价校验
        if (checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode())
                || PublishGoodsTypeEnum.isExcellentGoods(checkNextStepDTO.getPublishGoodsType())) {
            checkExcellentCar(checkNextStepDTO, user);
        }

        //集团敏感词校验
        if (checkNextStepDTO.getInvoiceTransport() != null && checkNextStepDTO.getInvoiceTransport().equals(YesOrNoEnum.YES.getId())) {
            sensitiveWordsChecker.checkSensitiveWords(user.getId(), checkNextStepDTO.getTaskContent(), checkNextStepDTO.getMachineRemark());
        }

        return true;
    }

    private void dealNextStepParam(CheckNextStepDTO checkNextStepDTO) {
        if (checkNextStepDTO.getGoodsLength() != null){
            checkNextStepDTO.setGoodsLength(checkNextStepDTO.getGoodsLength().setScale(2, RoundingMode.HALF_UP));
        }
        if (checkNextStepDTO.getGoodsWide() != null){
            checkNextStepDTO.setGoodsWide(checkNextStepDTO.getGoodsWide().setScale(2, RoundingMode.HALF_UP));
        }
        if (checkNextStepDTO.getGoodsHigh() != null){
            checkNextStepDTO.setGoodsHigh(checkNextStepDTO.getGoodsHigh().setScale(2, RoundingMode.HALF_UP));
        }
        if (checkNextStepDTO.getGoodsWeight() != null){
            checkNextStepDTO.setGoodsWeight(checkNextStepDTO.getGoodsWeight().setScale(2, RoundingMode.HALF_UP));
        }
    }


    @Override
    public Boolean checkPrice(CheckNextStepDTO checkNextStepDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        UserRpcVO user = userRemoteService.getUser(loginUser.getUserId());
        //专车校验
        if (checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
            PublishBO publishBO = new PublishBO();
            if (null != checkNextStepDTO.getCargoOwnerId()) {
                publishBO.setCargoOwnerId(checkNextStepDTO.getCargoOwnerId());
            }
            checkSpecialCar(publishBO, user, checkNextStepDTO);
        }

        //开票校验
        if (checkNextStepDTO.getInvoiceTransport() != null && checkNextStepDTO.getInvoiceTransport() == 1) {
            checkNextStepDTO.setCheckOverPrice(YesOrNoEnum.YES.getId());
            checkInvoicePrice(checkNextStepDTO, user);
        }

        //优车、用户出价校验
        if (checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode())
                || PublishGoodsTypeEnum.isExcellentGoods(checkNextStepDTO.getPublishGoodsType())) {
            checkExcellentCar(checkNextStepDTO, user);
        }

        return true;
    }

    @Override
    public List<CarouselGoodsVO> getCarouselGoods(CarouselGoodsDTO carouselGoodsDTO) {
        //a. 取今日发货数据
        // i. 货源范围：长宽高重有值且均不为1、0的货
        //ii. 优先级：与发布货源同路线（取到区级）>与发布货源同装货地（取到区级）>全部货源，上限30票
        //获取同路线货源
        if (StringUtils.isBlank(carouselGoodsDTO.getStartProvince()) || StringUtils.isBlank(carouselGoodsDTO.getStartCity()) || StringUtils.isBlank(carouselGoodsDTO.getStartArea())
                || StringUtils.isBlank(carouselGoodsDTO.getDestProvince()) || StringUtils.isBlank(carouselGoodsDTO.getDestCity()) || StringUtils.isBlank(carouselGoodsDTO.getDestArea())) {
            return new ArrayList<>();
        }
        List<CarouselGoodsVO> carouselGoods = transportMainService.getByGoodsPoint(carouselGoodsDTO);
        if (CollectionUtils.isNotEmpty(carouselGoods) && carouselGoods.size() >= 5) {
            dealNickName(carouselGoods);
            return carouselGoods;
        }
        //获取同装货地货源
        CarouselGoodsDTO sameStartGoodsDTO = new CarouselGoodsDTO();
        sameStartGoodsDTO.setStartProvince(carouselGoodsDTO.getStartProvince());
        sameStartGoodsDTO.setStartCity(carouselGoodsDTO.getStartCity());
        sameStartGoodsDTO.setStartArea(carouselGoodsDTO.getStartArea());
        List<CarouselGoodsVO> samStartGoods = transportMainService.getByGoodsPoint(sameStartGoodsDTO);
        carouselGoods.addAll(samStartGoods);
        if (CollectionUtils.isNotEmpty(carouselGoods) && carouselGoods.size() >= 5) {
            dealNickName(carouselGoods);
            return carouselGoods;
        }
        //获取全部货源
        List<CarouselGoodsVO> allGoods = transportMainService.getByGoodsPoint(new CarouselGoodsDTO());
        carouselGoods.addAll(allGoods);
        if (CollectionUtils.isNotEmpty(carouselGoods) && carouselGoods.size() >= 2) {
            dealNickName(carouselGoods);
            return carouselGoods;
        }
        return new ArrayList<>();
    }

    @Override
    public List<PublishGoodsTypeLabelVO> getPublishGoodsTypeLabel(PublishGoodsTypeLabelDTO publishGoodsTypeLabelDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        publishGoodsTypeLabelDTO.setUserId(loginUser.getUserId());
        BaseParamDTO getBaseParam = LoginHelper.getBaseParam();
        publishGoodsTypeLabelDTO.setClientVersion(getBaseParam.getClientVersion());
        List<PublishGoodsTypeLabelVO> publishGoodsTypeLabelVOList = new ArrayList<>();

        InvoiceTransportConfigLogDTO invoiceTransportConfigLog = null;
        if (Objects.equals(publishGoodsTypeLabelDTO.getInvoiceTransport(), InvoiceTransportEnum.YES.getCode()) && publishGoodsTypeLabelDTO.getUseCarType().equals(UseCarTypeEnum.FULL.getCode())){
            InvoiceEnterpriseDO enterpriseDO = invoiceEnterpriseService.getByCertigierUserId(publishGoodsTypeLabelDTO.getUserId());
            if (enterpriseDO != null){
                invoiceTransportConfigLog = invoiceTransportPriceEnterpriseConfigService.getLastInvoiceTransportEnterpriseConfig(enterpriseDO.getId(), publishGoodsTypeLabelDTO.getServiceProviderCode());
            }
        }

        List<Integer> publishGoodsTypeList = publishGoodsTypeLabelDTO.getPublishGoodsTypes();

        if (CollectionUtils.isEmpty(publishGoodsTypeList)){
            publishGoodsTypeList =  Arrays.stream(PublishGoodsTypeEnum.values())
                    .map(PublishGoodsTypeEnum::getCode)
                    .collect(Collectors.toList());
        }

        for (Integer publishGoodsType : publishGoodsTypeList) {
            PublishGoodsTypeLabelVO publishGoodsTypeLabelVO = new PublishGoodsTypeLabelVO();
            publishGoodsTypeLabelVO.setPublishGoodsType(publishGoodsType);
            if (invoiceTransportConfigLog != null){
                if (publishGoodsType == PublishGoodsTypeEnum.SPECIAL_GOODS.getCode() && Objects.equals(invoiceTransportConfigLog.getCanAssignCar(), AssignCarEnum.YES.getCode())){
                    publishGoodsTypeLabelVO.setTypeLabel("不可指派");
                }
                if(StringUtils.isNotBlank(publishGoodsTypeLabelDTO.getServiceProviderCode()) && Objects.equals(publishGoodsTypeLabelDTO.getServiceProviderCode(), InvoiceServiceProviderEnum.HBWJ.getCode())){
                    if (publishGoodsType == PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode() && Objects.equals(invoiceTransportConfigLog.getSegmentedPayments(), YesOrNoEnum.YES.getId())){
                        publishGoodsTypeLabelVO.setTypeLabel("可分段付");
                    }
                }
            }
            publishGoodsTypeLabelVOList.add(publishGoodsTypeLabelVO);
        }
        return publishGoodsTypeLabelVOList;
    }

    private void dealNickName(List<CarouselGoodsVO> carouselGoods) {
        carouselGoods.forEach(v -> {
            if (StringUtils.isBlank(v.getNickName())) {
                v.setNickName("王老板");
            } else {
                //如果昵称以先生或女士结尾，则截取第一个字+老板
                if (v.getNickName().endsWith("先生") || v.getNickName().endsWith("女士")) {
                    v.setNickName(v.getNickName().charAt(0) + "老板");
                } else {
                    v.setNickName("王老板");
                }
            }
        });
    }

    private void checkUserAuth(PublishBO publishBO, UserRpcVO user) {
        userAuthChecker.checkUserAuth(publishBO, user);
    }

    private void checkUserPermission(PublishBO publishBO, UserRpcVO user, CheckNextStepDTO checkNextStepDTO) {
        PublishProcessBO publishProcessBO = new PublishProcessBO();
        publishProcessBO.setUser(user);
        if (checkNextStepDTO.getPrice() != null) {
            publishBO.setPrice(checkNextStepDTO.getPrice().toString());
        }
        publishProcessBO.setPublishBO(publishBO);
        publishPermissionChecker.checkPublishPermission(publishProcessBO, false);
    }

    private void checkCarpool(PublishBO publishBO, UserRpcVO user, CheckNextStepDTO checkNextStepDTO) {
        publishBO.setStartCity(checkNextStepDTO.getStartCity());
        publishBO.setDestCity(checkNextStepDTO.getDestCity());
        publishBO.setDistance(checkNextStepDTO.getDistance());
        publishBO.setWeight(checkNextStepDTO.getGoodsWeight() == null ? null : checkNextStepDTO.getGoodsWeight().toString());
        publishBO.setInvoiceTransport(checkNextStepDTO.getInvoiceTransport());
        carpoolTransportChecker.check(publishBO, user);
    }

    private void checkSpecialCar(PublishBO publishBO, UserRpcVO user, CheckNextStepDTO checkNextStepDTO) {
        if (null == checkNextStepDTO.getPrice() || checkNextStepDTO.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusinessException.createException(GoodsErrorCode.PUBLISH_PRICE_IS_NULL);
        }
        if (null == checkNextStepDTO.getDistance()) {
            throw BusinessException.createException(GoodsErrorCode.INVOICE_DISTANCE_GETTING);
        }
        BaseParamDTO getBaseParam = LoginHelper.getBaseParam();

        PublishProcessBO publishProcessBO = new PublishProcessBO();
        CalculatePriceDTO priceDTO = TytBeanUtil.convertBean(checkNextStepDTO, CalculatePriceDTO.class);
        if (priceDTO != null) {
            priceDTO.setClientVersion(getBaseParam.getClientVersion());
            priceDTO.setUserId(user.getId());
            priceDTO.setDistanceKilometer(checkNextStepDTO.getDistanceKilometer() == null ? checkNextStepDTO.getDistance() : checkNextStepDTO.getDistanceKilometer());
            priceDTO.setWeight(checkNextStepDTO.getGoodsWeight().toString());
            priceDTO.setUseCarType(checkNextStepDTO.getUseCarType());
            CalcSpecialGoodsPriceResultDTO resultDTO = calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
            publishProcessBO.setSpecialGoodsPriceResult(resultDTO);
            if (resultDTO != null && (null == resultDTO.getPrice() || resultDTO.getPrice().compareTo(BigDecimal.ZERO) <= 0)) {
                throw BusinessException.createException(GoodsErrorCode.SPECIAL_CAR_ROUTE_NOT_SUPPORT.getCode(), resultDTO.getNotice());
            }

        }
        publishBO.setPerkPrice(checkNextStepDTO.getPerkPrice());
        publishBO.setPrice(checkNextStepDTO.getPrice().toPlainString());
        publishBO.setExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
        publishProcessBO.setPublishBO(publishBO);

        specialTransportChecker.specialCheck(publishProcessBO, user);
    }

    private Integer getPublishStyle(TransportMainVO oldTransportMain) {
        if (oldTransportMain == null) {
            return PublishStyleEnum.NEW_PUBLISH.getCode();
        }
        if (oldTransportMain.getStatus() == 4) {
            return PublishStyleEnum.NEW_PUBLISH.getCode();
        }
        if (oldTransportMain.getCtime().after(DateUtil.beginOfDay(new Date()))) {
            return PublishStyleEnum.TODAY_PUBLISH.getCode();
        }
        return PublishStyleEnum.HISTORY_PUBLISH.getCode();
    }

    private void checkInvoice(CheckNextStepDTO checkNextStepDTO, UserRpcVO user) {
        // 运距不能为空
        if (null == checkNextStepDTO.getDistance() || checkNextStepDTO.getDistance().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusinessException.createException(GoodsErrorCode.INVOICE_DISTANCE_GETTING);
        }
        if (StringUtils.isNotBlank(checkNextStepDTO.getServiceProviderCode()) && checkNextStepDTO.getServiceProviderCode().equals("HBWJ")) {
            String invoiceDistanceRule = tytConfigRemoteService.getStringValue("invoice_distance_rule", "20");
            if (checkNextStepDTO.getDistance().compareTo(new BigDecimal(invoiceDistanceRule)) < 0) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_DISTANCE_TOO_SHORT.getCode(),
                        String.format(GoodsErrorCode.INVOICE_DISTANCE_TOO_SHORT.getMsg(), invoiceDistanceRule));
            }
        }
        if (null == checkNextStepDTO.getGoodsLength() || null == checkNextStepDTO.getGoodsWide() || null == checkNextStepDTO.getGoodsHigh() || null == checkNextStepDTO.getGoodsWeight()) {
            throw BusinessException.createException(GoodsErrorCode.ERROR_NO_PARAM.getCode(), "专票长宽高不能为空");
        }
        // 1、 专车开票不支持翔和翎
        //2、 专车开票不支持甘肃网货
        if (checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode()) && null != checkNextStepDTO.getInvoiceSubjectId()) {
            String invoiceSujectData = tytConfigRemoteService.getStringValue("invoice_subject_data", "1,JCZY");
            String invoiceSujectDataXhl = tytConfigRemoteService.getStringValue("invoice_subject_data_xhl", "9,XHL");
            String[] split = invoiceSujectData.split(",");
            if (checkNextStepDTO.getInvoiceSubjectId().equals(Long.parseLong(split[0]))) {
                throw new BusinessException(GoodsErrorCode.INVOICE_SPECIAL_CAR_GANSU_SUBJECT_ERROR);
            }
            split = invoiceSujectDataXhl.split(",");
            if (checkNextStepDTO.getInvoiceSubjectId().equals(Long.parseLong(split[0]))) {
                throw new BusinessException(GoodsErrorCode.INVOICE_SPECIAL_CAR_XHL_SUBJECT_ERROR);
            }
        }
        InvoiceEnterpriseDO enterpriseDO = invoiceEnterpriseService.getByCertigierUserId(user.getId());
        // 校验开票企业
        checkInvoiceEnterprise(checkNextStepDTO, user, enterpriseDO);
        //校验网货风控
        checkInvoiceRiskConfig(checkNextStepDTO, enterpriseDO, user);
        //校验运费
        if (!checkNextStepDTO.getPublishGoodsType().equals(PublishGoodsTypeEnum.NORMAL_GOODS.getCode())) {
            checkInvoicePrice(checkNextStepDTO, user);
        }

    }

    private void checkExcellentCar(CheckNextStepDTO checkNextStepDTO, UserRpcVO user) {
        if (checkNextStepDTO.getPrice() == null || checkNextStepDTO.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusinessException.createException(GoodsErrorCode.PUBLISH_PRICE_IS_NULL);
        }
        TransportCarryReq carryDTO = TytBeanUtil.convertBean(checkNextStepDTO, TransportCarryReq.class);
        carryDTO.setExcellentGoods(YesOrNoEnum.NO.getId());
        if (PublishGoodsTypeEnum.isExcellentGoods(checkNextStepDTO.getPublishGoodsType())) {
            if (checkNextStepDTO.getCheckGoodsSize() != null && checkNextStepDTO.getCheckGoodsSize().equals(YesOrNoEnum.YES.getId())) {
                if (TransportUtil.isInvalidSize(checkNextStepDTO.getGoodsLength())
                        || TransportUtil.isInvalidSize(checkNextStepDTO.getGoodsWide())
                        || TransportUtil.isInvalidSize(checkNextStepDTO.getGoodsHigh())
                        || TransportUtil.isInvalidSize(checkNextStepDTO.getGoodsHigh())) {
                    throw BusinessException.createException(GoodsErrorCode.EXCELLENT_GOODS_SIZE_IS_NULL.getCode(), "货物尺寸为空或不合法，请重新填写");
                }
            }

            DepositBlockCheckResultRpcVO depositBlock = userLimitRemoteService.getDepositBlock(user.getId(), false);
            boolean excellentBlock = depositBlock != null && depositBlock.isBlock();
            if (excellentBlock) {
                throw BusinessException.createException(GoodsErrorCode.EXCELLENT_GOODS_BLOCK);
            }
            carryDTO.setExcellentGoods(YesOrNoEnum.YES.getId());
        }

        carryDTO.setUserId(user.getId());
        if (null != checkNextStepDTO.getGoodsLength()) {
            carryDTO.setGoodsLength(checkNextStepDTO.getGoodsLength().toPlainString());
        }
        if (null != checkNextStepDTO.getGoodsWide()) {
            carryDTO.setGoodsWide(checkNextStepDTO.getGoodsWide().toPlainString());
        }
        if (null != checkNextStepDTO.getGoodsHigh()) {
            carryDTO.setGoodsHigh(checkNextStepDTO.getGoodsHigh().toPlainString());
        }

        int thMinPrice = 100;
        int thMaxPrice = 999999;
        if (Objects.equals(checkNextStepDTO.getUseCarType(), UseCarTypeEnum.FULL.getCode())) {
            CarryPriceVO carryPriceVO = thPriceService.getThPrice(carryDTO);
            if (carryPriceVO != null) {
                thMaxPrice = carryPriceVO.getThMaxPrice() == null ? thMaxPrice : carryPriceVO.getThMaxPrice();
                if (PublishGoodsTypeEnum.isExcellentGoods(checkNextStepDTO.getPublishGoodsType())) {
                    thMinPrice = carryPriceVO.getFixPriceMin() == null ? thMinPrice : carryPriceVO.getFixPriceMin();
                } else {
                    thMinPrice = carryPriceVO.getThMinPrice() == null ? thMinPrice : carryPriceVO.getThMinPrice();
                }
            }
        }
        int priceInt = checkNextStepDTO.getPrice().intValue();
        if (priceInt < thMinPrice) {
            throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "您当前运费过低，将无法匹配到司机，请重新填价");
        }
        if (priceInt > thMaxPrice) {
            throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "您当前运费过高，可能存在填写错误，请重新填价");
        }

    }

    private void checkInvoicePrice(CheckNextStepDTO checkNextStepDTO, UserRpcVO user) {
        invoiceTransportChecker.checkPublishInvoiceTransportParam(user.getId(), checkNextStepDTO.getDistance().toString(), checkNextStepDTO.getPrice().toString(), checkNextStepDTO.getGoodsWeight().toString());
        if (StringUtils.isBlank(checkNextStepDTO.getServiceProviderCode())) {
            throw BusinessException.createException(CommonErrorCode.ERROR_PARAMETER_LACK.getCode(), "请选择开票服务商");
        }
        //吨公里运费
        if (checkNextStepDTO.getServiceProviderCode().equals(InvoiceServiceProviderEnum.HBWJ.getCode())
                && checkNextStepDTO.getCheckOverPrice() != null && checkNextStepDTO.getCheckOverPrice().equals(YesOrNoEnum.YES.getId())) {
            HbwjInvoiceTransportOverrunCheckReq hbwjInvoiceTransportOverrunCheckReq = new HbwjInvoiceTransportOverrunCheckReq(checkNextStepDTO.getGoodsWeight(), checkNextStepDTO.getDistance(), checkNextStepDTO.getPrice());
            Boolean overrunCheck = invoiceEnterpriseService.hbwjInvoiceTransportOverrunCheck(hbwjInvoiceTransportOverrunCheckReq);
            if (!overrunCheck) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_OVER_ERROR.getCode(), "吨公里运费高");
            }
        }
        //分段支付
        if (null != checkNextStepDTO.getCollectedPrice()) {
            invoiceTransportChecker.checkSegmentedPayments(checkNextStepDTO.getPrepaidPrice(), checkNextStepDTO.getCollectedPrice(), checkNextStepDTO.getReceiptPrice(), checkNextStepDTO.getPrice().toPlainString(), user.getId(), false, true, checkNextStepDTO.getServiceProviderCode());
        }
    }

    private void checkInvoiceEnterprise(CheckNextStepDTO checkNextStepDTO, UserRpcVO user, InvoiceEnterpriseDO enterpriseDO) {
        // 开票货源校验企业是否认证
        if (enterpriseDO == null) {
            throw BusinessException.createException(GoodsErrorCode.FAIL_ENTERPRISE_CERTIFICATION.getCode(), "未通过企业认证不可发布“专票”货源，如有需要，可联系客服获取帮助");
        }
        if (null == checkNextStepDTO.getInvoiceSubjectId() || StringUtils.isBlank(checkNextStepDTO.getServiceProviderCode())) {
            throw BusinessException.createException(GoodsErrorCode.INVOICE_SUBJECT_ID_IS_NULL.getCode(), "主体id为空或服务商code为空");
        }
        Long invoiceSubjectId = checkNextStepDTO.getInvoiceSubjectId();
        WebResult<List<InvoiceDominantVo>> invoiceDominantListWebResult = null;
        try {
            invoiceDominantListWebResult = thirdEnterpriseRemoteService.getInvoiceDominantList(user.getId());
        } catch (Exception e) {
            log.error("获取开票主体列表失败", e);
        }
        if (invoiceDominantListWebResult == null || !WebResult.success().getCode().equals(invoiceDominantListWebResult.getCode())) {
            throw BusinessException.createException(GoodsErrorCode.FAIL_ENTERPRISE_CERTIFICATION.getCode(), "无可用的开票主体");
        }
        List<InvoiceDominantVo> invoiceDominantList = invoiceDominantListWebResult.getData();
        if (CollectionUtils.isEmpty(invoiceDominantList)) {
            throw BusinessException.createException(GoodsErrorCode.FAIL_ENTERPRISE_CERTIFICATION.getCode(), "无可用的开票主体");
        }
        if (invoiceDominantList.stream().noneMatch(dominantVo -> invoiceSubjectId.equals(dominantVo.getDominantId()))) {
            throw BusinessException.createException(GoodsErrorCode.FAIL_ENTERPRISE_CERTIFICATION.getCode(), "开票主体不可用");
        }
    }

    private void checkInvoiceRiskConfig(CheckNextStepDTO checkNextStepDTO, InvoiceEnterpriseDO enterpriseDO, UserRpcVO user) {
        //开票长宽高限制
        InvoiceTransportConfigLogDTO config = invoiceTransportPriceEnterpriseConfigService.getLastInvoiceTransportEnterpriseConfig(enterpriseDO.getId(), checkNextStepDTO.getServiceProviderCode());
        if (config != null) {
            StringBuilder allDes = new StringBuilder();
            if ((null != config.getMaxLength() && checkNextStepDTO.getGoodsLength().compareTo(BigDecimal.valueOf(config.getMaxLength())) > 0)) {
                allDes.append("长上限为").append(config.getMaxLength()).append("米");
            }
            if ((null != config.getMaxWidth() && checkNextStepDTO.getGoodsWide().compareTo(BigDecimal.valueOf(config.getMaxWidth())) > 0)) {
                if (!allDes.isEmpty()) {
                    allDes.append("、");
                }
                allDes.append("宽上限为").append(config.getMaxWidth()).append("米");
            }
            if ((null != config.getMaxHeight() && checkNextStepDTO.getGoodsHigh().compareTo(BigDecimal.valueOf(config.getMaxHeight())) > 0)) {
                if (!allDes.isEmpty()) {
                    allDes.append("、");
                }
                allDes.append("高上限为").append(config.getMaxHeight()).append("米");
            }
            if ((null != config.getMaxTonnage() && checkNextStepDTO.getGoodsWeight().compareTo(BigDecimal.valueOf(config.getMaxTonnage())) > 0)) {
                if (!allDes.isEmpty()) {
                    allDes.append("、");
                }
                allDes.append("重量上限为").append(config.getMaxTonnage()).append("吨");
            }
            if (!allDes.isEmpty()) {
                throw BusinessException.createException(GoodsErrorCode.GOODS_INFO_OVER_LIMIT.getCode(), allDes.toString());
            }

            //在途运费
            Integer allNoFreezeOrdersPriceCount = ordersRemoteService.getAllNoFreezeOrdersPriceCount(user.getId());
            if (allNoFreezeOrdersPriceCount != null && allNoFreezeOrdersPriceCount >= 0 && config.getMaxTotalTransportPrice().compareTo(Double.parseDouble(String.valueOf(allNoFreezeOrdersPriceCount / 10000))) < 0) {
                throw BusinessException.createException(GoodsErrorCode.GOODS_INFO_OVER_LIMIT.getCode(), "待支付运费已达到" + config.getMaxTotalTransportPrice() + "万元，请先将已有运单运费结算完成，才可以发布专票货源");
            }
        }
    }


}
