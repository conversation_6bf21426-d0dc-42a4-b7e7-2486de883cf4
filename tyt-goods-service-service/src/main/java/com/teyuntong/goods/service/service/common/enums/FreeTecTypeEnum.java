package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免佣原因枚举
 *
 * <AUTHOR>
 * @since 2025-07-22 18:19
 */
@Getter
@AllArgsConstructor
public enum FreeTecTypeEnum {
    NEW_TRANSPORT_USER_FREE(1, "新货主首履免佣"),
    S_CITY_FREE(3, "S城市免佣"),
    EXCELLENT_GOODS_2_FREE(4, "优车2.0首单免佣"),
    OVER_TIME_FREE(5, "超时免佣")
    ;
    private Integer code;
    private String name;
}
