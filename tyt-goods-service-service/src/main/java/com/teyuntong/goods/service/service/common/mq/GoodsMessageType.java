package com.teyuntong.goods.service.service.common.mq;

import com.teyuntong.infra.common.rocketmq.message.MessageTypeBase;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/04 10:31
 */
@Data
public class GoodsMessageType implements MessageTypeBase {
    /**
     * 消息主题, 最长不超过255个字符; 由a-z, A-Z, 0-9, 以及中划线"-"和下划线"_"构成.
     */
    private final String topic;
    /**
     * 消息标签, 合法标识符, 尽量简短且见名知意
     */
    private final String tag;

}
