package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/06/04 13:36
 */
@Getter
public enum GoodsTransportLabelEnum {

    //好货类型 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
    GOOD_ONE(11,"好货1"),
    GOOD_TWO(12,"好货2"),
    GOOD_THREE(13,"好货3"),
    MIDDLE_ONE(21,"中货1"),
    MIDDLE_TWO(22,"中货2"),
    MIDDLE_THREE(23,"中货3"),
    BAD_ONE(31,"差货1"),
    BAD_TWO(32,"差货2");


    private final Integer code;
    private final String name;

    GoodsTransportLabelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 是否是好货
     */
    public static boolean isGood(Integer code) {
        return Objects.equals(GOOD_ONE.code, code) ||
                Objects.equals(GOOD_TWO.code, code) ||
                Objects.equals(GOOD_THREE.code, code);
    }

    /**
     * 是否是好货
     */
    public static boolean isMid(Integer code) {
        return Objects.equals(MIDDLE_ONE.code, code) ||
                Objects.equals(MIDDLE_TWO.code, code) ||
                Objects.equals(MIDDLE_THREE.code, code);
    }

    /**
     * 是否是好中货
     *
     * @param code
     * @return
     */
    public static boolean isGoodMiddleTransport(Integer code) {
        return Objects.equals(GOOD_ONE.code, code) ||
                Objects.equals(GOOD_TWO.code, code) ||
                Objects.equals(GOOD_THREE.code, code) ||
                Objects.equals(MIDDLE_ONE.code, code) ||
                Objects.equals(MIDDLE_TWO.code, code) ||
                Objects.equals(MIDDLE_THREE.code, code);
    }

    public static String getNameByCode(Integer code) {
        for (GoodsTransportLabelEnum value : GoodsTransportLabelEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }
}
