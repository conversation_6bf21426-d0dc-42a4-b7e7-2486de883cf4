package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.market.MarketingActivityRemoteService;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 加价校验
 *
 * <AUTHOR>
 * @since 2025/03/09 16:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityChecker {

    private final MarketingActivityRemoteService marketingActivityRemoteService;

    /**
     * 营销活动校验
     *
     * @param user
     */
    public void checkActivity(UserRpcVO user) {
        // 6510拉新裂变验证
        Boolean activityUserAuth;
        try {
            activityUserAuth = marketingActivityRemoteService.getActivityUserAuth(user.getId());
        } catch (Exception e) {
            log.error("调用营销活动校验异常", e);
            return;
        }
        if (activityUserAuth) {
            throw new BusinessException(GoodsErrorCode.PULL_NEW_USER_UNVERIFIED);
        }

    }
}
