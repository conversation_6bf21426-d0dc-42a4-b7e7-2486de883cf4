package com.teyuntong.goods.service.service.rpc.userempower;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.publish.dto.UserempowerSyncgoodsConsentDTO;
import com.teyuntong.goods.service.client.publish.dto.UserempowerSyncgoodsDTO;
import com.teyuntong.goods.service.client.publish.dto.UserempowerSyncgoodsRejectDTO;
import com.teyuntong.goods.service.client.publish.service.UserempowerSyncgoodsRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytSyncgoodsUserBlackDO;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytUserempowerSyncgoodsDO;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytSyncgoodsUserBlackMapper;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytSyncgoodsUserRejectMapper;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper.TytUserempowerSyncgoodsMapper;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 货源授权弹窗数据接口实现
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class UserempowerSyncgoodsRpcServiceImpl implements UserempowerSyncgoodsRpcService {

    private final TytUserempowerSyncgoodsMapper tytUserempowerSyncgoodsMapper;
    private final TytSyncgoodsUserBlackMapper tytSyncgoodsUserBlackMapper;
    private final TytSyncgoodsUserRejectMapper tytSyncgoodsUserRejectMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final TransportMainService transportMainService;

    /**
     * 同步货源授权弹窗数据接口
     * 如果用户在优车授权黑名单中，则直接不展示优车授权弹窗
     * 否则
     * 查询：select * from tyt_userempower_syncgoods where is_delete = 0 and user_id = #{userId} limit 1
     * 如果根据userId能查出来数据，并且授权状态为未授权，则要求弹窗，并且把查出来的数据返回
     * 如果没查出来数据，也要求弹窗，但是不返回数据
     *
     * @param userId 用户ID
     * @return 授权弹窗数据
     */
    @Override
    public UserempowerSyncgoodsDTO getSyncgoodsPopupData(Long userId, Long srcMsgId) {
        log.info("获取货源授权弹窗数据，userId: {}, srcMsgId:{}", userId, srcMsgId);

        if (srcMsgId != null) {
            TransportMainDO transportMainForId = transportMainService.getTransportMainForId(srcMsgId);
            if (transportMainForId != null && transportMainForId.getInvoiceTransport() != null && transportMainForId.getInvoiceTransport() == 1) {
                UserempowerSyncgoodsDTO result = new UserempowerSyncgoodsDTO();
                result.setNeedPopup(false);
                log.info("开票货源不展示授权同步YMM弹窗 srcMsgId:{}", srcMsgId);
                return result;
            }
        }

        UserempowerSyncgoodsDTO result = new UserempowerSyncgoodsDTO();
        result.setNeedPopup(true); // 默认需要弹窗

        if (Objects.isNull(userId)) {
            log.warn("获取货源授权弹窗数据，userId为空");
            return result;
        }

        // 检查当天是否已经展示过弹窗
        String redisKey = RedisKeyConstant.SYNCGOODS_AUTH_POPUP_SHOWED + userId;
        String showedValue = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(showedValue)) {
            // 当天已经展示过弹窗，不再展示
            result.setNeedPopup(false);
            log.info("获取货源授权弹窗数据成功，userId: {}, 当天已经展示过弹窗，不再展示", userId);
            return result;
        }

        // 检查用户是否在黑名单中
        TytSyncgoodsUserBlackDO blackUser = tytSyncgoodsUserBlackMapper.selectByUserId(userId);
        if (blackUser != null) {
            // 用户在黑名单中，不需要弹窗
            result.setNeedPopup(false);
            log.info("获取货源授权弹窗数据成功，userId: {}, 用户在黑名单中，不需要弹窗", userId);
            return result;
        }

        // 查询用户的授权记录
        TytUserempowerSyncgoodsDO syncgoods = tytUserempowerSyncgoodsMapper.selectByUserId(userId);

        // 如果有数据，并且有未授权的记录，则返回数据
        if (syncgoods != null) {
            // 检查是否有未授权的记录
            boolean hasUnauthorized = syncgoods.getStatus() != null && syncgoods.getStatus() == 1; // 1表示未授权

            if (hasUnauthorized) {
                // 转换为DTO
                UserempowerSyncgoodsDTO.SyncgoodsData data = new UserempowerSyncgoodsDTO.SyncgoodsData();
                BeanUtils.copyProperties(syncgoods, data);

                result.setSyncgoodsData(data);
                log.info("获取货源授权弹窗数据成功，userId: {}, 未授权, 授权数据内容: {}", userId, JSON.toJSONString(data));
            } else {
                // 已授权，不需要弹窗
                result.setNeedPopup(false);
                log.info("获取货源授权弹窗数据成功，userId: {}, 已授权，不需要弹窗", userId);
            }
        } else {
            // 没有数据，需要弹窗但不返回数据
            result.setSyncgoodsData(null);
            log.info("获取货源授权弹窗数据成功，userId: {}, 无授权记录，需要弹窗", userId);
        }

        if (result.getNeedPopup()) {
            //弹出弹窗，记录缓存
            stringRedisTemplate.opsForValue().set(redisKey, "1", Duration.ofDays(1));
        }

        return result;
    }

    /**
     * 同意货源授权接口
     * 如果用户在tyt_userempower_syncgoods授权表里数据不存在则创建数据，并且直接变成已授权
     * 如果用户授权记录存在，则修改状态为已授权
     *
     * @param consentDTO 同意授权请求参数
     * @return 是否成功
     */
    @Override
    public Boolean consentSyncgoodsAuth(UserempowerSyncgoodsConsentDTO consentDTO) {
        log.info("同意货源授权，请求参数: {}", JSON.toJSONString(consentDTO));

        if (Objects.isNull(consentDTO) || Objects.isNull(consentDTO.getUserId())) {
            log.warn("同意货源授权失败，参数不合法");
            return false;
        }

        //用户同意授权，清空所有拒绝授权记录
        tytSyncgoodsUserRejectMapper.cleanAllByUserId(consentDTO.getUserId());

        try {
            // 查询用户的授权记录
            TytUserempowerSyncgoodsDO syncgoods = tytUserempowerSyncgoodsMapper.selectByUserId(consentDTO.getUserId());

            if (syncgoods != null) {
                // 记录存在，更新状态为已授权
                int rows = tytUserempowerSyncgoodsMapper.updateAuthStatus(consentDTO.getUserId());

                // 货源诊断任务完成，重新计时
                diagnosisTaskCompleteReclocking(consentDTO);

                log.info("更新用户授权状态成功，userId: {}, 影响行数: {}", consentDTO.getUserId(), rows);
                return rows > 0;
            } else {
                // 记录不存在，创建一条已授权的记录
                int rows = tytUserempowerSyncgoodsMapper.insertAuthRecord(
                        consentDTO.getUserId(),
                        consentDTO.getGoodsUserName());

                // 货源诊断任务完成，重新计时
                diagnosisTaskCompleteReclocking(consentDTO);

                log.info("创建用户授权记录成功，userId: {}, 影响行数: {}", consentDTO.getUserId(), rows);
                return rows > 0;
            }
        } catch (Exception e) {
            log.error("同意货源授权异常，userId: {}", consentDTO.getUserId(), e);
            return false;
        }
    }

    /**
     * 货源诊断任务完成，重新计时
     *
     * @param consentDTO
     */
    private void diagnosisTaskCompleteReclocking(UserempowerSyncgoodsConsentDTO consentDTO) {
        if (Objects.isNull(consentDTO.getSrcMsgId())) {
            return;
        }
        long timeMillis = System.currentTimeMillis();
        stringRedisTemplate.opsForValue().set(RedisKeyConstant.DIAGNOSIS_TASK_COMPLETE_TIME_KEY + consentDTO.getSrcMsgId(), String.valueOf(timeMillis), 24L, TimeUnit.HOURS);
    }

    /**
     * 拒绝货源授权接口
     * 记录用户拒绝授权的次数，如果累计拒绝3次，则自动将用户加入黑名单
     * 黑名单用户将不再展示授权弹窗
     *
     * @param rejectDTO 拒绝授权请求参数
     * @return 是否成功
     */
    @Override
    public Boolean rejectSyncgoodsAuth(UserempowerSyncgoodsRejectDTO rejectDTO) {
        log.info("拒绝货源授权，请求参数: {}", JSON.toJSONString(rejectDTO));

        if (Objects.isNull(rejectDTO) || Objects.isNull(rejectDTO.getUserId())) {
            log.warn("拒绝货源授权失败，参数不合法");
            return false;
        }

        try {
            // 记录拒绝授权
            int rows = tytSyncgoodsUserRejectMapper.insertRejectRecord(rejectDTO.getUserId());
            if (rows <= 0) {
                log.error("记录拒绝授权失败，userId: {}", rejectDTO.getUserId());
                return false;
            }

            // 检查是否已经拒绝3次
            int rejectCount = tytSyncgoodsUserRejectMapper.countRejectByUserId(rejectDTO.getUserId());
            log.info("用户拒绝授权次数，userId: {}, rejectCount: {}", rejectDTO.getUserId(), rejectCount);

            // 如果拒绝3次，则加入黑名单
            if (rejectCount >= 3) {
                // 检查是否已经在黑名单中
                TytSyncgoodsUserBlackDO blackUser = tytSyncgoodsUserBlackMapper.selectByUserId(rejectDTO.getUserId());
                if (blackUser == null) {
                    // 加入黑名单
                    int blackRows = tytSyncgoodsUserBlackMapper.insertBlackRecord(
                            rejectDTO.getUserId(),
                            "拒绝授权3次",
                            "系统");
                    log.info("用户拒绝授权3次，已加入黑名单，userId: {}, 影响行数: {}", rejectDTO.getUserId(), blackRows);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("拒绝货源授权异常，userId: {}", rejectDTO.getUserId(), e);
            return false;
        }
    }
}
