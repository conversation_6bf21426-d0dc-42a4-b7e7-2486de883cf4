package com.teyuntong.goods.service.service.rpc.publish.builder;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.KeywordMatchesNewUnstandardDO;
import com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.MachineTypeBrandNewDO;
import com.teyuntong.goods.service.service.biz.goodsname.service.KeywordMatchesNewUnstandardService;
import com.teyuntong.goods.service.service.biz.goodsname.service.MachineTypeBrandNewService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.utils.IKAnalyzerUtils;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytSequenceRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 基础信息构建builder
 *
 * <AUTHOR>
 * @since 2025/02/21 15:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseTransportBuilder {

    private final TytSequenceRemoteService tytSequenceRemoteService;
    private final MachineTypeBrandNewService machineTypeBrandNewService;
    private final IKAnalyzerUtils ikAnalyzerUtils;
    private final KeywordMatchesNewUnstandardService keywordMatchesNewUnstandardService;
    private final ThPriceService thPriceService;

    public void build(DirectPublishProcessBO processBO) {
        TransportMainDO oldMain = processBO.getOldMain();
        TransportMainDO newMainDO = TransportPublishConverter.INSTANCE.copyMainDO(oldMain);
        processBO.setTransportMain(newMainDO);


        int publishTypeCode = processBO.getDirectPublishBO().getPublishGoodsTypeEnum().getCode();
        newMainDO.setPublishGoodsType(publishTypeCode);
        if (PublishGoodsTypeEnum.isExcellentGoods(publishTypeCode)) {
            newMainDO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
            newMainDO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
        } else if (PublishGoodsTypeEnum.isNormalGoods(publishTypeCode)) {
            newMainDO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            newMainDO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
        } else if (PublishGoodsTypeEnum.isSpecialGoods(publishTypeCode)) {
            newMainDO.setExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
            newMainDO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
        }

        Date now = new Date();
        if (processBO.getDirectPublishBO().isHistoryGoods()) {
            newMainDO.setId(null);
            newMainDO.setSrcMsgId(null);
            newMainDO.setSortType(TsSortTypeEnum.TOP.getCode());
            newMainDO.setTsOrderNo(tytSequenceRemoteService.getSequence("tyt_waybill_nbr"));
            newMainDO.setInfoStatus("0");
            newMainDO.setReleaseTime(now);
            newMainDO.setCtime(now);

            // 重新设置sourceType
            setSourceType(newMainDO);
        }

        BaseParamDTO baseParam = processBO.getBaseParam();
        newMainDO.setClientVersion(baseParam.getClientVersion());
        newMainDO.setPlatId(baseParam.getClientSign());

        newMainDO.setMtime(now);
        newMainDO.setPubTime(TimeUtil.formatTime(now));
        newMainDO.setPubDate(now);
        newMainDO.setStatus(1);
        newMainDO.setIsShow(1);

        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        newMainDO.setExcellentCardId(directPublishBO.getExcellCardId()); // 如果有优车发货卡，

        // 转电议，转一口价
        if (Objects.nonNull(directPublishBO.getPublishType())) {
            newMainDO.setPublishType(directPublishBO.getPublishType());
            if (directPublishBO.getPublishType().equals(PublishTypeEnum.FIXED.getCode())) {
                newMainDO.setShuntingQuantity(1);
            }
        }
        if (StringUtils.isNotBlank(directPublishBO.getPrice())) {
            newMainDO.setPrice(directPublishBO.getPrice());
        }
        // 兼容订金支付页如果price为空串时会报错
        if(StringUtils.isBlank(newMainDO.getPrice())){
            newMainDO.setPrice(null);
        }

        // 重新设置装卸货时间
        resetLoadingTime(processBO.getOptEnum(), newMainDO);

        // 计算重发次数
        setResendCount(processBO, newMainDO);

        // 补充新版标准化货物信息的参考值
//        addReferTransportNewInfo(newMainDO);

        // 更新货源信息
        updateGoodsInfo(processBO);

        // 必须先获取标准货物信息才能生成hashCode
        newMainDO.setHashCode(TransportUtil.getNewHashCode(newMainDO));

//        // 设置是否是优车2.0
//        if (Objects.equals(processBO.getDirectPublishBO().getGoodCarPriceTransport(), 1)) {
//            newMainDO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
//            newMainDO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
//        } else {
//            newMainDO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
//        }

        // 如果抽佣，使用最新的附加费和企业税率
        if (Objects.equals(newMainDO.getInvoiceTransport(), 1)) {
            DirectPublishBO extendBO = processBO.getDirectPublishBO();
            newMainDO.setAdditionalPrice(extendBO.getAdditionalPrice());
            newMainDO.setEnterpriseTaxRate(extendBO.getEnterpriseTaxRate());
        }
    }

    public void setSourceType(TransportMainDO newMainDO) {
        if (Objects.equals(SourceTypeEnum.YMM.getCode(), newMainDO.getSourceType())
                || Objects.equals(SourceTypeEnum.HONGXIN.getCode(), newMainDO.getSourceType())
                || Objects.equals(SourceTypeEnum.DISPATCH.getCode(), newMainDO.getSourceType())) {
            newMainDO.setSourceType(newMainDO.getSourceType());
        } else {
            newMainDO.setSourceType(SourceTypeEnum.NORMAL.getCode());
        }
    }


    /**
     * 计算重发次数
     */
    public void setResendCount(DirectPublishProcessBO directPublishProcessBO, TransportMainDO newMainDO) {
        int resendCount;
        if (directPublishProcessBO.getDirectPublishBO().isHistoryGoods()) {
            resendCount = 0;
        } else {
            resendCount = newMainDO.getResendCounts() == null ? 0 : newMainDO.getResendCounts();
            if (directPublishProcessBO.getDirectPublishBO().isTopFlag()) {
                resendCount = resendCount + 1;
            }
        }
        newMainDO.setResendCounts(resendCount);
        // 重发次数为0的不需要[].
        if (resendCount == 0) {
            newMainDO.setPcOldContent(newMainDO.getStartPoint().trim() + "---" + newMainDO.getDestPoint().trim() + " " + newMainDO.getTaskContent().trim());
        } else {
            newMainDO.setPcOldContent("[" + resendCount + "]." + newMainDO.getStartPoint().trim() + "---" + newMainDO.getDestPoint().trim() + " " + newMainDO.getTaskContent().trim());
        }
        newMainDO.setDisplayType("1");
        newMainDO.setIsDisplay(1);
    }

    /**
     * 补充新版标准化货物信息的参考值
     */
    public void addReferTransportNewInfo(TransportMainDO newMainDO) {
        // 未传时直接当非标货源
        if (newMainDO.getMatchItemId() == null) {
            newMainDO.setIsStandard(1);
            newMainDO.setMatchItemId(-1);
            return;
        }
        // 查询新版本标准化
        if (newMainDO.getMatchItemId() >= 0 && newMainDO.getMatchItemId() != 9999999) {
            MachineTypeBrandNewDO machineTypeBrandNew = machineTypeBrandNewService.getById(newMainDO.getMatchItemId().longValue());
            if (machineTypeBrandNew != null) {
                String brand = StringUtils.defaultIfBlank(machineTypeBrandNew.getBrand(), "");
                String goodTypeName = StringUtils.defaultIfBlank(machineTypeBrandNew.getSecondClass(), "");
                String topType = StringUtils.defaultIfBlank(machineTypeBrandNew.getTopType(), "");

                newMainDO.setBrand(brand);
                newMainDO.setGoodTypeName(goodTypeName);
                newMainDO.setType(topType);

                newMainDO.setReferLength(this.getReferNumber(newMainDO.getLength(), machineTypeBrandNew.getLength()));
                newMainDO.setReferWidth(this.getReferNumber(newMainDO.getWide(), machineTypeBrandNew.getWidth()));
                newMainDO.setReferHeight(this.getReferNumber(newMainDO.getHigh(), machineTypeBrandNew.getHeight()));
                newMainDO.setReferWeight(this.getReferNumber(newMainDO.getWeight(), machineTypeBrandNew.getWeight()));
                newMainDO.setIsStandard(0);
                return;
            }
        }
        // 如果不是标准货源，看看能否从非标货源转化
        List<String> keywords = ikAnalyzerUtils.analyze(newMainDO.getTaskContent());
        if(CollUtil.isNotEmpty(keywords)){
           List<KeywordMatchesNewUnstandardDO> unStandardDOList = keywordMatchesNewUnstandardService.selectByKeywords(keywords);
           if(CollUtil.isNotEmpty(unStandardDOList)){
               KeywordMatchesNewUnstandardDO unStandardDO = CollUtil.getFirst(unStandardDOList);
               // 作为非标转化回来的标识
               newMainDO.setMatchItemId(9999999);
               newMainDO.setIsStandard(0);

               newMainDO.setBrand(unStandardDO.getBrand());
               newMainDO.setGoodTypeName(unStandardDO.getSecondClass());
               newMainDO.setType(unStandardDO.getSecondType());

               newMainDO.setReferLength(TransportUtil.getReferNumber(unStandardDO.getLength(),newMainDO.getLength()));
               newMainDO.setReferWidth(TransportUtil.getReferNumber(unStandardDO.getWidth(), newMainDO.getWide()));
               newMainDO.setReferHeight(TransportUtil.getReferNumber(unStandardDO.getHeight(), newMainDO.getHigh()));
               newMainDO.setReferWeight(TransportUtil.getReferNumber(unStandardDO.getWeight(), newMainDO.getWeight()));
                return;
           }
        }
        // 都不是，则设置为非标货源标识
        newMainDO.setIsStandard(1);
        newMainDO.setMatchItemId(-1);
    }

    /**
     * 处理自动重发装卸货时间过期的问题：
     * 如果是直接发布，若原货源装卸货时间已过，则变更为“今明两日，随到随装”发布
     * 如果是重新上架，若原货源装卸货时间已过，则变更为“当天全天”发布
     */
    private void resetLoadingTime(PublishOptEnum optEnum, TransportMainDO newMainDO) {
        if (newMainDO.getLoadingTime() != null && new Date().compareTo(newMainDO.getLoadingTime()) > 0
                || newMainDO.getUnloadTime() != null && new Date().compareTo(newMainDO.getUnloadTime()) > 0) {
            if (PublishOptEnum.AUTO_RESEND.equals(optEnum)) {
                newMainDO.setLoadingTime(null);
                newMainDO.setBeginLoadingTime(null);
                newMainDO.setUnloadTime(null);
                newMainDO.setBeginUnloadTime(null);
            } else if (PublishOptEnum.RERELEASE.equals(optEnum)) {
                newMainDO.setLoadingTime(null);
                newMainDO.setBeginLoadingTime(null);
                newMainDO.setUnloadTime(null);
                newMainDO.setBeginUnloadTime(null);
            }
        }
    }

    /**
     * 处理参考参数
     */
    private Integer getReferNumber(String reqNumberStr, BigDecimal numberDec) {
        Integer referNumber = null;
        if (NumberUtils.isParsable(reqNumberStr)) {
            BigDecimal bigDecimal = new BigDecimal(reqNumberStr);
            BigDecimal multDec = new BigDecimal(100);

            referNumber = bigDecimal.multiply(multDec).intValue();
        } else {
            if (numberDec != null) {
                referNumber = numberDec.movePointRight(2).intValue();
            }
        }
        return referNumber;
    }

    /**
     * 更新货源信息
     */
    private void updateGoodsInfo(DirectPublishProcessBO directPublishProcessBO) {
        DirectPublishBO directPublishBO = directPublishProcessBO.getDirectPublishBO();
        if (directPublishBO.getUpdateGoodsInfoDTO() != null && directPublishProcessBO.getTransportMain() != null) {

            TransportMainDO newMainDO = directPublishProcessBO.getTransportMain();
            UpdateGoodsInfoDTO updateGoodsInfoDTO = directPublishBO.getUpdateGoodsInfoDTO();

            // 更新长宽高重
            if (updateGoodsInfoDTO.getLength() != null) {
                newMainDO.setLength(updateGoodsInfoDTO.getLength());
            }
            if (updateGoodsInfoDTO.getWide() != null) {
                newMainDO.setWide(updateGoodsInfoDTO.getWide());
            }
            if (updateGoodsInfoDTO.getHigh() != null) {
                newMainDO.setHigh(updateGoodsInfoDTO.getHigh());
            }
            if (updateGoodsInfoDTO.getWeight() != null) {
                newMainDO.setWeight(updateGoodsInfoDTO.getWeight());
            }

            // 更新目的地
            if (updateGoodsInfoDTO.getDestLatitude() != null && updateGoodsInfoDTO.getDestLongitude() != null) {
                newMainDO.setDestPoint(updateGoodsInfoDTO.getDestPoint());
                newMainDO.setDestProvinc(updateGoodsInfoDTO.getDestProvinc());
                newMainDO.setDestCity(updateGoodsInfoDTO.getDestCity());
                newMainDO.setDestArea(updateGoodsInfoDTO.getDestArea());
                newMainDO.setDestDetailAdd(updateGoodsInfoDTO.getDestDetailAdd());

                newMainDO.setDestLatitude(updateGoodsInfoDTO.getDestLatitude());
                newMainDO.setDestLongitude(updateGoodsInfoDTO.getDestLongitude());
                newMainDO.setDestCoord(updateGoodsInfoDTO.getDestCoordX() + "," + updateGoodsInfoDTO.getDestCoordY());
                newMainDO.setDestCoordX(updateGoodsInfoDTO.getDestCoordX());
                newMainDO.setDestCoordY(updateGoodsInfoDTO.getDestCoordY());

                if (updateGoodsInfoDTO.getDistance() != null) {
                    newMainDO.setDistance(updateGoodsInfoDTO.getDistance());
                    Integer clientSign = directPublishProcessBO.getBaseParam().getClientSign();
                    if (Objects.equals(clientSign, ClientSignEnum.ANDROID_GOODS.getCode())) {
                        newMainDO.setAndroidDistance(updateGoodsInfoDTO.getDistance());
                    } else if (Objects.equals(clientSign, ClientSignEnum.IOS_GOODS.getCode())) {
                        newMainDO.setIosDistance(updateGoodsInfoDTO.getDistance());
                    }
                }
            }
        }
    }

}
