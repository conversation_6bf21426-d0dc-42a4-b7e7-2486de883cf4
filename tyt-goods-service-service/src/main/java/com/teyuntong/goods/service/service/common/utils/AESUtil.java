
package com.teyuntong.goods.service.service.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Component
@Slf4j
public class AESUtil {
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";    //"算法/模式/补码方式"

    private static final String defaultCharset = "UTF-8";
    private static final String KEY_AES = "AES";
    private static final String KEY_MD5 = "MD5";
    private static MessageDigest md5Digest;
    public static String KEY;
    @Value("${picc.insurance.key}")
    private String keyInjector;
    static {
        try {
            md5Digest = MessageDigest.getInstance(KEY_MD5);
        } catch (NoSuchAlgorithmException e) {

        }
    }

    @PostConstruct
    public void init(){
        KEY = this.keyInjector;
    }


    //���ܷ���ʹ��ʾ����
//     public static void main(String[] args) throws Exception {
//         String key = "TYT#test";
// //        String data = "{\"system\":\"S10000038\",\"interface\":\"100052\",\"mode\":\"\"}";
//         String data = "\t{\n" +
//                 "\t\t\"head\": {\n" +
//                 "\t\t\t\"transactionNo\": \"1543217903\",\n" +
//                 "\t\t\t\"operator\": \"S10000038\",\n" +
//                 "\t\t\t\"timeStamp\": \"2018-11-26 07:38:23\"\n" +
//                 "\n" +
//                 "\t\t},\n" +
//                 "\t\t\"body\": {\n" +
//                 "\t\t\t\"applicant\": {\n" +
//                 "\t\t\t\t\"cardNo\": \"110101199102062324\",\n" +
//                 "            \t\"cardType\": \"01\",\n" +
//                 "            \t\"name\": \"满十八\",\n" +
//                 "            \t\"birthday\": \"\",\n" +
//                 "            \t\"sex\": \"\",\n" +
//                 "            \t\"mobile\": \"\"\n" +
//                 "\t\t\t},\n" +
//                 "\t\t\t\"insurants\": [\n" +
//                 "\t\t\t\t{\n" +
//                 "\t\t\t\t\t\"cardNo\": \"110101199102062324\",\n" +
//                 "\t\t\t\t\t\"cardType\": \"01\",\n" +
//                 "\t                \"insurerKey\": \"1543217903#tyt\",\n" +
//                 "\t                \"name\": \"满十八\",\n" +
//                 "\t                \"relation\": \"0\",\n" +
//                 "\t                \"birthday\": \"\",\n" +
//                 "\t                \"sex\": \"\",\n" +
//                 "\t                \"amount\": \"10000\",\n" +
//                 "\t\t\t\t\t\"premium\": \"100\"\n" +
//                 "\n" +
//                 "\t            }\n" +
//                 "\t\t\t],\n" +
//                 "\t\t\t\"orderInfo\": {\n" +
//                 "\t\t\t\t\"startTime\": \"2018-11-27 15:38:23\",\n" +
//                 "            \t\"orderNo\": \"1543217903\",\n" +
//                 "            \t\"productNo\": \"ABX10000084\",\n" +
//                 "            \t\"channelId\": \"CH10000342\",\n" +
//                 "            \t\"sumAmount\": \"10000\",\n" +
//                 "\t\t\t\t\"sumPremium\": \"100\"\n" +
//                 "\n" +
//                 "\t\t\t},\n" +
//                 "\t\t\t\"targets\": {\n" +
//                 "            \t\"packQty\": \"1台\",\n" +
//                 "            \t\"goodsName\":\"挖机\",\n" +
//                 "\t            \"goodsTypeNo\":\"630602\",\n" +
//                 "            \t\"transportType\": \"公路\",\n" +
//                 "            \t\"transport\": \"汽车\",\n" +
//                 "            \t\"fromLoc\": \"北京\",\n" +
//                 "            \t\"toLoc\": \"天津\",\n" +
//                 "            \t\"departureDate\": \"2018-11-27:15\",\n" +
//                 "            \t\"ratio\": \"30\"\n" +
//                 "\t\t\t},\n" +
//                 "\t\t\t\"extendInfo\": {\n" +
//                 " \t\t\t\t\"callbackURL\": \"http://www.teyuntong.com\"\n" +
//                 "\t\t\t}\n" +
//                 "\t\t}\n" +
//                 "\n" +
//                 "\t}";
//         String encrypted = AESUtil.encrypt(data, key);
// //        encrypted = "hVSfkX7VVUcgDXwWe+4z8uK3DTQfzdwISsrmodPwKt4Q1MEq61qD4WDvWgddTU1TqBsAlpm96KL6rmg56mv6wrdO0kbyHxURLsZ3rcmK4z9Sbw4S6y/O9m/dGg3UPDUvcaaTZSVyapvD3jkfKNeqQ9m0UT4rSVQpWGyFCv4hT8Anu93f2aBjdRaVLpXfZj2VATATCxQq7vFoEcaSVocFYSz3CPtja7SzT8WIbdFaWl2wsxIAk4dlvIwUU6IvzXre";
//         String decrypted = AESUtil.decrypt(encrypted, key);
//         System.out.println("密文\n" + encrypted);
//         System.out.println("明文:\n" + decrypted);
//
//         String encryptedResult = "hVSfkX7VVUcgDXwWe+4z8uK3DTQfzdwISsrmodPwKt4Q1MEq61qD4WDvWgddTU1TqBsAlpm96KL6rmg56mv6wt58m4C3oM+09VxFjJSjzmCrNIkna0cYw41IR/twkjMOcaaTZSVyapvD3jkfKNeqQ9m0UT4rSVQpWGyFCv4hT8Anu93f2aBjdRaVLpXfZj2VATATCxQq7vFoEcaSVocFYSz3CPtja7SzT8WIbdFaWl11tmDaHeo7Ctl7YSc0zpXX";
//         encryptedResult = "hVSfkX7VVUcgDXwWe+4z8ik3aNr+SiqKtOnl3UyPLZ2Wy+tJb6k90/Y6KjmiiWLEzJUDA3uWTqg3oInAHuWXUtw1dSgOGH9peUt9ZMEML7ctOm9ho5MMY/vOad5yzrmG3AVSTC92leE3ENg1zcjBhwOsh5oUFZr3veUeRGVW7Cnv+t6Onp+Mo1ht/S239VYmGcWRIfRm4tYyE7+6OScmMLIXIj8aWdV/IywajoHVHRF9MWxyHjAhUmwmbYI4abTnDEkEX0EOm2nEXiVPFDc3Eg==";
//         System.out.printf("返回:" + AESUtil.decrypt(encryptedResult, key));
//
//         String content = "特运通服务端加密字符串";
//         logger.info("加密content："+content);
//         String keyAes = "a3+@$@$!~!#2224441";
//         System.out.println(key.length());
//         logger.info("加密key："+keyAes);
//         String enResult = enCode(content, keyAes);
//         logger.info("加密result："+enResult);
//         String deResult = deCode(enResult, keyAes);
//         logger.info("解密result："+deResult);
//     }

    /**
     * ����
     *
     * @param data ��Ҫ���ܵ�����
     * @param key ��������
     * @return
     */
    public static String encrypt(String data, String key) {
        return doAES(data, key, Cipher.ENCRYPT_MODE);
    }
    /**
     * ����
     *
     * @param data ����������
     * @param key ������Կ
     * @return
     */
    public static String decrypt(String data, String key) {
        return doAES(data, key, Cipher.DECRYPT_MODE);
    }


    /**
     * �ӽ���
     *
     * @param data
     * @param key
     * @param mode
     * @return
     */
    private static String doAES(String data, String key, int mode) {
        try {
            boolean encrypt = mode == Cipher.ENCRYPT_MODE;
            byte[] content;
            if (encrypt) {
                content = data.getBytes(defaultCharset);
            } else {
                content = Base64.decodeBase64(data.getBytes());
            }
            SecretKeySpec keySpec = new SecretKeySpec(md5Digest.digest(key.getBytes(defaultCharset))
                    , KEY_AES);
            Cipher cipher = Cipher.getInstance(KEY_AES);// ����������
            cipher.init(mode, keySpec);// ��ʼ��
            byte[] result = cipher.doFinal(content);
            if (encrypt) {
                return new String(Base64.encodeBase64(result));
            } else {
                return new String(result, defaultCharset);
            }
        } catch (Exception e) {
        }
        return null;
    }

    public static String doB64encode(String data){
        return Base64.encodeBase64String(data.getBytes());
    }



    /*****************************************************
     * AES加密
     * @param content 加密内容
     * @param key 加密密码，由字母或数字组成
    此方法使用AES-128-ECB加密模式，key需要为16位
    加密解密key必须相同，如：abcd1234abcd1234
     * @return 加密密文
     ****************************************************/

    public static String enCode(String content, String key) {
        if (key == null || "".equals(key)) {
            log.info("key为空！");
            return null;
        }
        if (key.length() != 16) {
            //logger.info("key长度不是16位！");
            return null;
        }
        try {
            byte[] raw = key.getBytes();  //获得密码的字节数组
            SecretKeySpec skey = new SecretKeySpec(raw, "AES"); //根据密码生成AES密钥
            Cipher cipher = Cipher.getInstance(ALGORITHM);  //根据指定算法ALGORITHM自成密码器
            cipher.init(Cipher.ENCRYPT_MODE, skey); //初始化密码器，第一个参数为加密(ENCRYPT_MODE)或者解密(DECRYPT_MODE)操作，第二个参数为生成的AES密钥
            byte [] byte_content = content.getBytes("utf-8"); //获取加密内容的字节数组(设置为utf-8)不然内容中如果有中文和英文混合中文就会解密为乱码
            byte [] encode_content = cipher.doFinal(byte_content); //密码器加密数据
            return org.apache.commons.net.util.Base64.encodeBase64String(encode_content); //将加密后的数据转换为字符串返回
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /*****************************************************
     * AES解密
     * @param content 加密密文
     * @param key 加密密码,由字母或数字组成
    此方法使用AES-128-ECB加密模式，key需要为16位
    加密解密key必须相同
     * @return 解密明文
     ****************************************************/

    public static String deCode(String content, String key) {
        if (key == null || "".equals(key)) {
            log.info("key为空！");
            return null;
        }
        if (key.length() != 16) {
            log.info("key长度不是16位！");
            return null;
        }
        try {
            byte[] raw = key.getBytes();  //获得密码的字节数组
            SecretKeySpec skey = new SecretKeySpec(raw, "AES"); //根据密码生成AES密钥
            Cipher cipher = Cipher.getInstance(ALGORITHM);  //根据指定算法ALGORITHM自成密码器
            cipher.init(Cipher.DECRYPT_MODE, skey); //初始化密码器，第一个参数为加密(ENCRYPT_MODE)或者解密(DECRYPT_MODE)操作，第二个参数为生成的AES密钥
            byte [] encode_content = org.apache.commons.net.util.Base64.decodeBase64(content); //把密文字符串转回密文字节数组
            byte [] byte_content = cipher.doFinal(encode_content); //密码器解密数据
            return new String(byte_content,"utf-8"); //将解密后的数据转换为字符串返回
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}

