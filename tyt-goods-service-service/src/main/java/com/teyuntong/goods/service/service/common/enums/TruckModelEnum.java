package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 货车导航默认货车类型枚举，按货物吨重匹配车型
 *
 * <AUTHOR>
 * @since 2025/07/21 16:08
 */
@AllArgsConstructor
@Getter
public enum TruckModelEnum {

    TRUCK_1(3, "3吨板（轻型清障）", "冀PN9651", "9.34", "6", "4.2", "2.55", "3", 2),
    TRUCK_2(5, "5吨板（轻型清障）", "冀PN9651", "11.34", "8", "6.5", "2.55", "3", 2),
    TRUCK_3(8, "8吨板（轻型清障）", "冀PN9651", "13.34", "10", "8.5", "2.55", "3", 2),
    TRUCK_4(12, "6.8（两轴）", "冀PN9651", "19", "12", "6.8", "2.6", "3", 2),
    TRUCK_5(15, "8.6（三轴）", "冀PN9651", "23", "15", "8.6", "2.6", "3", 3),
    TRUCK_6(18, "9.6（三轴）", "冀PN9651", "26", "18", "9.6", "2.6", "3", 3),
    TRUCK_7(23, "四桥（车头2轴）", "冀PN9651", "32", "26.5", "11", "3", "3.2", 4),
    TRUCK_8(29, "五桥（车头3轴）", "冀PN9651", "42", "36.5", "12", "3", "3.2", 5),
    TRUCK_9(32, "大六桥", "冀PN9651", "52", "46.5", "13", "3", "3.2", 6),
    TRUCK_10(43, "六桥板（办证）", "冀PN9651", "52", "46.5", "13", "3", "3.2", 6),
    TRUCK_11(52, "七桥板（办证）", "冀PN9651", "60", "52", "13", "3", "3.2", 7),
    TRUCK_12(63, "三线六轴（办证）", "冀PN9651", "76.1", "68", "13", "3", "3.2", 9),
    TRUCK_13(80, "四线板（办证）", "冀PN9651", "94", "82", "13", "3", "3.2", 11),
    TRUCK_14(92, "五线板（办证）", "冀PN9651", "112", "98", "13", "3", "3.2", 13),

    ;

    // private final int[] goodsWeightRange; // 货吨重区间，左闭右开，如 [0,3)
    private final int weightTo; // 货吨重上限，不包含
    private final String model; // 车型
    private final String plateNumber; // 车牌号
    private final String weight; // 货车总重
    private final String load; // 核定载重
    private final String length; // 车长
    private final String width; // 车宽
    private final String height; // 车高
    private final int axleCount; // 轴数

    /**
     * 根据货重获取车型
     */
    public static TruckModelEnum getByWeight(BigDecimal weight) {
        // 如果货重为空或0，返回null
        if (weight == null || weight.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        for (TruckModelEnum truckModelEnum : values()) {
            if (weight.doubleValue() < truckModelEnum.weightTo) {
                return truckModelEnum;
            }
        }
        return TRUCK_14; // 92吨以上按照80-92档位计算
    }

    /**
     * 专车运距车型匹配逻辑:
     * => 0-10吨，按照平台车型运距匹配规则匹配运费
     * => 10-32吨，按照大六桥车型运距匹配运费
     * => 32吨以上，按照平台运距规则匹配运费
     * 工单ID:SSRS-3652
     */
    public static TruckModelEnum matchSpecialCar(BigDecimal weight) {
        // 如果货重为空或0，返回null
        if (weight == null || weight.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        double v = weight.doubleValue();
        if (10 <= v && v <= 32) {
            return TRUCK_9;
        }
        return getByWeight(weight);
    }

}
