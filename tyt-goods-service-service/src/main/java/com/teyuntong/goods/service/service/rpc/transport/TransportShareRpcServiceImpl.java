package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.dto.TransportShareDTO;
import com.teyuntong.goods.service.client.transport.service.TransportShareRpcService;
import com.teyuntong.goods.service.service.biz.transport.service.ShareRecService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 货源分享相关
 *
 * <AUTHOR>
 * @since 2025-06-19 15:00
 */
@Component
@Slf4j
public class TransportShareRpcServiceImpl implements TransportShareRpcService {

    @Resource
    private ShareRecService shareRecService;

    @Override
    public Boolean saveShareRecord(TransportShareDTO dto) {
        if (Objects.isNull(dto.getUserId()) || StringUtils.isAnyBlank(dto.getShareTarget(), dto.getShareLink())) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        return shareRecService.saveShareRecord(dto);
    }
}
