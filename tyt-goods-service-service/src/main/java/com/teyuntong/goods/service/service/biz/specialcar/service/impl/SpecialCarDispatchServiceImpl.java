package com.teyuntong.goods.service.service.biz.specialcar.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.service.biz.record.mybatis.entity.GoodsAddressLevelRecordDO;
import com.teyuntong.goods.service.service.biz.record.service.GoodsAddressLevelRecordService;
import com.teyuntong.goods.service.service.biz.specialcar.dto.AssignOrderUser;
import com.teyuntong.goods.service.service.biz.specialcar.dto.AssignableCarFilterDTO;
import com.teyuntong.goods.service.service.biz.specialcar.dto.AutoAssignOrderDTO;
import com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarDTO;
import com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarInfoDTO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarInfoGpscheatDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarAutoDispatchConfigDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarDispatchDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarDispatchDetailDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SigningCarInfoGpscheatMapper;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarDispatchMapper;
import com.teyuntong.goods.service.service.biz.specialcar.service.SigningCarService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarAutoDispatchConfigService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarDispatchDetailService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarDispatchService;
import com.teyuntong.goods.service.service.biz.specialcar.service.TransportDispatchCarTypeService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCompanyDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCargoOwnerService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCompanyService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCooperativeService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.AssignCarEnum;
import com.teyuntong.goods.service.service.common.enums.DistancePreferenceEnum;
import com.teyuntong.goods.service.service.common.enums.DriverDrivingEnum;
import com.teyuntong.goods.service.service.common.enums.DriverTagEnum;
import com.teyuntong.goods.service.service.common.enums.DriverVerifyEnum;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PublishUserTypeEnum;
import com.teyuntong.goods.service.service.common.utils.AddressCuttingUtil;
import com.teyuntong.goods.service.service.common.utils.ListUtil;
import com.teyuntong.goods.service.service.remote.basic.MapDictRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.outer.DistanceRemoteService;
import com.teyuntong.goods.service.service.remote.trace.CarLocationRemoteService;
import com.teyuntong.goods.service.service.remote.user.CarRemoteService;
import com.teyuntong.infra.basic.resource.client.tytcity.dto.MapDictDto;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.MapDictVo;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import com.teyuntong.user.service.client.car.vo.CarRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import com.teyuntong.user.trace.client.location.vo.CurrentLocationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 专车派单表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpecialCarDispatchServiceImpl implements SpecialCarDispatchService {
    private final DispatchCargoOwnerService dispatchCargoOwnerService;
    private final SpecialCarAutoDispatchConfigService specialCarAutoDispatchConfigService;
    private final DispatchCompanyService dispatchCompanyService;
    private final CarRemoteService carRemoteService;
    private final SpecialCarDispatchMapper specialCarDispatchMapper;
    private final SpecialCarDispatchDetailService specialCarDispatchDetailService;
    private final TransportDispatchCarTypeService transportDispatchCarTypeService;
    private final SigningCarService signingCarService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final OrdersRemoteService ordersRemoteService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final GoodsAddressLevelRecordService goodsAddressLevelRecordService;
    private final TransportMainService transportMainService;
    private final StringRedisTemplate stringRedisTemplate;
    private final CarLocationRemoteService carLocationRemoteService;
    private final DistanceRemoteService distanceRemoteService;
    private final MapDictRemoteService mapDictRemoteService;
    private final SigningCarInfoGpscheatMapper signingCarInfoGpscheatMapper;
    /**
     * 货主测试账号
     */
    private static final String CARGO_OWNER_TEST_ACCOUNT = "cargo_owner_test_user_ids";
    /**
     * 距离阈值
     */
    private static final String DISTANCE_THRESHOLD = "70";
    /**
     * 司机匹配数量阈值
     */
    private static final int DRIVER_MATCH_THRESHOLD = 20;
    /**
     * 距离查询开关：0-本地距离表，1-腾讯货车导航
     */
    private static final String SPECIAL_CAR_DISTANCE_SWITCH = "special_car_distance_switch";
    /**
     * 缓存车辆当前位置信息
     */
    public static final String CACHE_CAR_CURRENT_LOCATION = "goods:service:car:current:location:";

    //清障车类型
    public static final String CLEARANCE_VEHICLE_TYPE = "清障车";

    /**
     * 执行专车自动派单
     *
     * @param main
     * @param user
     * @param distanceLimit,非专车自动指派（好差货指派）传入，其他情况可传null
     */
    @Override
    public void doDispatch(TransportMainDO main, UserRpcVO user, Integer distanceLimit) {
        try {
            AssignableCarFilterDTO filterDTO = getAssignableCarFilterDTO(main, user, distanceLimit);
            // 路线是否在专车派单配置中
            SpecialCarAutoDispatchConfigDO dispatchConfig = null;
            if (Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterDTO.getExcellentGoods())) {
                dispatchConfig = specialCarAutoDispatchConfigService
                        .selectDispatchConfigByRoute(filterDTO.getStartCity(), filterDTO.getDestCity());
            }
            List<SigningCarDTO> cars = this.getAssignableCars(filterDTO, dispatchConfig);
            log.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, 匹配到的签约车辆:{}", filterDTO.getSrcMsgId(),
                    user.getId(), JSONObject.toJSONString(cars));
            // 对满足条件的司机进行排序
            List<SigningCarDTO> carUsers = sortCars(main, user, filterDTO, cars);

            if (Objects.nonNull(dispatchConfig) && Objects.nonNull(dispatchConfig.getMaxDispatchNum()) &&
                    CollectionUtils.isNotEmpty(cars) && Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterDTO.getExcellentGoods())) {
                carUsers = carUsers.stream().limit(dispatchConfig.getMaxDispatchNum()).collect(Collectors.toList());
            }

            AutoAssignOrderDTO assignOrderDTO = getAutoAssignOrderDTO(main, user, carUsers, dispatchConfig);
            log.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, mq信息:{}", filterDTO.getSrcMsgId(), user.getId(),
                    JSONObject.toJSONString(assignOrderDTO));

            // 保存到派单记录表
            saveSpecialCarDispatch(assignOrderDTO);
        } catch (Exception e) {
            log.error("专车发货自动派单异常，货源ID:{}, 发货用户ID:{}", main.getId(), user.getId(), e);
        }
        // 自动将货主添加到专车货主管理
        dispatchCargoOwnerService.autoAddCargoOwner(main.getId(), user);
    }

    /**
     * 查询满足派单条件的司机
     *
     * @param filterDTO
     * @param dispatchConfig
     * @return
     */
    private List<SigningCarDTO> getAssignableCars(AssignableCarFilterDTO filterDTO, SpecialCarAutoDispatchConfigDO dispatchConfig) {
        log.info("专车发货自动派单，获取满足条件的司机列表参数:{}", JSON.toJSONString(filterDTO));
        if (StringUtils.isEmpty(filterDTO.getStartCity()) && StringUtils.isEmpty(filterDTO.getDestCity())) {
            return Collections.emptyList();
        }
        // 在配置中的测试账号，不走司机圈选逻辑
        String cargoOwnerTestAccount = tytConfigRemoteService.getStringValue(CARGO_OWNER_TEST_ACCOUNT, "0");
        if (StringUtils.isNotBlank(cargoOwnerTestAccount)) {
            List<String> cargoOwnerTestIds = Arrays.asList(cargoOwnerTestAccount.split(","));
            if (CollectionUtils.isNotEmpty(cargoOwnerTestIds) && cargoOwnerTestIds.contains(filterDTO.getUserId().toString())) {
                log.info("专车发货自动派单，在配置中的测试货主账号不走司机圈选，货主ID:{}", filterDTO.getUserId());
                return Collections.emptyList();
            }
        }

        List<SigningCarDTO> list = new ArrayList<>();
        // 筛选满足派单条件的司机：路线 > 城市 > 省份 > 全国
        List<SigningCarInfoDTO> assignableCars = filterCarsByCondition(filterDTO, dispatchConfig);
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars.forEach(e -> {
                SigningCarDTO dto = new SigningCarDTO();
                dto.setId(e.getCarInfoId());
                dto.setLocation(e.getLocation());
                dto.setDistance(e.getDistance());
                dto.setUserId(e.getUserId());
                dto.setPhone(e.getPhone());
                dto.setDriverId(e.getDriverId());
                dto.setDriverUserId(e.getDriverUserId());
                dto.setFavorableComment(e.getFavorableComment());
                dto.setReceivingOrders(e.getReceivingOrders());
                dto.setHeadCityNo(e.getHeadCityNo());
                dto.setTailCityNo(e.getTailCityNo());
                dto.setCarType(e.getCarType());
                dto.setDriverTag(e.getDriverTag());
                list.add(dto);
            });
        }
        return list;
    }

    /**
     * 根据条件筛选满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoDTO> filterCarsByCondition(AssignableCarFilterDTO filterBean,
                                                          SpecialCarAutoDispatchConfigDO dispatchConfig) {
        String distanceThreshold = DISTANCE_THRESHOLD;
        if (!Objects.equals(filterBean.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) &&
                Objects.nonNull(filterBean.getDistanceLimit())) {
            distanceThreshold = String.valueOf(filterBean.getDistanceLimit());
        }

        int maxDispatchNum = DRIVER_MATCH_THRESHOLD;
        if (Objects.nonNull(dispatchConfig)) {
            distanceThreshold = String.valueOf(dispatchConfig.getDistanceLimit());
            maxDispatchNum = dispatchConfig.getMaxDispatchNum();
        }

        Integer distanceSwitch = tytConfigRemoteService.getIntValue(SPECIAL_CAR_DISTANCE_SWITCH, 1);

        // 根据路线筛选司机，获取距离
        List<SigningCarInfoDTO> assignableCars = filterByRoute(filterBean, distanceThreshold, dispatchConfig, distanceSwitch);

        if (assignableCars.size() < maxDispatchNum) {
            // 路线匹配距离小于70公里的车辆小于20个，根据城市再筛选司机
            filterByCity(filterBean, distanceThreshold, assignableCars, dispatchConfig, distanceSwitch);
        }

        if (assignableCars.size() < maxDispatchNum) {
            // 路线和城市匹配距离小于70公里的车辆小于20个，根据省份再筛选司机
            filterByProvince(filterBean, distanceThreshold, assignableCars, dispatchConfig, distanceSwitch);
        }

        // if (assignableCars.size() < maxDispatchNum) {
        //     // 路线和城市和省份筛选后的司机数量还是小于20，需要筛选城市配置为全国的司机
        //     filterByCountry(filterBean, distanceThreshold, assignableCars, dispatchConfig, distanceSwitch);
        // }
        return assignableCars;
    }

    /**
     * 根据路线筛选司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param dispatchConfig
     * @return
     */
    private List<SigningCarInfoDTO> filterByRoute(AssignableCarFilterDTO filterBean,
                                                  String distanceThreshold,
                                                  SpecialCarAutoDispatchConfigDO dispatchConfig,
                                                  Integer distanceSwitch) {
        List<SigningCarInfoDTO> assignableCars = getAssignableCarsByRoute(filterBean);

        assignableCars = filterByDispatchConfig(dispatchConfig, assignableCars, filterBean);

        getLocationAndDistance(filterBean, assignableCars, distanceSwitch);
        assignableCars = assignableCars.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，根据路线筛选司机，货源ID：{}, 出发地:{}，目的地:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartCity(), filterBean.getDestCity(), distanceThreshold, assignableCars.size(), JSON.toJSONString(assignableCars));
        return assignableCars;
    }

    /**
     * 根据城市再筛选司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param assignableCars
     * @param dispatchConfig
     */
    private void filterByCity(AssignableCarFilterDTO filterBean,
                              String distanceThreshold,
                              List<SigningCarInfoDTO> assignableCars,
                              SpecialCarAutoDispatchConfigDO dispatchConfig,
                              Integer distanceSwitch) {
        List<SigningCarInfoDTO> assignableCarsByCity = getAssignableCarsByCity(filterBean);
        // 去除路线筛选已经筛选出来的司机
        assignableCarsByCity.removeAll(assignableCars);

        assignableCarsByCity = filterByDispatchConfig(dispatchConfig, assignableCarsByCity, filterBean);

        getLocationAndDistance(filterBean, assignableCarsByCity, distanceSwitch);
        assignableCarsByCity = assignableCarsByCity.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，根据城市筛选司机，货源ID：{}, 出发地市:{}，目的地市:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartCity(), filterBean.getDestCity(), distanceThreshold, assignableCarsByCity.size(),
                JSON.toJSONString(assignableCarsByCity));
        assignableCars.addAll(assignableCarsByCity);
    }

    /**
     * 根据省份再筛选司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param assignableCars
     * @param dispatchConfig
     */
    private void filterByProvince(AssignableCarFilterDTO filterBean,
                                  String distanceThreshold,
                                  List<SigningCarInfoDTO> assignableCars,
                                  SpecialCarAutoDispatchConfigDO dispatchConfig,
                                  Integer distanceSwitch) {
        List<SigningCarInfoDTO> assignableCarsByProvince = getAssignableCarsByProvince(filterBean);
        // 去除路线筛选已经筛选出来的司机
        assignableCarsByProvince.removeAll(assignableCars);

        assignableCarsByProvince = filterByDispatchConfig(dispatchConfig, assignableCarsByProvince, filterBean);

        getLocationAndDistance(filterBean, assignableCarsByProvince, distanceSwitch);
        assignableCarsByProvince = assignableCarsByProvince.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，根据省份筛选司机，货源ID：{}, 出发地省:{}，目的地省:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartProvince(), filterBean.getDestProvince(), distanceThreshold, assignableCarsByProvince.size(),
                JSON.toJSONString(assignableCarsByProvince));
        assignableCars.addAll(assignableCarsByProvince);
    }

    /**
     * 筛选城市配置为全国的司机
     *
     * @param filterBean
     * @param distanceThreshold
     * @param assignableCars
     * @param dispatchConfig
     */
    private void filterByCountry(AssignableCarFilterDTO filterBean,
                                 String distanceThreshold,
                                 List<SigningCarInfoDTO> assignableCars,
                                 SpecialCarAutoDispatchConfigDO dispatchConfig,
                                 Integer distanceSwitch) {
        List<SigningCarInfoDTO> assignableCarsByCountry = getAssignableCarsByCountry(filterBean);
        // 去除路线和城市已经筛选出来的司机
        assignableCarsByCountry.removeAll(assignableCars);

        assignableCarsByCountry = filterByDispatchConfig(dispatchConfig, assignableCarsByCountry, filterBean);

        getLocationAndDistance(filterBean, assignableCarsByCountry, distanceSwitch);
        assignableCarsByCountry = assignableCarsByCountry.stream()
                .filter(e -> Objects.nonNull(e.getDistance()) && e.getDistance().compareTo(new BigDecimal(distanceThreshold)) <= 0)
                .collect(Collectors.toList());
        log.info("专车发货自动派单，筛选城市配置有全国的司机，货源ID：{}, 出发地:{}，目的地:{}，司机距离限制:{}, 数量：{}, 结果:{}", filterBean.getSrcMsgId(),
                filterBean.getStartCity(), filterBean.getDestCity(), distanceThreshold, assignableCarsByCountry.size(),
                JSON.toJSONString(assignableCarsByCountry));
        assignableCars.addAll(assignableCarsByCountry);
    }

    /**
     * 通过路线筛选获取满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoDTO> getAssignableCarsByRoute(AssignableCarFilterDTO filterBean) {
        List<SigningCarInfoDTO> assignableCars = signingCarService.getAssignableCarsByRoute(filterBean.getStartCity(), filterBean.getDestCity());
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars = filterAssignableCars(filterBean, assignableCars);
        } else {
            assignableCars = new ArrayList<>();
        }
        return assignableCars;
    }

    /**
     * 通过城市筛选获取满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoDTO> getAssignableCarsByCity(AssignableCarFilterDTO filterBean) {
        String startCity = filterBean.getStartCity();
        String destCity = filterBean.getDestCity();
        List<String> cities = new ArrayList<>();
        cities.add(startCity);
        cities.add(destCity);

        // tyt_signing_car_info_city表中city存值存在：北京市  北京。需要处理一下把这两种都查出来
        if (startCity.endsWith("市")) {
            cities.add(startCity.replace("市", ""));
        }
        if (destCity.endsWith("市")) {
            cities.add(destCity.replace("市", ""));
        }

        List<SigningCarInfoDTO> assignableCars = signingCarService.getAssignableCarsByCity(cities);
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars = filterAssignableCars(filterBean, assignableCars);
        } else {
            assignableCars = new ArrayList<>();
        }
        return assignableCars;
    }

    /**
     * 根据省份筛选司机
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoDTO> getAssignableCarsByProvince(AssignableCarFilterDTO filterBean) {
        List<String> provinces = new ArrayList<>();
        provinces.add(filterBean.getStartProvince());
        provinces.add(filterBean.getDestProvince());

        List<SigningCarInfoDTO> assignableCars = signingCarService.getAssignableCarsByProvince(provinces);
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            assignableCars = filterAssignableCars(filterBean, assignableCars);
        } else {
            assignableCars = new ArrayList<>();
        }
        return assignableCars;
    }

    /**
     * 筛选城市配置为全国的满足派单条件的车辆
     *
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoDTO> getAssignableCarsByCountry(AssignableCarFilterDTO filterBean) {
        List<SigningCarInfoDTO> assignableCars = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 100;
        while (true) {
            int start = (pageNo - 1) * pageSize;
            List<SigningCarInfoDTO> subAssignableCars = signingCarService.getAssignableCarsByCountry(start, pageSize);
            int size = subAssignableCars.size();
            if (size == 0) {
                break;
            }
            subAssignableCars = filterAssignableCars(filterBean, subAssignableCars);
            assignableCars.addAll(subAssignableCars);
            if (size < pageSize) {
                break;
            }
            pageNo = pageNo + 1;
        }
        return assignableCars;
    }

    /**
     * 根据条件过滤可派单司机
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoDTO> filterAssignableCars(AssignableCarFilterDTO filterBean,
                                                         List<SigningCarInfoDTO> assignableCars) {
        // 需要驾驶此类货物，增加驾驶能力筛选
        assignableCars = filterByDrivingAbility(filterBean, assignableCars);

        // 指定了签约合作商，增加签约合作商筛选
        assignableCars = filterByCargoOwner(filterBean, assignableCars);

        // 运输距离偏好过滤，长途：大于200公里； 短途：小于等于200公里； 长途/短途：不限制距离
        assignableCars = filterByDistancePreference(filterBean, assignableCars);

        // 过滤存在承运中或待支付的专车订单的车主
        // 6600不判断司机是否有待支付订单
        // assignableCars = filterByOrder(filterBean, assignableCars);

        return assignableCars;
    }

    /**
     * 根据驾驶能力筛选
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoDTO> filterByDrivingAbility(AssignableCarFilterDTO filterBean, List<SigningCarInfoDTO> assignableCars) {
        if (DriverDrivingEnum.DRIVING.getCode().equals(filterBean.getDriverDriving()) &&
                StringUtils.isNotEmpty(filterBean.getGoodsTypeName())) {
            assignableCars = assignableCars.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getDrivingAbility()) &&
                            e.getDrivingAbility().contains(filterBean.getGoodsTypeName()))
                    .collect(Collectors.toList());
        }
        return assignableCars;
    }

    /**
     * 签约合作商条件过滤
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoDTO> filterByCargoOwner(AssignableCarFilterDTO filterBean, List<SigningCarInfoDTO> assignableCars) {
        if (Objects.nonNull(filterBean.getCargoOwnerId())) {
            DispatchCargoOwnerDO cargoOwner = dispatchCargoOwnerService.selectByCooperativeId(filterBean.getCargoOwnerId());

            // 该货主指定车方承运，筛选该货主签约司机
            if (Objects.nonNull(cargoOwner) && AssignCarEnum.YES.getCode().equals(cargoOwner.getAssignCar())) {
                DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByCooperativeId(cargoOwner.getCooperativeId());
                if (Objects.isNull(cooperative)) {
                    return assignableCars;
                }
                assignableCars = assignableCars.stream()
                        .filter(e -> {
                            if (StringUtils.isNotEmpty(e.getSigning())) {
                                List<String> list = Arrays.stream(e.getSigning().split(",")).collect(Collectors.toList());
                                return list.contains(cooperative.getCooperativeName());
                            }
                            return false;
                        })
                        // 筛选完成司机认证的司机
                        .filter(e -> Objects.equals(e.getVerifyStatus(), DriverVerifyEnum.VERIFY_SUCCESS.getCode()))
                        .collect(Collectors.toList());
            }
        }
        return assignableCars;
    }

    /**
     * 运输距离偏好条件过滤
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoDTO> filterByDistancePreference(AssignableCarFilterDTO filterBean, List<SigningCarInfoDTO> assignableCars) {
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            BigDecimal distanceKilometer = new BigDecimal("0");
            GoodsAddressLevelRecordDO levelRecord = goodsAddressLevelRecordService.getBySrcMsgId(filterBean.getSrcMsgId());
            if (Objects.nonNull(levelRecord) && Objects.nonNull(levelRecord.getDistanceKilometer())) {
                distanceKilometer = levelRecord.getDistanceKilometer();
            } else {
                Integer distance = transportMainService.selectDistanceBySrcMsgId(filterBean.getSrcMsgId());
                if (Objects.nonNull(distance)) {
                    distanceKilometer = new BigDecimal(distance / 100);
                }
            }
            String preference;
            if (distanceKilometer.compareTo(new BigDecimal("200")) > 0) {
                preference = DistancePreferenceEnum.LONG_DISTANCE.getValue();
            } else {
                preference = DistancePreferenceEnum.SHORT_DISTANCE.getValue();
            }
            String finalPreference = preference;
            assignableCars = assignableCars.stream()
                    .filter(e -> (Objects.equals(finalPreference, e.getDistancePreference()) ||
                            Objects.equals(DistancePreferenceEnum.NO_LIMIT.getValue(), e.getDistancePreference())))
                    .collect(Collectors.toList());
        }
        return assignableCars;
    }

    /**
     * 若司机存在承运中货待支付专车订单，则不给该司机指派(非专车货源需要过滤)
     *
     * @param filterBean
     * @param assignableCars
     * @return
     */
    private List<SigningCarInfoDTO> filterByOrder(AssignableCarFilterDTO filterBean, List<SigningCarInfoDTO> assignableCars) {
        if (Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterBean.getExcellentGoods())) {
            return assignableCars;
        }
        if (CollectionUtils.isNotEmpty(assignableCars)) {
            List<Long> carUserIdList = assignableCars.stream().map(SigningCarInfoDTO::getUserId).collect(Collectors.toList());
            try {
                Map<Long, Boolean> carOrderMap = ordersRemoteService.getOrderListByPayUserIds(carUserIdList);
                if (Objects.nonNull(carOrderMap) && !carOrderMap.isEmpty()) {
                    log.info("专车发货自动派单，货源ID:{}, 查询司机订单返回:{}", filterBean.getSrcMsgId(), JSONObject.toJSONString(carOrderMap));
                    if (MapUtils.isNotEmpty(carOrderMap)) {
                        assignableCars = assignableCars.stream()
                                .filter(v -> (Objects.isNull(carOrderMap.get(v.getUserId())) || !carOrderMap.get(v.getUserId())))
                                .collect(Collectors.toList());
                    }
                }
            } catch (Exception e) {
                log.error("专车发货自动派单，查询司机是否存在承运中或待支付专车订单失败，货源id：{}", filterBean.getSrcMsgId(), e);
            }
        }
        return assignableCars;
    }

    /**
     * 根据专车派单配置过滤
     *
     * @param dispatchConfig
     * @param assignableCars
     * @param filterBean
     * @return
     */
    private List<SigningCarInfoDTO> filterByDispatchConfig(SpecialCarAutoDispatchConfigDO dispatchConfig,
                                                           List<SigningCarInfoDTO> assignableCars,
                                                           AssignableCarFilterDTO filterBean) {
        if (!Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), filterBean.getExcellentGoods())) {
            return assignableCars;
        }

        if (Objects.isNull(dispatchConfig) || Objects.isNull(dispatchConfig.getFavorRate()) || Objects.isNull(dispatchConfig.getReceiveRate())) {
            return assignableCars;
        }
        assignableCars = assignableCars.stream()
                .filter(v -> {
                    if (Objects.nonNull(v.getFavorableComment()) && Objects.nonNull(v.getReceivingOrders())) {
                        return v.getFavorableComment().compareTo(dispatchConfig.getFavorRate()) >= 0 &&
                                v.getReceivingOrders().compareTo(dispatchConfig.getReceiveRate()) >= 0;
                    }
                    return false;
                }).collect(Collectors.toList());
        return assignableCars;
    }

    /**
     * 获取车辆位置和距离
     *
     * @param filterBean
     * @param assignableCars
     */
    private void getLocationAndDistance(AssignableCarFilterDTO filterBean, List<SigningCarInfoDTO> assignableCars, Integer distanceSwitch) {
        Map<String, CurrentLocationVO> map = new HashMap<>();
        // 调用bi获取车头位置信息
        getHeadLocation(filterBean, assignableCars, map);

        for (SigningCarInfoDTO dto : assignableCars) {
            if (Objects.equals(distanceSwitch, 1)) {
                getDistanceByLocationV2(filterBean, map, dto);
            } else {
                getDistanceByLocation(filterBean, map, dto);
            }
        }
    }

    /**
     * 根据位置获取距离，调用腾讯地图距离矩阵接口
     * https://lbs.qq.com/service/webService/webServiceGuide/route/directionTrucking#10.1
     *
     * @param filterBean
     * @param map
     * @param dto
     */
    private void getDistanceByLocationV2(AssignableCarFilterDTO filterBean, Map<String, CurrentLocationVO> map, SigningCarInfoDTO dto) {
        CurrentLocationVO locationVO = map.get(dto.getHeadCityNo());
        if (Objects.isNull(locationVO)) {
            log.info("专车发货自动派单，未获取到车头位置，货源ID:{}, 车头信息:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    dto.getHeadCityNo(), filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }
        dto.setLocation(locationVO.getNewLocation());

        if (StringUtils.isBlank(locationVO.getLocationLatitude()) || StringUtils.isBlank(locationVO.getLocationLongitude()) ||
                Objects.equals(locationVO.getLocationLatitude(), "0.000000") ||
                Objects.equals(locationVO.getLocationLongitude(), "0.000000")) {
            log.info("专车发货自动派单，未获取到车头经纬度，货源ID:{}, 车头信息:{}, 出发地:{}, 目的地:{}，位置信息:{}", filterBean.getSrcMsgId(),
                    dto.getHeadCityNo(), filterBean.getStartCity(), filterBean.getDestCity(), locationVO);
            return;
        }

        List<SigningCarInfoGpscheatDO> gpscheatByCarInfoId = signingCarInfoGpscheatMapper.getGpscheatByCarInfoId(dto.getCarInfoId());

        List<DistanceRpcDTO> distanceRpcDTOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(gpscheatByCarInfoId)) {
            for (SigningCarInfoGpscheatDO signingCarInfoGpscheatDO : gpscheatByCarInfoId) {
                DistanceRpcDTO req = new DistanceRpcDTO();
                req.setSrcMsgId(filterBean.getSrcMsgId());
                req.setFromLatitude(String.valueOf(signingCarInfoGpscheatDO.getLatitude()));
                req.setFromLongitude(String.valueOf(signingCarInfoGpscheatDO.getLongitude()));
                req.setToLatitude(filterBean.getStartLatitude().toString());
                req.setToLongitude(filterBean.getStartLongitude().toString());
                distanceRpcDTOList.add(req);
            }
        }

        Integer distance = 0;
        try {
            DistanceRpcDTO req = new DistanceRpcDTO();
            req.setSrcMsgId(filterBean.getSrcMsgId());
            req.setFromLatitude(locationVO.getLocationLatitude());
            req.setFromLongitude(locationVO.getLocationLongitude());
            req.setToLatitude(filterBean.getStartLatitude().toString());
            req.setToLongitude(filterBean.getStartLongitude().toString());
            distanceRpcDTOList.add(req);
            DistanceRpcVO distanceRpcVO = distanceRemoteService.calculateDistance(req);
            if (Objects.nonNull(distanceRpcVO)) {
                distance = distanceRpcVO.getDistance();
            }
        } catch (Exception e) {
            log.error("专车发货自动派单，根据车头位置查询距离异常，货源ID:{}, 位置信息:{}, 出发地城市:{}, 出发地区:{}",
                    filterBean.getSrcMsgId(), locationVO, filterBean.getStartCity(), filterBean.getStartArea(), e);
        }
        if (Objects.isNull(distance) || distance == 0) {
            log.info("专车发货自动派单，根据车头位置查询距离失败，货源ID:{}, 位置信息:{}, 出发地城市:{}, 出发地区:{}",
                    filterBean.getSrcMsgId(), locationVO, filterBean.getStartCity(), filterBean.getStartArea());
            return;
        }
        log.info("专车发货自动派单，根据车头位置获取距离，货源ID：{}, 车头信息:{}, 出发地城市:{}, 出发地区:{}, 距离:{}",
                filterBean.getSrcMsgId(), dto.getHeadCityNo(), filterBean.getStartCity(), filterBean.getStartArea(), distance);
        //换算公里
        BigDecimal d = new BigDecimal(distance).divide(new BigDecimal("1000"), 1, RoundingMode.HALF_UP);
        dto.setDistance(d);
    }

    /**
     * 根据位置信息获取车辆距离货物的距离
     *
     * @param filterBean
     * @param map
     * @param dto
     */
    private void getDistanceByLocation(AssignableCarFilterDTO filterBean, Map<String, CurrentLocationVO> map, SigningCarInfoDTO dto) {
        CurrentLocationVO locationVO = map.get(dto.getHeadCityNo());
        if (Objects.isNull(locationVO) || StringUtils.isEmpty(locationVO.getNewLocation())) {
            log.info("专车发货自动派单，未获取到车头位置，货源ID:{}, 车头信息:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    dto.getHeadCityNo(), filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }
        String location = locationVO.getNewLocation();
        dto.setLocation(location);

        String address = AddressCuttingUtil.addressCutting(location);
        String province = "";
        String city = "";
        String district = "";
        if (StringUtils.isEmpty(address)) {
            log.info("专车发货自动派单，车头位置截取失败，货源ID:{}, 车头信息:{}, 车头位置:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    dto.getHeadCityNo(), location, filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }
        String[] parts = address.split(",");
        province = parts[0];
        if (parts.length > 2) {
            city = parts[1];
            district = parts[2];
        }
        if (StringUtils.isEmpty( province) || StringUtils.isEmpty(city) || StringUtils.isEmpty(district)) {
            log.info("专车发货自动派单，车头位置截取失败，货源ID:{}, 车头信息:{}, 车头位置:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                    dto.getHeadCityNo(), location, filterBean.getStartCity(), filterBean.getDestCity());
            return;
        }

        int distance = 0;
        String districtNew = districtInfo(district);
        if (city.equals(filterBean.getStartCity()) && (district.equals(filterBean.getStartArea()) || districtNew.equals(filterBean.getStartArea()))) {
            // 车头位置和出发地相同距离赋值1公里
            distance = 100;
        } else {
            //查询距离
            distance = getDistance(filterBean, province, city, district);
            if (0 == distance) {
                distance = getDistance(filterBean, province, city, districtNew);
            }
        }
        if (0 == distance) {
            log.info("专车发货自动派单，根据车头位置查询距离失败，货源ID:{}, 车头信息:{}, 车头城市:{}, 车头区:{}, 出发地城市:{}, 出发地区:{}",
                    filterBean.getSrcMsgId(), dto.getHeadCityNo(), city, district, filterBean.getStartCity(), filterBean.getStartArea());
            return;
        }
        log.info("专车发货自动派单，根据车头位置获取距离，货源ID：{}, 车头信息:{}, 车头城市:{}, 车头区:{}, 出发地城市:{}, 出发地区:{}, 距离:{}",
                filterBean.getSrcMsgId(), dto.getHeadCityNo(), city, district, filterBean.getStartCity(), filterBean.getStartArea(), distance);
        //换算公里
        BigDecimal d = new BigDecimal(distance).divide(new BigDecimal("100"), 1, RoundingMode.HALF_UP);
        dto.setDistance(d);
    }

    private int getDistance(AssignableCarFilterDTO filterBean, String province, String city, String district) {
        MapDictDto mapDictDto = new MapDictDto();
        mapDictDto.setStartProvinc(province);
        mapDictDto.setStartCity(city);
        mapDictDto.setStartArea(district);
        mapDictDto.setDestProvinc(filterBean.getStartProvince());
        mapDictDto.setDestCity(filterBean.getStartCity());
        mapDictDto.setDestArea(filterBean.getStartArea());
        MapDictVo mapDict = mapDictRemoteService.getMapDict(mapDictDto);
        if (Objects.nonNull(mapDict)) {
            return mapDict.getDistance();
        }
        return 0;
    }

    public String districtInfo(String district) {
        if (district.endsWith("区")) {
            return remove(district, "县");
        }
        if (district.endsWith("县")) {
            return remove(district, "区");
        }
        return district;
    }

    public String remove(String district, String name) {
        String substring = district.substring(0, district.length() - 1);
        district = substring + name;

        return district;
    }

    /**
     * 调用bi获取车头位置信息
     *
     * @param filterBean
     * @param assignableCars
     * @param map
     */
    private void getHeadLocation(AssignableCarFilterDTO filterBean, List<SigningCarInfoDTO> assignableCars, Map<String, CurrentLocationVO> map) {
        // 车头去重
        List<String> headList = assignableCars.stream()
                .map(SigningCarInfoDTO::getHeadCityNo)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(headList)) {
            List<List<String>> splitHeadList = ListUtil.splitIntoChunks(headList, 5);
            for (List<String> list : splitHeadList) {
                //去重后的车头信息调用bi获取每个车头位置
                String heads = list.stream()
                        .map(String::trim)
                        .collect(Collectors.joining(","));
                try {
                    StopWatch watch = new StopWatch();
                    watch.start();
                    List<CurrentLocationVO> location = this.getCurrentLocations(heads);
                    watch.stop();
                    log.info("专车发货自动派单，调用bi获取车头位置，货源ID：{}, 请求车头数量:{}，耗时:{} 毫秒, 结果：{}",
                            filterBean.getSrcMsgId(), list.size(), watch.getTotalTimeMillis(), JSON.toJSONString(location));
                    if (CollectionUtils.isNotEmpty(location)) {
                        for (CurrentLocationVO vo : location) {
                            map.put(vo.getHeadCity() + vo.getHeadNo(), vo);
                        }
                    }
                } catch (Exception e) {
                    log.error("专车发货自动派单，调用bi获取车头位置失败，货源ID:{}, 出发地:{}, 目的地:{}", filterBean.getSrcMsgId(),
                            filterBean.getStartCity(), filterBean.getDestCity(), e);
                }
            }
        }
    }

    public List<CurrentLocationVO> getCurrentLocations(String heads) {
        List<CurrentLocationVO> locationData = new ArrayList<>();
        String locationJsonStr = "";
        try {
            locationJsonStr = stringRedisTemplate.opsForValue().get(CACHE_CAR_CURRENT_LOCATION + heads);
            if (StringUtils.isNotBlank(locationJsonStr)) {
                locationData = JSONObject.parseArray(locationJsonStr, CurrentLocationVO.class);
            }
        } catch (Exception e) {
            log.warn("专车发货自动派单，getCurrentLocations json parse error, heads:{}, locationJsonStr: {}", heads, locationJsonStr, e);
        }

        if (CollectionUtils.isNotEmpty(locationData)) {
            return locationData;
        }

        try {
            WebResult<List<CurrentLocationVO>> carRealTimeLocation = carLocationRemoteService.getCarRealTimeLocation(heads, null);
            if (carRealTimeLocation.ok()) {
                locationData = carRealTimeLocation.getData();
            }
            if (CollectionUtils.isNotEmpty(locationData)) {
                stringRedisTemplate.opsForValue().set(CACHE_CAR_CURRENT_LOCATION + heads, JSONObject.toJSONString(locationData), 300, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("专车发货自动派单，getCurrentLocations error, heads:{}", heads, e);
        }
        return locationData;
    }

    /**
     * 司机排序
     *
     * @param newTransport
     * @param user
     * @param dto
     * @param cars
     * @return
     */
    private List<SigningCarDTO> sortCars(TransportMainDO newTransport, UserRpcVO user, AssignableCarFilterDTO dto, List<SigningCarDTO> cars) {
        List<SigningCarDTO> carUsers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cars)) {
            carUsers = signingCarService.getSigningCarList(cars);
        }

        BigDecimal weightDecimal = new BigDecimal(newTransport.getWeight());

        if (Objects.equals(ExcellentGoodsEnums.SPECIAL.getCode(), dto.getExcellentGoods())) {
            // 根据是否车型匹配，重新对司机进行排序，优先指派给车型匹配的司机
            if (weightDecimal.compareTo(new BigDecimal("8")) > 0) {
                //如果货物吨位 <= 8则不走车型匹配
                carUsers = sortByCarTypeMatch(carUsers, newTransport.getWeight());
            } else {
                //不走车型匹配直接按匹配
                carUsers.forEach(v -> v.setCarTypeMatch(1));
            }
        } else {
            // 非专车优先指派给司机标签为全职运力的车辆，再指派给兼职运力，兼职运力的车辆优先指派给车型匹配的车辆
            carUsers = sortByDriverTag(carUsers, newTransport.getWeight());
        }

        // 根据货物吨位过滤车辆类型
        // 如果货物吨位 <= 8 carType必须是清障车
        // 如果货物吨位 > 8 carType必须不是清障车
        if (CollectionUtils.isNotEmpty(carUsers)) {
            if (weightDecimal.compareTo(new BigDecimal("8")) <= 0) {
                // 吨位 <= 8，只保留清障车
                carUsers = carUsers.stream()
                        .filter(v -> CLEARANCE_VEHICLE_TYPE.equals(v.getCarType()))
                        .collect(Collectors.toList());
                log.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, 吨位<=8只保留清障车，过滤后车辆数量:{}",
                        newTransport.getSrcMsgId(), user.getId(), carUsers.size());
            } else {
                // 吨位 > 8，排除清障车
                carUsers = carUsers.stream()
                        .filter(v -> !CLEARANCE_VEHICLE_TYPE.equals(v.getCarType()))
                        .collect(Collectors.toList());
                log.info("专车发货自动派单，货源ID:{}, 发货用户ID:{}, 吨位>8排除清障车，过滤后车辆数量:{}",
                        newTransport.getSrcMsgId(), user.getId(), carUsers.size());
            }
        }

        return carUsers;
    }

    /**
     * 非专车优先指派给司机标签为全职运力的车辆，再指派给兼职运力，兼职运力的车辆优先指派给车型匹配的车辆
     *
     * @param carUsers
     * @param weight
     * @return
     */
    private List<SigningCarDTO> sortByDriverTag(List<SigningCarDTO> carUsers, String weight) {
        if (CollectionUtils.isEmpty(carUsers)) {
            return new ArrayList<>();
        }

        // 全职运力
        List<SigningCarDTO> fullTimeCars = carUsers.stream()
                .filter(v -> Objects.equals(v.getDriverTag(), DriverTagEnum.FULL_TIME.getCode()))
                .sorted(Comparator.comparing(SigningCarDTO::getRange).reversed())
                .collect(Collectors.toList());

        // 兼职运力
        List<SigningCarDTO> partTimeCars = carUsers.stream()
                .filter(v -> Objects.equals(v.getDriverTag(), DriverTagEnum.PART_TIME.getCode()))
                .sorted(Comparator.comparing(SigningCarDTO::getRange).reversed())
                .collect(Collectors.toList());
        List<SigningCarDTO> sortedPatTimeCars = this.sortByCarTypeMatch(partTimeCars, weight);
        fullTimeCars.addAll(sortedPatTimeCars);

        return fullTimeCars;
    }

    /**
     * 专车货源根据是否车型匹配，重新对司机进行排序，优先指派给车型匹配的司机
     *
     * @param carUsers 满足条件的司机列表
     * @param weight   货物吨重
     * @return
     */
    private List<SigningCarDTO> sortByCarTypeMatch(List<SigningCarDTO> carUsers, String weight) {
        if (CollectionUtils.isNotEmpty(carUsers) && StringUtils.isNotBlank(weight)) {
            BigDecimal weightDecimal = new BigDecimal(weight);
            List<String> carTypeList = transportDispatchCarTypeService.selectCarTypeListByWeight(weightDecimal);
            if (CollectionUtils.isNotEmpty(carTypeList)) {
                List<SigningCarDTO> sortedCarUsers = carUsers.stream()
                        .filter(v -> carTypeList.contains(v.getCarType()))
                        .sorted(Comparator.comparing(SigningCarDTO::getRange).reversed())
                        .collect(Collectors.toList());
                sortedCarUsers.forEach(v -> v.setCarTypeMatch(1));

                List<SigningCarDTO> carTypeNotMatchList = carUsers.stream()
                        .filter(v -> !carTypeList.contains(v.getCarType()))
                        .sorted(Comparator.comparing(SigningCarDTO::getRange).reversed())
                        .collect(Collectors.toList());
                carTypeNotMatchList.forEach(v -> v.setCarTypeMatch(0));
                sortedCarUsers.addAll(carTypeNotMatchList);

                return sortedCarUsers;
            }
        }
        return carUsers;
    }

    private void saveSpecialCarDispatch(AutoAssignOrderDTO assignOrderDTO) {
        List<AssignOrderUser> userList = assignOrderDTO.getUserList();
        Long dispatchId = saveDispatch(assignOrderDTO, userList);

        saveDispatchDetail(assignOrderDTO, userList, dispatchId);
    }

    private Long saveDispatch(AutoAssignOrderDTO dto, List<AssignOrderUser> userList) {
        SpecialCarDispatchDO specialCarDispatch = new SpecialCarDispatchDO();
        //货源ID
        specialCarDispatch.setTsId(dto.getSrcMsgId());
        //发货用户类型：1-调度发货，2-普通用户发货
        specialCarDispatch.setPublishUserType(dto.getPublishUserType());
        //调度人员ID
        specialCarDispatch.setDispatchUserId(dto.getDispatchUserId());
        //调度人员姓名
        specialCarDispatch.setDispatchUserName(dto.getDispatchUserName());
        //调度人员手机号
        specialCarDispatch.setDispatchCellPhone(dto.getDispatchCellPhone());
        //调度人员钉钉手机号
        specialCarDispatch.setDispatchDingTalkPhone(dto.getDispatchDingTalkPhone());
        // 派单方式
        specialCarDispatch.setDispatchType(dto.getDispatchType());
        // 先后指派司机x分钟未接单指派下一位
        specialCarDispatch.setAfterMinutes(dto.getAfterMinutes());
        //接单状态：0.未接单  1.已接单
        specialCarDispatch.setAcceptStatus(0);
        //司机列表数量
        specialCarDispatch.setUserCount(userList.size());
        //已派单司机数量
        specialCarDispatch.setDispatchUserCount(0);
        //派单失败通知次数
        specialCarDispatch.setNotifyCount(0);
        //创建时间
        specialCarDispatch.setCreateTime(new Date());
        //修改时间
        specialCarDispatch.setModifyTime(new Date());
        specialCarDispatchMapper.insert(specialCarDispatch);
        log.info("专车发货自动派单，dispatch表数据保存成功，srcMsgId:{}", dto.getSrcMsgId());
        return specialCarDispatch.getId();
    }

    /**
     * 保存派单记录详情表
     *
     * @param dto
     * @param userList
     * @param dispatchId
     */
    private void saveDispatchDetail(AutoAssignOrderDTO dto, List<AssignOrderUser> userList, Long dispatchId) {
        for (AssignOrderUser driverInfo : userList) {
            SpecialCarDispatchDetailDO specialCarDispatchDetail = new SpecialCarDispatchDetailDO();
            specialCarDispatchDetail.setDispatchId(dispatchId);
            specialCarDispatchDetail.setTsId(dto.getSrcMsgId());
            specialCarDispatchDetail.setUserId(driverInfo.getUserId());
            specialCarDispatchDetail.setPayLinkPhone(driverInfo.getTelephone());
            //司机ID
            specialCarDispatchDetail.setDriverId(driverInfo.getDriverId());
            //位置
            specialCarDispatchDetail.setLocation(driverInfo.getLocation());
            //距离
            specialCarDispatchDetail.setDistance(driverInfo.getDistance());
            //tyt_signing_car_info表ID
            specialCarDispatchDetail.setCarInfoId(driverInfo.getCarInfoId());
            specialCarDispatchDetail.setPriority(driverInfo.getPriority());
            specialCarDispatchDetail.setDispatchStatus(0);
            specialCarDispatchDetail.setAcceptStatus(0);
            specialCarDispatchDetail.setCreateTime(new Date());
            specialCarDispatchDetail.setModifyTime(new Date());
            specialCarDispatchDetail.setCarId(driverInfo.getCarId());
            specialCarDispatchDetail.setCarTypeMatch(driverInfo.getCarTypeMatch());
            specialCarDispatchDetail.setDriverTag(driverInfo.getDriverTag());
            specialCarDispatchDetailService.saveSpecialCarDispatchDetail(specialCarDispatchDetail);
        }
    }

    /**
     * 组装派单实体
     *
     * @param main
     * @param user
     * @param carUsers
     * @param dispatchConfig
     * @return
     */
    private AutoAssignOrderDTO getAutoAssignOrderDTO(TransportMainDO main, UserRpcVO user, List<SigningCarDTO> carUsers, SpecialCarAutoDispatchConfigDO dispatchConfig) {
        AutoAssignOrderDTO assignOrderBean = new AutoAssignOrderDTO();
        assignOrderBean.setSrcMsgId(main.getSrcMsgId());
        assignOrderBean.setPublishUserType(PublishUserTypeEnum.NORMAL_USER.getCode());
        assignOrderBean.setDispatchType(1);
        if (Objects.nonNull(dispatchConfig) && Objects.nonNull(dispatchConfig.getDispatchType())) {
            assignOrderBean.setDispatchType(dispatchConfig.getDispatchType());
            assignOrderBean.setAfterMinutes(dispatchConfig.getAfterMinutes());
        }
        // 如果发货人是代调，增加代调信息
        List<DispatchCompanyDO> companyDOList = dispatchCompanyService.selectByUserId(user.getId());
        if (CollectionUtils.isNotEmpty(companyDOList)) {
            DispatchCompanyDO companyDO = companyDOList.get(0);
            assignOrderBean.setPublishUserType(PublishUserTypeEnum.DISPATCH.getCode());
            assignOrderBean.setDispatchUserId(user.getId());
            assignOrderBean.setDispatchUserName(user.getUserName());
            assignOrderBean.setDispatchCellPhone(user.getCellPhone());
            assignOrderBean.setDispatchDingTalkPhone(companyDO.getDingTalkPhone());
        }
        List<AssignOrderUser> userList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(carUsers)) {
            // priority数字越小优先级越高
            int priority = 1;
            for (SigningCarDTO dto : carUsers) {
                if (Objects.isNull(dto.getUserId())) {
                    continue;
                }
                AssignOrderUser orderUser = new AssignOrderUser();
                orderUser.setUserId(dto.getUserId());
                orderUser.setTelephone(dto.getPhone());
                orderUser.setPriority(priority);
                orderUser.setDriverId(dto.getDriverId());
                orderUser.setCarInfoId(dto.getId());
                orderUser.setDistance(dto.getDistance());
                orderUser.setLocation(dto.getLocation());
                orderUser.setCarTypeMatch(dto.getCarTypeMatch());
                orderUser.setDriverTag(dto.getDriverTag());
                orderUser.setCarId(getTytCarIdBySigningCarData(dto));

                if (userList.contains(orderUser)) {
                    continue;
                }
                userList.add(orderUser);
                priority = priority + 1;
            }
        }
        assignOrderBean.setUserList(userList);
        return assignOrderBean;
    }

    private Long getTytCarIdBySigningCarData(SigningCarDTO dto) {
        if (dto.getUserId() != null && StringUtils.isNotBlank(dto.getHeadCityNo()) && dto.getHeadCityNo().length() > 2
                && StringUtils.isNotBlank(dto.getTailCityNo()) && dto.getTailCityNo().length() > 2) {
            log.info("专车发货自动派单 根据车主和车辆信息获取平台车辆表ID，用户ID:{}, 车头车牌号:{}, 挂车车牌号:{}", dto.getUserId(), dto.getHeadCityNo(), dto.getTailCityNo());
            List<CarRpcVO> list = carRemoteService.getCarListByHeadNo(dto.getUserId(), dto.getHeadCityNo().substring(0, 1), dto.getHeadCityNo().substring(1));
            if (CollectionUtils.isNotEmpty(list)) {
                CarRpcVO carRpcVO = list.stream().filter(v -> Objects.equals(v.getAuth(), "1")
                        && Objects.equals(v.getTailCity(), dto.getTailCityNo().substring(0, 1))
                        && Objects.equals(v.getTailNo(), dto.getTailCityNo().substring(1))).findFirst().orElse(null);
                if (Objects.nonNull(carRpcVO)) {
                    return carRpcVO.getId();
                }
            }
        }
        return null;
    }

    private AssignableCarFilterDTO getAssignableCarFilterDTO(TransportMainDO main, UserRpcVO user, Integer distanceLimit) {
        AssignableCarFilterDTO dto = new AssignableCarFilterDTO();
        dto.setSrcMsgId(main.getSrcMsgId());
        dto.setStartProvince(main.getStartProvinc());
        dto.setStartCity(main.getStartCity());
        dto.setStartArea(main.getStartArea());
        dto.setDestProvince(main.getDestProvinc());
        dto.setDestCity(main.getDestCity());
        dto.setDriverDriving(main.getDriverDriving());
        dto.setGoodsTypeName(main.getGoodTypeName());
        dto.setCargoOwnerId(main.getCargoOwnerId());
        dto.setDistanceLimit(distanceLimit);
        dto.setExcellentGoods(main.getExcellentGoods());
        dto.setUserId(user.getId());
        dto.setStartLatitude(main.getStartLatitude());
        dto.setStartLongitude(main.getStartLongitude());
        return dto;
    }

}
