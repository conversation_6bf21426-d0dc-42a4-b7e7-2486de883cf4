package com.teyuntong.goods.service.service.rpc.publish.direct;

import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.checker.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发货校验器
 *
 * <AUTHOR>
 * @since 2025/02/11 10:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportDirectPublishChecker {

    private final SeckillGoodsLockChecker seckillGoodsLockChecker;
    private final PublishParamsChecker publishParamsChecker;
    private final ExcellentTransportChecker excellentTransportChecker;
    private final UserPublishLimitChecker userPublishLimitChecker;
    private final AutoConvertExcellentGoodsChecker autoConvertExcellentGoodsChecker;
    private final InvoiceTransportChecker invoiceTransportChecker;
    private final YmmTransportChecker ymmTransportChecker;
    private final UserAuth<PERSON>he<PERSON> userAuthChecker;
    private final BackendTransportChecker backendTransportChecker;
    private final PersonalDuplicateChecker personalDuplicateChecker;
    private final AllowTransportTopChecker allowTransportTopChecker;
    private final CarpoolTransportChecker carpoolTransportChecker;
    private final ExposureCardUseChecker exposureCardUseChecker;
    private final PublishPermissionChecker publishPermissionChecker;
    private final AddPriceChecker addPriceChecker;
    private final AllowPriceChecker allowPriceChecker;
    private final UsefulOrderChecker usefulOrderChecker;

    /**
     * 校验直接发布主流程
     */
    public void checkChain(DirectPublishProcessBO processBO) {

        // 好货抢单锁定判断
        seckillGoodsLockChecker.check(processBO);

        // 参数校验
        publishParamsChecker.checkDirectPublish(processBO);

        // 校验是否可以自动转优车定价货源
//        autoConvertExcellentGoodsChecker.checkDirectPublish(processBO);
        // 优车发货新老映射
        autoConvertExcellentGoodsChecker.setExcellentGoodsType(processBO);

        // 加价校验
        addPriceChecker.checkDirectPublish(processBO);

        // 转一口价校验订单是否已存在
        usefulOrderChecker.check(processBO);

        // 校验价格是否合理
        allowPriceChecker.checkDirectPublish(processBO);

        // 曝光卡使用校验
        exposureCardUseChecker.check(processBO);

        // 优车1.0发货权益
        excellentTransportChecker.checkDirectPublish(processBO);

        // 校验用户发货限制
        userPublishLimitChecker.check(processBO.getUser());

        // 开票货源校验
        invoiceTransportChecker.checkDirectPublish(processBO);

        // 运满满货源校验
        ymmTransportChecker.checkDirectPublish(processBO.getOldMain());

        // 校验用户实名认证权益
        userAuthChecker.checkDirectPublish(processBO);

        // 校验小程序发货有效性
        backendTransportChecker.checkDirect(processBO.getDirectPublishBO());

        // 校验重货
        personalDuplicateChecker.check(processBO, null);

        // 校验货源刷新权限
        allowTransportTopChecker.checkDirectPublish(processBO);

        // 拼车货源校验是否满足拼车条件
        carpoolTransportChecker.check(processBO);

        // 扣减用户发货权益
        publishPermissionChecker.checkDirectPublishPermission(processBO, false);

    }


}
