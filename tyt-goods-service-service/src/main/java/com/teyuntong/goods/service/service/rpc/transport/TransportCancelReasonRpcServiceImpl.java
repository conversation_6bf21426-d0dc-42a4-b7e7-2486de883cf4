package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.service.TransportCancelReasonRpcService;
import com.teyuntong.goods.service.client.transport.vo.CancelReasonDetailVO;
import com.teyuntong.goods.service.client.transport.vo.CancelReasonVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.BackoutReasonDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.BackoutReasonService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytSourceRemoteService;
import com.teyuntong.infra.basic.resource.client.source.vo.TytSourceVO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 货源撤销原因
 *
 * <AUTHOR>
 * @since 2025-02-16 13:34
 */
@RestController
@Slf4j
public class TransportCancelReasonRpcServiceImpl implements TransportCancelReasonRpcService {
    @Autowired
    private TytSourceRemoteService tytSourceRemoteService;
    @Autowired
    private ABTestRemoteService abTestRemoteService;
    @Autowired
    private TytConfigRemoteService tytConfigRemoteService;
    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private BackoutReasonService backoutReasonService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    private static final String BACKOUT_REASON_KEY = "backout_reason";
    private static final String REFRESH_NOTICE = "本次免费刷新1次货源";
    private static final String ABTEST_CANCEL_GIVE_REFRESH = "cancel_give_refresh";
    private static final String CONFIG_REFRESH_NOTICE_PUBLISH_TYPES = "refresh_notice_publish_types";
    private static final String CONFIG_CANCEL_COUNT_LIMIT = "cancel_count_limit";
    // 撤销并重发的撤销原因key
    private static final int CANCEL_AND_REPUBLISH_REASON_VALUE = 13;

    /**
     * 获取货源撤销原因列表
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public CancelReasonVO getCancelReasonList(Long srcMsgId) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        CancelReasonVO reasonVO = new CancelReasonVO();

        List<TytSourceVO> reasonList = tytSourceRemoteService.getListByGroupCode(BACKOUT_REASON_KEY);
        List<CancelReasonDetailVO> detailVOS = reasonList.stream()
                .filter(v -> Objects.equals(v.getStatus(), 0))
                .sorted(Comparator.comparingInt(TytSourceVO::getSort))
                .map(this::convertToCancelReasonDetailVO)
                .collect(Collectors.toList());
        reasonVO.setReasonList(detailVOS);

        // 是否要显示刷新货源提示信息
        boolean showRefreshNotice = showRefreshNotice(loginUser, srcMsgId);
        if (showRefreshNotice) {
            reasonVO.setNotice(REFRESH_NOTICE);
        }
        return reasonVO;
    }

    /**
     * 判断是否要展示刷新货源提示信息
     *
     * @param loginUser
     * @return
     */
    private boolean showRefreshNotice(LoginUserDTO loginUser, Long srcMsgId) {
        if (Objects.isNull(srcMsgId)) {
            return false;
        }
        TransportMainDO transport = transportMainService.getTransportMainForId(srcMsgId);
        // 货源不在发布中，不显示
        if (Objects.isNull(transport) || !Objects.equals(transport.getStatus(), 1)) {
            return false;
        }
        Integer userType = abTestRemoteService.getUserType(ABTEST_CANCEL_GIVE_REFRESH, loginUser.getUserId());
        if (Objects.equals(userType, YesOrNoEnum.NO.getId())) {
            return false;
        }
        String configType = tytConfigRemoteService.getStringValue(CONFIG_REFRESH_NOTICE_PUBLISH_TYPES);
        if (StringUtils.isBlank(configType)) {
            return false;
        }
        // 货源类型是否匹配
        boolean goodsTypeMatch = isGoodsTypeMatch(transport, configType);
        if (!goodsTypeMatch) {
            return false;
        }
        // 该货源是否是首次撤销
        List<BackoutReasonDO> backoutReasons = backoutReasonService.getBackoutReasonBySrcMsgId(srcMsgId);
        if (CollectionUtils.isNotEmpty(backoutReasons)) {
            return false;
        }
        // 该货主当天撤销次数小于x次
        Integer cancelCountLimit = tytConfigRemoteService.getIntValue(CONFIG_CANCEL_COUNT_LIMIT, 3);
        int cancelCount = 0;
        // int cancelCount = backoutReasonService.getTodayUserCancelCount(loginUser.getUserId(), List.of(CANCEL_AND_REPUBLISH_REASON_VALUE));
        String cacheKey = RedisKeyConstant.CANCEL_GIVE_REFRESH_CACHE_PREFIX + loginUser.getUserId();
        String cacheRefreshValue = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(cacheRefreshValue)) {
            cancelCount = Integer.parseInt(cacheRefreshValue);
        }
        return cancelCount < cancelCountLimit;
    }

    private static boolean isGoodsTypeMatch(TransportMainDO transport, String configType) {
        String[] typeList = configType.split(",");
        for (String type : typeList) {
            // 电议无价
            if (Objects.equals(type, GoodsTypeEnum.TEL_PRICELESS.getCode())) {
                if (Objects.equals(transport.getPublishType(), PublishTypeEnum.TELE.getCode()) &&
                        (StringUtils.isBlank(transport.getPrice()) || Objects.equals(transport.getPrice(), "0"))) {
                    return true;
                }
            }
            // 一口价
            if (Objects.equals(type, GoodsTypeEnum.FIX_PRICE.getCode())) {
                if (Objects.equals(transport.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
                    return true;
                }
            }
            // 电议有价
            if (Objects.equals(type, GoodsTypeEnum.TEL_HAVE_PRICE.getCode())) {
                if (Objects.equals(transport.getPublishType(), PublishTypeEnum.TELE.getCode()) &&
                        StringUtils.isNotBlank(transport.getPrice()) && !Objects.equals(transport.getPrice(), "0")) {
                    return true;
                }
            }
        }
        return false;
    }

    private CancelReasonDetailVO convertToCancelReasonDetailVO(TytSourceVO sourceVO) {
        CancelReasonDetailVO detailVO = new CancelReasonDetailVO();
        detailVO.setReasonKey(sourceVO.getName());
        detailVO.setReasonValue(sourceVO.getValue());
        // 添加其他需要映射的字段
        return detailVO;
    }

    /**
     * 撤销送刷新货源类型：1-电议无价，2-一口价，3-电议有价
     */
    @Getter
    enum GoodsTypeEnum {
        TEL_PRICELESS("1", "电议无价"),
        FIX_PRICE("2", "一口价"),
        TEL_HAVE_PRICE("3", "电议有价");
        private final String code;
        private final String name;

        GoodsTypeEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
