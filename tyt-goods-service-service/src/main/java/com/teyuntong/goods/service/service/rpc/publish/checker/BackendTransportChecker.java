package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.util.ObjectUtil;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportBackendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportBackendService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 校验小程序发货有效性
 *
 * <AUTHOR>
 * @since 2025/02/21 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BackendTransportChecker {

    private final TransportBackendService transportBackendService;

    /**
     * 货源发布校验小程序货源
     *
     * @param publishBO
     * @param user
     */
    public void checkBackend(PublishBO publishBO, TransportBackendDO transportBackend, UserRpcVO user) {

        // 如果当前发布的小程序货源已经被发布的，进行提示
        if (transportBackend != null && Objects.equals(transportBackend.getSrcMsgId(), publishBO.getSrcMsgId())) {
            throw new BusinessException(GoodsErrorCode.BACKEND_TRANSPORT_PUBLISHED);
        }

    }

    /**
     * 校验小程序发货有效性
     */
    public void checkDirect(DirectPublishBO publishBO) {
        if (Objects.equals(publishBO.getIsBackendTransport(), 1)) {
            TransportBackendDO transportBackendDO = transportBackendService.selectBySrcMsgId(publishBO.getSrcMsgId());
            if (transportBackendDO != null && ObjectUtil.equal(transportBackendDO.getStatus(), 2)) {
                throw new BusinessException(GoodsErrorCode.GOODS_HAS_CANCELED);
            }
        }
    }



}
