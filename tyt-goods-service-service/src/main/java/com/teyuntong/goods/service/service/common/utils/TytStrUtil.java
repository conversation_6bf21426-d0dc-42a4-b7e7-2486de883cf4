package com.teyuntong.goods.service.service.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2025/2/18 14:42
 */
public class TytStrUtil {

    /**
     * 替换字符串中的手机号为星号
     *
     * @param taskContent
     * @return
     */
    public static String hidePhone(String taskContent) {
        if (StringUtils.isBlank(taskContent)) {
            return "";
        }
        String strTemp = taskContent;
        String tempStr = "";
        boolean flag = true;

        // 是否有连字符-
        int phoneType = taskContent.indexOf("-");
        if (phoneType > -1) {
            // 验证座机逻辑,是否符合座机逻辑,按7位截取
            do {
                String temp = TytStrUtil.getNumbersFromStr(strTemp, 7);
                if (StringUtils.isNotEmpty(temp)) {
                    tempStr = TytStrUtil.replaceStr4StarByIndex(temp, 0, 4);
                    strTemp = strTemp.replace(temp, tempStr);
                } else {
                    flag = false;
                }
            } while (flag);
        } else {
            // 验证手机号逻辑,是否存在11位连续数字
            do {
                String temp = TytStrUtil.getNumbersFromStr(strTemp, 10);
                if (StringUtils.isNotEmpty(temp)) {
                    tempStr = TytStrUtil.replaceStr4StarByIndex(temp, 3, 7);
                    strTemp = strTemp.replace(temp, tempStr);
                } else {
                    flag = false;
                }
            } while (flag);
        }
        return strTemp;
    }

    /**
     * 截取数字
     *
     * @param content
     * @return
     */
    public static String getNumbersFromStr(String content, int length) {
        Pattern pattern = Pattern.compile("[0-9]{" + length + "}");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            return matcher.group(0);
        }
        return "";
    }

    /**
     * 隐藏字符串中的手机号
     * @param value
     * @return
     */
    public static String hidePhoneInStr(String value){
        if(StringUtils.isBlank(value)){
            return value;
        }
        String reg = "\\d{11,}";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(value);
        while(matcher.find()){
            value = value.replace(matcher.group(),"***");
        }
        return value;
    }


    /**
     * 将指定位置字符串替换为4个星号
     *
     * @param str
     * @param begin
     * @param end
     * @return
     */
    public static String replaceStr4StarByIndex(String str, int begin, int end) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        int length = str.length();
        System.out.println(length);
        if (begin < 0 || begin >= length || end >= length) {
            System.out.println("截取范围超出字符串长度");
            return null;
        }
        String temp = str.substring(0, begin) + "****" + str.substring(end, length);
        return temp;
    }
}