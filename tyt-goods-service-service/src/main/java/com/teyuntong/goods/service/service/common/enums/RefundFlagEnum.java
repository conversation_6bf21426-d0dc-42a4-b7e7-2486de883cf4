package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 是否退款枚举
 * <AUTHOR>
 * @since 2025-02-29 14:33
 */
public enum RefundFlagEnum {
    NO_RETURN(0, "不退还"),
    RETURN(1, "退还")
    ;

    @Getter
    private Integer code;

    @Getter
    private String name;

    RefundFlagEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
