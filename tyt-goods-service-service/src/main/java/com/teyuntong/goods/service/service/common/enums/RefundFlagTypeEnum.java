package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 订金模式 0:不限；1:退还；2:不退还
 *
 * <AUTHOR>
 * @since 2025-07-17 20:02
 */
@Getter
@AllArgsConstructor
public enum RefundFlagTypeEnum {
    NO_LIMIT(0, "不限"),
    GIVE_BACK(1, "退还"),
    NOT_GIVE_BACK(2, "不退还")
    ;
    private Integer code;
    private String name;


    public static String getName(Integer code) {
        for (RefundFlagTypeEnum e : RefundFlagTypeEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e.getName();
            }
        }
        return "";
    }
}
