package com.teyuntong.goods.service.service.rpc.transport.checker;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 置撤销置成交校验
 *
 * <AUTHOR>
 * @since 2025/02/21 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SaveTransportStatusChecker {

    private final TransportMainService transportMainService;

    /**
     * 置撤销置成交校验
     */
    public void check(Long srcMsgId, Long operatorUserId) {
        if (srcMsgId != null && srcMsgId > 0) {
            TransportMainDO transportMainForId = transportMainService.getTransportMainForId(srcMsgId);
            if (transportMainForId != null) {
                if (operatorUserId.compareTo(transportMainForId.getUserId()) != 0) {
                    throw new BusinessException(GoodsErrorCode.ERROR_NO_BELONG_TO_ONESELF);
                }
                if (transportMainForId.getStatus() != 1 || transportMainForId.getCtime().before(DateUtil.beginOfDay(new Date()))) {
                    throw new BusinessException(GoodsErrorCode.ERROR_NO_IN_PUBLISH);
                }
            } else {
                throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
            }
        }
    }
}
