package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportAutoResendRecordDO;
import com.teyuntong.goods.service.service.biz.publish.service.TransportAutoResendService;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 自动重发货源数据记录
 *
 * <AUTHOR>
 * @since 2025/02/23 17:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AutoResendTransportPostHandler {

    private final TransportAutoResendService transportAutoResendService;

    /**
     * 添加货源重发日志
     */
    @Async("threadPoolExecutor")
    public void handler(DirectPublishProcessBO processBO) {
        // 历史货源直接发布需要记录数据
        if (processBO.getDirectPublishBO().isHistoryGoods()) {
            boolean autoResend = PublishOptEnum.AUTO_RESEND.equals(processBO.getOptEnum());
            transportAutoResendService.addResendLog(
                    processBO.getOldMain().getSrcMsgId(), // 旧货源id
                    processBO.getTransportMain().getSrcMsgId(),  // 新货源id
                    autoResend ? 1 : 2); // 重发类型 1自动重发；2手动重发
        }
    }

    /**
     * 添加货源重发日志
     */
    @Async("threadPoolExecutor")
    public void saveAutoResendRecord(Integer autoResend, Long userId, Long srcMsgId) {

        TransportAutoResendRecordDO autoResendRecordDO = transportAutoResendService.getRecordByOldSrcMsgId(srcMsgId);
        if (Objects.equals(autoResend, YesOrNoEnum.YES.getId())) {
            if (autoResendRecordDO == null) {
                autoResendRecordDO = new TransportAutoResendRecordDO();
                autoResendRecordDO.setUserId(userId);
                autoResendRecordDO.setOldSrcMsgId(srcMsgId);
                autoResendRecordDO.setCreateTime(new Date());
                autoResendRecordDO.setModifyTime(new Date());
                transportAutoResendService.saveRecord(autoResendRecordDO);
            }
        }else{
            if(autoResendRecordDO != null){
                transportAutoResendService.deleteRecordById(autoResendRecordDO.getId());
            }
        }
    }
}
