package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 校验该货源的订单是否已存在（包括未支付的）
 *
 * <AUTHOR>
 * @since 2025/03/09 16:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UsefulOrderChecker {

    private final OrdersRemoteService ordersRemoteService;

    public void check(DirectPublishProcessBO processBO) {
        // 只有转一口价才校验
        if (!PublishOptEnum.TRANSFER_FIXED.equals(processBO.getOptEnum())) {
            return;
        }

        Long srcMsgId = processBO.getDirectPublishBO().getSrcMsgId();
        TransportOrdersVO transportOrdersVO = ordersRemoteService.checkUsefulOrderExist(srcMsgId);
        if (Objects.nonNull(transportOrdersVO)) {
            throw BusinessException.createException(GoodsErrorCode.TRANSFER_FIXED_PARAM_ERROR.getCode(),
                    "该货源已被车方支付，不可继续转为一口价货源!");
        }

    }

}
