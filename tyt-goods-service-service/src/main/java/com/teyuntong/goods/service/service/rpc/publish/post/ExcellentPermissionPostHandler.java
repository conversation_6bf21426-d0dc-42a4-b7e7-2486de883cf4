package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.ExcellentPriceCountService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.user.ExcellentGoodsCardRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.user.service.client.permission.enums.ExcellentCardTypeEnum;
import com.teyuntong.user.service.client.permission.enums.ExcellentGoodsTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 优车权益扣减 post handler
 *
 * <AUTHOR>
 * @since 2025/03/07 18:37
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcellentPermissionPostHandler {

    private final ExcellentGoodsCardRemoteService excellentGoodsCardRemoteService;
    private final ExcellentPriceCountService excellentPriceCountService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        TransportMainDO newMainDO = processBO.getTransportMain();

        // 新版融合优车发货不扣除权益
        if (processBO.getBaseParam().getClientFusion() != null && PublishGoodsTypeEnum.isExcellentGoods(newMainDO.getPublishGoodsType())) {
            log.info("新版优车发货不扣除权益，srcMsgId:{}，publishGoodsType:{}", newMainDO.getSrcMsgId(), newMainDO.getPublishGoodsType());
            return;
        }

        // 如果发布成功，优车货源添加发布时间，供货源下放使用,并扣减使用次数
        Long userId = processBO.getUser().getId();

        if (Objects.equals(newMainDO.getExcellentGoods(), ExcellentGoodsEnums.EXCELLENT.getCode())) {
            if (newMainDO.getExcellentCardId() == null) {
                ExcellentGoodsTypeEnum excellentGoodsTypeEnum = getExcellentGoodsTypeEnum(newMainDO.getPublishType(), newMainDO.getPrice());
                excellentGoodsCardRemoteService.increaseExcellentGoodsPublishTimes(userId, excellentGoodsTypeEnum);
            } else {
                // 更新优车发货卡状态
                excellentGoodsCardRemoteService.updateExcellentCardStatus(newMainDO.getExcellentCardId(), ExcellentCardTypeEnum.USED);
            }

            // 6680 如果是手动选择优车2.0电议，需要扣减使用次数。
            saveExcellentPriceTele(processBO);
        }
    }

    /**
     * 根据发布类型和价格获取货源类型
     *
     * @param publishType 货源类型（电议1，一口价2）
     * @param price       价格
     */
    public ExcellentGoodsTypeEnum getExcellentGoodsTypeEnum(Integer publishType, String price) {
        if (Objects.equals(publishType, 2)) {
            return ExcellentGoodsTypeEnum.FIXED;
        } else if (TransportUtil.hasPrice(price)) {
            return ExcellentGoodsTypeEnum.HAVEPRICE;
        } else {
            return ExcellentGoodsTypeEnum.NOPRICE;
        }
    }

    /**
     * 6680 记录优车2.0电议次数。
     */
    public void saveExcellentPriceTele(BasePublishProcessBO processBO) {
        // 非电议跳过
        if (!PublishTypeEnum.TELE.getCode().equals(processBO.getTransportMain().getPublishType())) {
            return;
        }

        boolean automaticGoodCarPriceTransport = false; // 是否自动转优车2.0电议
        boolean needUseExcellentGoodsTele = false; // 是否手动选择优车2.0电议

        if (processBO instanceof PublishProcessBO publishBO) {
            automaticGoodCarPriceTransport = publishBO.getPublishBO().isAutomaticGoodCarPriceTransport();
            needUseExcellentGoodsTele = publishBO.getPublishBO().isNeedUseExcellentGoodsTele();
        } else if (processBO instanceof DirectPublishProcessBO directBO) {
            automaticGoodCarPriceTransport = directBO.getDirectPublishBO().isAutomaticGoodCarPriceTransport();
            needUseExcellentGoodsTele = directBO.getDirectPublishBO().isNeedUseExcellentGoodsTele();
        }

        Long userId = processBO.getUser().getId();
        Long srcMsgId = processBO.getTransportMain().getSrcMsgId();

        // 自动转优车电议，次数+1
        if (automaticGoodCarPriceTransport) {
            excellentPriceCountService.saveAutoTransferTele(userId, srcMsgId);
        }
        // 手动选择记录消耗次数
        if (needUseExcellentGoodsTele) {
            excellentPriceCountService.saveUseCount(userId, srcMsgId);
        }

    }

}
