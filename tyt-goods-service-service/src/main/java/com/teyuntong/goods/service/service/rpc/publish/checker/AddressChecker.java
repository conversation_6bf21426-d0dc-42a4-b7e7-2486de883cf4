package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 加价校验
 *
 * <AUTHOR>
 * @since 2025/03/09 16:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddressChecker {

    /**
     * 校验发货地址
     *
     * @param publishBO
     */
    public void checkPublishAddress(PublishBO publishBO) {
        if (StringUtils.isNotBlank(publishBO.getStartDetailAdd()) && publishBO.getStartDetailAdd().length() > 200) {
            throw BusinessException.createException(GoodsErrorCode.START_POINT_ERROR.getCode(), "装货地地址过长，请更换附近其他地址");
        }
        if (StringUtils.isNotBlank(publishBO.getDestDetailAdd()) && publishBO.getDestDetailAdd().length() > 200) {
            throw BusinessException.createException(GoodsErrorCode.START_POINT_ERROR.getCode(), "卸货地地址过长，请更换附近其他地址");
        }
        if (publishBO.getStartCoordX() == null || publishBO.getStartCoordX().compareTo(BigDecimal.ZERO) == 0
                || publishBO.getStartCoordY() == null || publishBO.getStartCoordY().compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException(GoodsErrorCode.START_POINT_ERROR);
        }
        if (publishBO.getDestCoordX() == null || publishBO.getDestCoordX().compareTo(BigDecimal.ZERO) == 0
                || publishBO.getDestCoordY() == null || publishBO.getDestCoordY().compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException(GoodsErrorCode.DEST_POINT_ERROR);

        }
    }


}
