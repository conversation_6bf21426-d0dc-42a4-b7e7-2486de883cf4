package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 价格模式 0:不限；1:有价电议；2:无价电议；3:一口价
 *
 * <AUTHOR>
 * @since 2025-07-17 20:08
 */
@Getter
@AllArgsConstructor
public enum PricePublishTypeEnum {

    NO_LIMIT(0, "不限"),
    HAVE_PRICE_TEL(1, "有价电议"),
    NO_PRICE_TEL(2, "无价电议"),
    FIX_PRICE(3, "一口价")
    ;
    private Integer code;
    private String name;


    public static String getName(Integer code) {
        for (PricePublishTypeEnum e : PricePublishTypeEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e.getName();
            }
        }
        return "";
    }
}
