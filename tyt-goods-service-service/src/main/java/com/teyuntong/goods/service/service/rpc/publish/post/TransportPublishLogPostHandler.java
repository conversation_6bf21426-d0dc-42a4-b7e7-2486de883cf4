package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.goods.service.client.transport.vo.ChangePriceLogReq;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportPublishLogDO;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPublishLogService;
import com.teyuntong.goods.service.service.biz.refresh.mybatis.entity.GoodsRefreshManualLogDO;
import com.teyuntong.goods.service.service.biz.refresh.service.GoodsRefreshManualLogService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.PublishTransportDataSnapDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.PublishTransportDataSnapService;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货源发布记录
 *
 * <AUTHOR>
 * @since 2025/03/24 14:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportPublishLogPostHandler {

    private final TransportPublishLogService transportPublishLogService;
    private final ThPriceRpcService thPriceRpcService;
    private final PublishTransportDataSnapService publishTransportDataSnapService;
    private final GoodsRefreshManualLogService goodsRefreshManualLogService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        TransportMainDO transportMain = processBO.getTransportMain();
        TransportMainDO oldMain = processBO.getOldMain();
        PublishOptEnum optEnum = processBO.getOptEnum();

        TransportPublishLogDO publishLog = new TransportPublishLogDO();
        publishLog.setUserId(transportMain.getUserId());
        publishLog.setSrcMsgId(transportMain.getSrcMsgId());
        publishLog.setPubType(PubTypeEnum.getByCode(optEnum));
        publishLog.setPubTime(new Date());
        publishLog.setRequestSource(null);
        transportPublishLogService.save(publishLog);

        PublishTransportDataSnapDO publishTransportDataSnapDO = new PublishTransportDataSnapDO();
        makePublishTransportDataSnapDO(transportMain, publishTransportDataSnapDO);
        publishTransportDataSnapService.save(publishTransportDataSnapDO);

        // 记录价格变更日志
        ChangePriceLogReq changePriceLogReq = new ChangePriceLogReq();
        changePriceLogReq.setSrcMsgId(transportMain.getSrcMsgId());
        changePriceLogReq.setPrice(TransportUtil.hasPrice(transportMain.getPrice()) ?
                new BigDecimal(transportMain.getPrice()) : BigDecimal.ZERO);
        changePriceLogReq.setPublishType(transportMain.getPublishType());
        changePriceLogReq.setOperationType(OperationTypeEnum.getByCode(optEnum));
        thPriceRpcService.changePirceLog(changePriceLogReq);

        // 记录操作日志
        GoodsRefreshManualLogDO logDO = new GoodsRefreshManualLogDO();
        logDO.setSrcMsgId(transportMain.getSrcMsgId());
        logDO.setUserId(transportMain.getUserId());
        logDO.setItemValue(0);
        logDO.setPriceBefore(oldMain == null ? "" : oldMain.getPrice());
        logDO.setPriceAfter(transportMain.getPrice());
        logDO.setPublishTypeBefore(oldMain == null ? null : oldMain.getPublishType());
        logDO.setPublishTypeAfter(transportMain.getPublishType());
        goodsRefreshManualLogService.save(logDO);
    }

    private void makePublishTransportDataSnapDO(TransportMainDO transportMain, PublishTransportDataSnapDO publishTransportDataSnapDO) {
        publishTransportDataSnapDO.setSrcMsgId(transportMain.getSrcMsgId());
        publishTransportDataSnapDO.setUserId(transportMain.getUserId());
        publishTransportDataSnapDO.setStartProvinc(transportMain.getStartProvinc());
        publishTransportDataSnapDO.setStartCity(transportMain.getStartCity());
        publishTransportDataSnapDO.setStartArea(transportMain.getStartArea());
        publishTransportDataSnapDO.setStartDetailAdd(transportMain.getStartDetailAdd());
        publishTransportDataSnapDO.setStartLongitude(transportMain.getStartLongitudeValue() == null ? null : transportMain.getStartLongitudeValue());
        publishTransportDataSnapDO.setStartLatitude(transportMain.getStartLatitudeValue() == null ? null : transportMain.getStartLatitudeValue());
        publishTransportDataSnapDO.setStartCoordX(transportMain.getStartCoordXValue() == null ? null : transportMain.getStartCoordXValue());
        publishTransportDataSnapDO.setStartCoordY(transportMain.getStartCoordYValue() == null ? null : transportMain.getStartCoordYValue());
        publishTransportDataSnapDO.setStartPoint(transportMain.getStartPoint());
        publishTransportDataSnapDO.setDestProvinc(transportMain.getDestProvinc());
        publishTransportDataSnapDO.setDestCity(transportMain.getDestCity());
        publishTransportDataSnapDO.setDestArea(transportMain.getDestArea());
        publishTransportDataSnapDO.setDestDetailAdd(transportMain.getDestDetailAdd());
        publishTransportDataSnapDO.setDestLongitude(transportMain.getDestLongitudeValue() == null ? null : transportMain.getDestLongitudeValue());
        publishTransportDataSnapDO.setDestLatitude(transportMain.getDestLatitudeValue() == null ? null : transportMain.getDestLatitudeValue());
        publishTransportDataSnapDO.setDestCoordX(transportMain.getDestCoordXValue() == null ? null : transportMain.getDestCoordXValue());
        publishTransportDataSnapDO.setDestCoordY(transportMain.getDestCoordYValue() == null ? null : transportMain.getDestCoordYValue());
        publishTransportDataSnapDO.setDestPoint(transportMain.getDestPoint());
        publishTransportDataSnapDO.setTaskContent(transportMain.getTaskContent());
        publishTransportDataSnapDO.setMatchItemId(transportMain.getMatchItemId() == null ? null : String.valueOf(transportMain.getMatchItemId()));
        publishTransportDataSnapDO.setType(transportMain.getType());
        publishTransportDataSnapDO.setBrand(transportMain.getBrand());
        publishTransportDataSnapDO.setGoodTypeName(transportMain.getGoodTypeName());
        publishTransportDataSnapDO.setWeight(transportMain.getWeight());
        publishTransportDataSnapDO.setLength(transportMain.getLength());
        publishTransportDataSnapDO.setWide(transportMain.getWide());
        publishTransportDataSnapDO.setHigh(transportMain.getHigh());
    }
}

@Getter
@AllArgsConstructor
enum PubTypeEnum {
    CONFIRM_PUBLISH(1, "确认发布"),
    DIRECT_PUBLISH(2, "直接发布"),
    ADD_PRICE(3, "加价"),
    TRANSFER_FIX_PRICE(4, "转一口价"),
    FILL_PRICE(5, "填价"),
    REPUBLISH(6, "曝光"),
    UPDATE_INFO(7, "更新货源信息"),
    ;
    private final Integer code;
    private final String name;

    public static Integer getByCode(PublishOptEnum optEnum) {
        if (optEnum == PublishOptEnum.DIRECT) {
            return DIRECT_PUBLISH.code;
        } else if (optEnum == PublishOptEnum.FILL_PRICE) {
            return FILL_PRICE.code;
        } else if (optEnum == PublishOptEnum.ADD_PRICE) {
            return ADD_PRICE.code;
        } else if (optEnum == PublishOptEnum.TRANSFER_FIXED || optEnum == PublishOptEnum.TRANSFER_TELE) {
            return TRANSFER_FIX_PRICE.code;
        } else if (optEnum == PublishOptEnum.REPUBLISH) {
            return REPUBLISH.code;
        } else if (optEnum == PublishOptEnum.UPDATE_INFO) {
            return UPDATE_INFO.code;
        } else if (optEnum == PublishOptEnum.PUBLISH || optEnum == PublishOptEnum.EDIT) {
            return CONFIRM_PUBLISH.code;
        } else {
            return 0;
        }
    }
}

@Getter
@AllArgsConstructor
enum OperationTypeEnum {
    // 操作入口 1:编辑发布；2:直接发布；3:填价、加价；4:转一口价、转电议；5:拒绝报价修改运费；6:货源诊断
    CONFIRM_PUBLISH(1, "编辑发布"),
    DIRECT_PUBLISH(2, "直接发布"),
    ADD_PRICE(3, "填价、加价"),
    TRANSFER_FIX_PRICE(4, "转一口价、转电议"),
    REFUSE_QUOTE(5, "拒绝报价修改运费"),
    DIAGNOSIS(6, "货源诊断"),
    ;
    private final Integer code;
    private final String name;

    public static Integer getByCode(PublishOptEnum optEnum) {
        if (optEnum == PublishOptEnum.DIRECT) {
            return DIRECT_PUBLISH.code;
        } else if (optEnum == PublishOptEnum.FILL_PRICE) {
            return ADD_PRICE.code;
        } else if (optEnum == PublishOptEnum.ADD_PRICE) {
            return ADD_PRICE.code;
        } else if (optEnum == PublishOptEnum.TRANSFER_FIXED || optEnum == PublishOptEnum.TRANSFER_TELE) {
            return TRANSFER_FIX_PRICE.code;
        } else if (optEnum == PublishOptEnum.PUBLISH || optEnum == PublishOptEnum.EDIT) {
            return CONFIRM_PUBLISH.code;
        } else {
            return 0;
        }
    }
}