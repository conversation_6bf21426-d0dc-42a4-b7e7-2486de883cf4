package com.teyuntong.goods.service.service.common.enums;

import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 优车定价货源业务逻辑专用货源类型
 */
@Getter
@AllArgsConstructor
public enum ExcellentTransportPublishTypeEnum {
    NOPRICE(1, "电议无价"),
    HAVEPRICE(2, "电议有价"),
    FIXED(3, "一口价"),
    ;

    private final Integer code;
    private final String zhName;

}
