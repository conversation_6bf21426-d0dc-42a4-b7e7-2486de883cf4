package com.teyuntong.goods.service.service.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @since 2024/5/14 18:08
 */
@Configuration
public class ThreadPoolConfig {

    @Bean(name = "threadPoolExecutor")
    public ExecutorService systemFixedExecutor() {
        return new ThreadPoolExecutor(4, 10,
                30L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(100), Executors.defaultThreadFactory());

    }

}
