package com.teyuntong.goods.service.service.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class IKAnalyzerUtils {

    private final ThreadLocal<IKSegmenter> segmenter = ThreadLocal.withInitial(() ->
            new IKSegmenter(new StringReader(""), true)
    );

    public List<String> analyze(String text) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(text)) {
            return result;
        }
        StringReader reader = null;
        try {
            text = text.replaceAll("[\\s\\p{Punct}]+", "");
            if (StringUtils.isBlank(text)) {
                return result;
            }
            reader = new StringReader(text);
            IKSegmenter ik = segmenter.get();
            ik.reset(reader);
            Lexeme lexeme;
            while ((lexeme = ik.next()) != null) {
                result.add(lexeme.getLexemeText());
            }
        } catch (IOException e) {
            log.error("分词失败", e);
        } finally {
            if (reader != null){
                reader.close();
            }
        }

        return result;
    }

    @PostConstruct
    public void init() {
        try {
            this.analyze("初始化加载");
        } catch (Exception e) {
            log.error("初始化IKAnalyzer失败", e);
        }
    }


}