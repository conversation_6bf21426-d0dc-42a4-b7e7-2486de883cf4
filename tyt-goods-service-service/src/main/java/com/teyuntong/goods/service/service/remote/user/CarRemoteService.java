package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.car.service.CarRpcService;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "CarRemoteService",
        fallbackFactory = CarRemoteService.CarServiceRemoteFallbackFactory.class)
public interface CarRemoteService extends CarRpcService {

    @Component
    class CarServiceRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CarRemoteService>{
        protected CarServiceRemoteFallbackFactory() {
            super(true, CarRemoteService.class);
        }
    }
}