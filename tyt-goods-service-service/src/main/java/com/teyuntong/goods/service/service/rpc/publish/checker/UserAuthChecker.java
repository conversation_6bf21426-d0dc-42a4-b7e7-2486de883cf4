package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.service.common.enums.ClientSignEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.NoticePopupTemplRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.USER_MAX_PUBLISH_NUM;
import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.MONTH_PUBLISH_NUM;
import static com.teyuntong.goods.service.service.common.enums.PublishStyleEnum.TODAY_PUBLISH;

/**
 * 用户认证校验checker
 *
 * <AUTHOR>
 * @since 2025/02/21 14:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthChecker {

    private final NoticePopupTemplRemoteService noticePopupTemplRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final RedisUtil redisUtil;

    /**
     * 校验用户发布权限
     */
    public void checkDirectPublish(DirectPublishProcessBO processBO) {
        // 非历史货源直接发布，可以不校验用户权益
        if (!processBO.getDirectPublishBO().isHistoryGoods()) {
            return;
        }

        // 实名认证不校验
        UserRpcVO user = processBO.getUser();
        if (Objects.equals(user.getVerifyPhotoSign(), YesOrNoEnum.YES.getId())) {
            return;
        }

        // 未实名认证，不超过发货次数，可以发货
        Integer usedPublishNum = Optional.ofNullable(redisUtil.getInt(MONTH_PUBLISH_NUM + user.getId())).orElse(0);
        Integer maxPublishNum = tytConfigRemoteService.getIntValue(USER_MAX_PUBLISH_NUM, 3);
        // 本月发布次数超过限制，不允许发布
        if (usedPublishNum < maxPublishNum) {
            return;
        }

        // 否则提示用户未实名认证
        NoticePopupTemplVo noticePopupTemplVo;
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        if (Objects.equals(user.getVerifyPhotoSign(), 2)) {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.身份认证中提示);
        } else if (ClientSignEnum.isValid(baseParam == null ? null : baseParam.getClientSign())) {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.未实名认证);
        } else {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.未认证身份);
        }
        throw new BusinessException(GoodsErrorCode.USER_AUTH_NO_IDENTITY, noticePopupTemplVo);
    }

    public void checkUserAuth(PublishBO publishBO, UserRpcVO user) {
        // 如果这个货源是当天已经发布过的，再次编辑发布时不再校验是否认证
        if (Objects.equals(publishBO.getPublishStyle(), TODAY_PUBLISH.getCode())) {
            return;
        }
        // 未实名认证
        if (!Objects.equals(user.getVerifyPhotoSign(), YesOrNoEnum.YES.getId())) {
            Integer usePublishNum = redisUtil.getInt(MONTH_PUBLISH_NUM + user.getId());
            Integer maxPublishNum = tytConfigRemoteService.getIntValue(USER_MAX_PUBLISH_NUM, 3);
            int userSubPublishNum = Objects.isNull(usePublishNum) ? 0 : usePublishNum;
            // 本月发布次数超过限制，不允许发布
            if (userSubPublishNum >= maxPublishNum) {
                throw new BusinessException(GoodsErrorCode.USER_AUTH_NO_IDENTITY);
            }
        }

    }
}
