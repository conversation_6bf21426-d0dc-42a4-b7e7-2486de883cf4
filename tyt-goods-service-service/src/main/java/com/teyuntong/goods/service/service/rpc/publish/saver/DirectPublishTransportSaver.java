package com.teyuntong.goods.service.service.rpc.publish.saver;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.common.utils.TytStrUtil;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/02/21 19:25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DirectPublishTransportSaver {

    private final TransportMainService transportMainService;
    private final TransportMainExtendService transportMainExtendService;
    private final TransportService transportService;
    private final TransportExtendService transportExtendService;

    @Transactional(rollbackFor = Exception.class)
    public void saveDirect(DirectPublishProcessBO processBO) {
        TransportMainDO newMainDO = processBO.getTransportMain();
        if (processBO.getDirectPublishBO().isHistoryGoods()) {

            // 保存main表
            newMainDO.setId(null);
            transportMainService.save(newMainDO);

            // 设置srcId为新值
            newMainDO.setSrcMsgId(newMainDO.getId());
            // 更新相似货源信息及srcMsgId
            this.updateSimilarityCode(newMainDO, processBO.getMainExtend());

            // 保存main扩展表
            TransportMainExtendDO newMainExtendDO = processBO.getMainExtend();
            newMainExtendDO.setSrcMsgId(newMainDO.getId());
            newMainExtendDO.setSeckillGoods(transportExtendService.judgeSeckillGoods(
                    processBO.getIsSeckillGoods(), newMainDO.getSrcMsgId()));
            transportMainExtendService.save(newMainExtendDO);

            // 保存transport表
            TransportDO newTransportDO = TransportPublishConverter.INSTANCE.fromMainDO(newMainDO);
            newTransportDO.setCtime(new Date());
            newTransportDO.setSort(0L);
            transportService.save(newTransportDO);

            // 不再更新transport表sort字段，goods-search处理sort=id
            // newTransportDO.setSort(newTransportDO.getId());
            // transportService.updateById(newTransportDO);

            // 保存transport扩展表
            TransportExtendDO newTransportExtendDO = TransportPublishConverter.INSTANCE.fromMainExtendDO(newMainExtendDO);
            newTransportExtendDO.setTsId(newTransportDO.getId());
            newTransportExtendDO.setTopFlag(2); // 直接发布
            newTransportExtendDO.setCreateTime(newTransportDO.getCtime());
            transportExtendService.save(newTransportExtendDO);

            // 记录transport，后面post handler会用到
            processBO.setTransport(newTransportDO);
        } else {

            // 更新main表
            transportMainService.updateById(newMainDO);

            // 更新相似货源信息及srcMsgId
            this.updateSimilarityCode(newMainDO, processBO.getMainExtend());

            // 更新main扩展表
            TransportMainExtendDO newMainExtendDO = processBO.getMainExtend();
            newMainExtendDO.setSeckillGoods(transportExtendService.judgeSeckillGoods(
                    processBO.getIsSeckillGoods(), newMainDO.getSrcMsgId()));
            transportMainExtendService.updateById(newMainExtendDO);

            // save transport
            if (processBO.getDirectPublishBO().isTopFlag()) { // 置顶

                // 保存transport表
                TransportDO newTransportDO = TransportPublishConverter.INSTANCE.fromMainDO(newMainDO);
                newTransportDO.setCtime(new Date());
                newTransportDO.setSort(0L);
                transportService.save(newTransportDO);

                // 不再更新transport表sort字段，goods-search处理sort=id
                // newTransportDO.setSort(newTransportDO.getId());
                // transportService.updateById(newTransportDO);

                // 保存transport扩展表
                boolean useExposure = processBO.getDirectPublishBO().isUseExposure();
                TransportExtendDO newTransportExtendDO = TransportPublishConverter.INSTANCE.fromMainExtendDO(newMainExtendDO);
                newTransportExtendDO.setTsId(newTransportDO.getId());
                newTransportExtendDO.setTopFlag(useExposure ? 3 : 1); // 曝光卡
                newTransportExtendDO.setCreateTime(newTransportDO.getCtime());
                transportExtendService.save(newTransportExtendDO);

                // 记录transport，后面post handler会用到
                processBO.setTransport(newTransportDO);
            } else {

                // 更新transport表
                TransportDO lastTransportDO = transportService.getLastBySrcMygId(newMainDO.getSrcMsgId());
                BeanUtils.copyProperties(newMainDO, lastTransportDO, "id", "sort", "tsOrderNo", "ctime", "releaseTime");
                transportService.updateById(lastTransportDO);

                // 更新transport扩展表
                TransportExtendDO lastTransportExtendDO = transportExtendService.getByTsId(lastTransportDO.getId());
                BeanUtils.copyProperties(newMainExtendDO, lastTransportExtendDO, "id", "tsId", "createTime");
                transportExtendService.updateById(lastTransportExtendDO);

                // 记录transport，后面post handler会用到
                processBO.setTransport(lastTransportDO);
            }

        }
    }

    /**
     * 更新Main表相似货源信息
     *
     * @param transportMainDO
     */
    private void updateSimilarityCode(TransportMainDO transportMainDO, TransportMainExtendDO mainExtendDO) {
        // 相似货源是否折叠，如果价格超过优车最低价，就不折叠
        boolean foldSwitch = TransportUtil.isExcellentPrice(transportMainDO.getPrice(), mainExtendDO.getSuggestMinPrice());
        String similarityCode = transportMainService.genSimilarityCode(transportMainDO, foldSwitch);
        TransportMainDO firstSimilarityGoods = transportMainService.getFirstSimilarityGoods(similarityCode);

        Long similarityFirstId;
        String similarityFirstInfo;
        if (firstSimilarityGoods == null) {
            similarityFirstId = transportMainDO.getSrcMsgId();
            similarityFirstInfo = StringUtils.substring(transportMainDO.getTaskContent(), 0, 10) + "##"
                    + TytStrUtil.hidePhone(transportMainDO.getNickName()) + "##"
                    + transportMainDO.getUserType() + "##"
                    + transportMainDO.getIsInfoFee() + "##"
                    + transportMainDO.getRegTime().getTime();
        } else {
            similarityFirstId = firstSimilarityGoods.getSimilarityFirstId();
            similarityFirstInfo = firstSimilarityGoods.getSimilarityFirstInfo();
        }

        transportMainDO.setSimilarityCode(similarityCode);
        transportMainDO.setSimilarityFirstId(similarityFirstId);
        transportMainDO.setSimilarityFirstInfo(similarityFirstInfo);

        // 更新相似货源信息，也包含了更新srcMsgId
        transportMainService.updateById(transportMainDO);
    }

}
