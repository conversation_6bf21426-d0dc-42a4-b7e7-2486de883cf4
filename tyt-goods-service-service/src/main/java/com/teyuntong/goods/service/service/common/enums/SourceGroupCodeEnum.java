package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 货源标签code
 *
 * <AUTHOR>
 * @date 2022/8/26 15:59
 */
@Getter
public enum SourceGroupCodeEnum {

    REMARK("transport_label", "备注"),
    CAR_LENGTH("car_length_label", "长度标签"),
    TAIL_CAR_STYLE("tail_car_style", "挂车样式"),
    TAIL_CAR_TYPE("tail_car_type", "挂车型号"),
    ;
    private String groupCode;

    private String zhName;

    SourceGroupCodeEnum(String groupCode, String zhName) {
        this.groupCode = groupCode;
        this.zhName = zhName;
    }

}
