package com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarInfoGpscheatDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 专车司机虚拟定位表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-08-28
 */
@Mapper
public interface SigningCarInfoGpscheatMapper extends BaseMapper<SigningCarInfoGpscheatDO> {

    /**
     * 保存虚拟坐标信息
     *
     * @param carInfoId 车辆信息ID
     * @param coordX 坐标x
     * @param coordY 坐标y
     * @param detailAdd 详细地址
     * @param longitude 经度
     * @param latitude 纬度
     * @param provinc 省
     * @param city 市
     * @param area 区
     */
    void saveGpscheat(@Param("carInfoId") Long carInfoId,
                      @Param("coordX") String coordX,
                      @Param("coordY") String coordY,
                      @Param("detailAdd") String detailAdd,
                      @Param("longitude") String longitude,
                      @Param("latitude") String latitude,
                      @Param("provinc") String provinc,
                      @Param("city") String city,
                      @Param("area") String area);

    void deleteGpscheat(@Param("carInfoId") Long carInfoId,
                        @Param("coordX") String coordX,
                        @Param("coordY") String coordY,
                        @Param("detailAdd") String detailAdd,
                        @Param("longitude") String longitude,
                        @Param("latitude") String latitude,
                        @Param("provinc") String provinc,
                        @Param("city") String city,
                        @Param("area") String area);

    void deleteGpscheatById(@Param("id") Long id);

    Long getGpscheatLimit1(@Param("carInfoId") Long carInfoId,
                        @Param("coordX") String coordX,
                        @Param("coordY") String coordY,
                        @Param("detailAdd") String detailAdd,
                        @Param("longitude") String longitude,
                        @Param("latitude") String latitude,
                        @Param("provinc") String provinc,
                        @Param("city") String city,
                        @Param("area") String area);

    List<SigningCarInfoGpscheatDO> getGpscheatByCarInfoId(@Param("carInfoId") Long carInfoId);

    void deleteAllGpscheat();

}
