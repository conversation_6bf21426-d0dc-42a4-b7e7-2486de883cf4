package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 适用货源枚举 ：1:专车(废弃)；2:普货；3:优车1.0；4:优车2.0；5:运满满；6:专车(非平台-签约合作商)；7专车(平台)
 *
 * <AUTHOR>
 * @since 2025-07-17 19:41
 */
@Getter
@AllArgsConstructor
public enum ApplyTransportTypeEnum {
    NORMAL(2, "普货"),
    EXCELLENT_1(3, "优车1.0"),
    EXCELLENT_2(4, "优车2.0"),
    YMM(5, "运满满"),
    SPECIAL_NON_PLAT(6, "专车(非平台-签约合作商)"),
    SPECIAL_PLAT(7, "专车(平台)")
    ;
    private Integer code;
    private String name;

    /**
     * 根据编码获取名称
     *
     * @param code
     * @return
     */
    public static String getName(Integer code) {
        for (ApplyTransportTypeEnum e : ApplyTransportTypeEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e.getName();
            }
        }
        return "";
    }
}
