package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.service.TransportEnterpriseLogRpcService;
import com.teyuntong.goods.service.client.transport.vo.InvoiceXHLConsigneeDataVO;
import com.teyuntong.goods.service.client.transport.vo.TransportEnterpriseLogVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportEnterpriseLogDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportEnterpriseLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequiredArgsConstructor
public class TransportEnterpriseLogRpcServiceImpl implements TransportEnterpriseLogRpcService {

    private final TransportEnterpriseLogService transportEnterpriseLogService;


    @Override
    public TransportEnterpriseLogVO getTransportEnterpriseLogBySrcMsgId(Long srcMsgId) {
        TransportEnterpriseLogDO transportEnterpriseLogBySrcMsgId = transportEnterpriseLogService.getBySrcMsgId(srcMsgId);
        TransportEnterpriseLogVO transportEnterpriseLogVO = new TransportEnterpriseLogVO();
        if (transportEnterpriseLogBySrcMsgId == null) {
            return null;
        }
        BeanUtils.copyProperties(transportEnterpriseLogBySrcMsgId, transportEnterpriseLogVO);
        return transportEnterpriseLogVO;
    }

    @Override
    public List<InvoiceXHLConsigneeDataVO> getXHLConsigneeData(Long userId) {
        return transportEnterpriseLogService.getXHLConsigneeData(userId);
    }
}
