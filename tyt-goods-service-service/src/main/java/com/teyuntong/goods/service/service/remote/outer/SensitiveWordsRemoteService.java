package com.teyuntong.goods.service.service.remote.outer;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.sensitivewords.SensitiveWordsRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "SensitiveWordsRpcService", fallbackFactory = SensitiveWordsRemoteService.SensitiveWordsRemoteServiceFallbackFactory.class)
public interface SensitiveWordsRemoteService extends SensitiveWordsRpcService {


    @Component
    class SensitiveWordsRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SensitiveWordsRemoteService> {
        protected SensitiveWordsRemoteServiceFallbackFactory() {
            super(true, SensitiveWordsRemoteService.class);
        }
    }
}
