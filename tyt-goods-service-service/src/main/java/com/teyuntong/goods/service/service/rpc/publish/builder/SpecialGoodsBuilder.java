package com.teyuntong.goods.service.service.rpc.publish.builder;

import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.client.publish.dto.CalculatePriceDTO;
import com.teyuntong.goods.service.service.biz.record.mybatis.entity.GoodsAddressLevelRecordDO;
import com.teyuntong.goods.service.service.biz.record.service.GoodsAddressLevelRecordService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.RefundFlagEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 专车货源构建器
 *
 * <AUTHOR>
 * @since 2025/02/21 16:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpecialGoodsBuilder {

    private final GoodsAddressLevelRecordService goodsAddressLevelRecordService;
    private final CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;

    /**
     * 专车发货需要重新计算运费
     */
    public void redirectBuild(DirectPublishProcessBO processBO) {
        TransportMainDO newMainDO = processBO.getTransportMain();
        TransportMainExtendDO mainExtend = processBO.getMainExtend();
        TransportMainDO oldMain = processBO.getOldMain();

        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(newMainDO.getExcellentGoods())) {
            CalculatePriceDTO priceDTO = new CalculatePriceDTO();
            priceDTO.setCargoOwnerId(newMainDO.getCargoOwnerId());
            priceDTO.setUserId(newMainDO.getUserId());
            priceDTO.setStartCity(newMainDO.getStartCity());
            priceDTO.setDestCity(newMainDO.getDestCity());
            priceDTO.setDriverDriving(newMainDO.getDriverDriving());
            priceDTO.setWeight(newMainDO.getWeight());
            if (Objects.equals(newMainDO.getRefundFlag(), RefundFlagEnum.NO_RETURN.getCode())) {
                priceDTO.setInfoFee(newMainDO.getInfoFee());
            }
            // 从五级地址表获取距离
            GoodsAddressLevelRecordDO distanceRecordDO = goodsAddressLevelRecordService.getBySrcMsgId(oldMain.getSrcMsgId());
            if (Objects.isNull(distanceRecordDO)) {
                throw new BusinessException(GoodsErrorCode.NO_GET_GOODS_DISTANCE);
            }
            priceDTO.setDistanceKilometer(distanceRecordDO.getDistanceKilometer());
            priceDTO.setOtherFee(distanceRecordDO.getOtherFee());

            priceDTO.setUseCarType(mainExtend.getUseCarType());
            priceDTO.setClientVersion(processBO.getBaseParam().getClientVersion());
            CalcSpecialGoodsPriceResultDTO priceResultDTO = calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
            BigDecimal price = priceResultDTO.getPrice();
            if (Objects.equals(price.intValue(), 0)) {
                throw new BusinessException(GoodsErrorCode.CALC_SPECIAL_GOODS_PRICE_FAIL);
            }
            if (PublishOptEnum.isPublish(processBO.getOptEnum())) {
                BigDecimal perkPrice = getPerkPrice(priceResultDTO, mainExtend);
                newMainDO.setPrice(price.toPlainString());
            } else {
                newMainDO.setPrice(newMainDO.getPrice());
            }
        }
    }

    private BigDecimal getPerkPrice(CalcSpecialGoodsPriceResultDTO priceResultDTO, TransportMainExtendDO mainExtend) {
        BigDecimal perkPrice = priceResultDTO.getPerkPrice() == null ? BigDecimal.ZERO : priceResultDTO.getPerkPrice();
        // 获取数据库中原有的 perkPrice（int 类型）
        Integer dbPerkPrice = mainExtend.getPerkPrice();
        // 判断当前计算出的 perkPrice 是否存在且大于0
        boolean hasCurrentPerk = perkPrice.compareTo(BigDecimal.ZERO) > 0;
        // 判断数据库中的 perkPrice 是否存在且大于0
        boolean hasDbPerk = dbPerkPrice != null && dbPerkPrice > 0;

        if (hasCurrentPerk) {
            // 如果当前有优惠，但数据库中没有 或者 金额不一致 → 优惠比例变化
            if (!hasDbPerk || !dbPerkPrice.equals(perkPrice.intValueExact())) {
                throw new BusinessException(GoodsErrorCode.PERK_RATIO_CHANGE);
            }
        } else {
            // 当前无优惠，但数据库中有 → 活动已结束
            if (hasDbPerk) {
                throw new BusinessException(GoodsErrorCode.PERK_ACTIVITY_END);
            }
        }
        return perkPrice;
    }


    /**
     * 专车发货需要重新计算运费
     */
    public void build(PublishProcessBO publishProcessBO, UserRpcVO user) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        // 专车发货计算运费
        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {

            CalculatePriceDTO priceDTO = new CalculatePriceDTO();
            priceDTO.setUserId(user.getId());
            priceDTO.setCargoOwnerId(publishBO.getCargoOwnerId());
            priceDTO.setOtherFee(publishBO.getOtherFee());
            priceDTO.setStartCity(publishBO.getStartCity());
            priceDTO.setDestCity(publishBO.getDestCity());
            priceDTO.setWeight(publishBO.getWeight());
            if (Objects.equals(RefundFlagEnum.NO_RETURN.getCode(), publishBO.getRefundFlag())) {
                priceDTO.setInfoFee(publishBO.getInfoFee());
            }
            priceDTO.setDistanceKilometer(publishBO.getDistanceKilometer());
            priceDTO.setDriverDriving(publishBO.getDriverDriving());
            priceDTO.setUseCarType(publishBO.getUseCarType());
            priceDTO.setClientVersion(publishProcessBO.getBaseParam().getClientVersion());
            CalcSpecialGoodsPriceResultDTO priceResult = calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
            publishBO.setPriceType(priceResult.getPriceType());
            publishBO.setPerkPrice(priceResult.getPerkPrice());
            publishProcessBO.setSpecialGoodsPriceResult(priceResult);

        }
    }

}
