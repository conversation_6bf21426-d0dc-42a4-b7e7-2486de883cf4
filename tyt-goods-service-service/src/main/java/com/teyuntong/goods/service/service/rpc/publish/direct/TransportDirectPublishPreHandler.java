package com.teyuntong.goods.service.service.rpc.publish.direct;

import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.pre.AddTransportVaryPreHandler;
import com.teyuntong.goods.service.service.rpc.publish.pre.HideHistoryTransportPreHandler;
import com.teyuntong.goods.service.service.rpc.publish.pre.RevokeTransportPreHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 直接发布前置处理
 *
 * <AUTHOR>
 * @since 2025/02/11 10:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportDirectPublishPreHandler {

    private final AddTransportVaryPreHandler addTransportVaryPreHandler;
    private final HideHistoryTransportPreHandler hideHistoryTransportPreHandler;
    private final RevokeTransportPreHandler revokeTransportPreHandler;

    /**
     * 校验直接发布主流程
     */
    public void preHandler(DirectPublishProcessBO processBO) {
        // 货源撤销
        revokeTransportPreHandler.handler(processBO);
        // 更新vary表
        addTransportVaryPreHandler.handler(processBO);
        // 更新历史货源的可见状态
        hideHistoryTransportPreHandler.handler(processBO);
    }


}
