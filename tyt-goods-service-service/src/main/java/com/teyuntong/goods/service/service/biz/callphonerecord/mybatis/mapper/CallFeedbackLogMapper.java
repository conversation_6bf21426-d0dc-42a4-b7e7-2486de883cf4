package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallFeedbackLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 拨打反馈页面填写记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Mapper
@DS("tyt")
public interface CallFeedbackLogMapper extends BaseMapper<CallFeedbackLogDO> {

    // 根据srcMsgId和carUserId查询反馈记录
    List<CallFeedbackLogDO> getLastFeedback(@Param("carUserId") Long carUserId, @Param("srcMsgIds") List<Long> srcMsgIds);
}
