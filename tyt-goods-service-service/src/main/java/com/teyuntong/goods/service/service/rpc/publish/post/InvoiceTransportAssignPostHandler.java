package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.client.transport.dto.SaveGoodsStatusDTO;
import com.teyuntong.goods.service.client.transport.service.TransportStatusRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.inner.PlatTransportRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.trade.service.client.orders.dto.AssignOrdersSaveBillDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 开票货源发布时自动指派
 *
 * <AUTHOR>
 * @since 2025/03/07 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceTransportAssignPostHandler {


    private final OrdersRemoteService ordersRemoteService;
    private final TransportStatusRpcService transportStatusRpcService;

    /**
     * 开票货源自动指派车方
     *
     * @param transportMain
     * @param publishBO
     */
    public void invoiceAssignCar(TransportMainDO transportMain, PublishBO publishBO) {
        // 处理指派货源
        try {
            if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId()) && StringUtils.isNotBlank(publishBO.getAssignCarTel())) {
                log.info("开票货源指派车方,srcMsgId:{},userId:{}", transportMain.getSrcMsgId(), transportMain.getUserId());
                AssignOrdersSaveBillDTO assignOrdersSaveBillDTO = new AssignOrdersSaveBillDTO();
                assignOrdersSaveBillDTO.setTsId(transportMain.getSrcMsgId());
                assignOrdersSaveBillDTO.setUserId(transportMain.getUserId());
                assignOrdersSaveBillDTO.setCellPhone(publishBO.getAssignCarTel());
                assignOrdersSaveBillDTO.setAgencyMoney(0L);
                assignOrdersSaveBillDTO.setCarriageFee(StringUtils.isNotBlank(transportMain.getPrice()) ? Integer.parseInt(transportMain.getPrice()) : 0);
                assignOrdersSaveBillDTO.setTecServiceFee(0L);
                assignOrdersSaveBillDTO.setCarOwnerPayType(99);
                WebResult<Object> response = ordersRemoteService.saveWayBill(assignOrdersSaveBillDTO);
                if (!response.ok()) {
                    log.error("开票货源指派车方生成订单失败，准备撤销货源,srcMsgId:{},userId:{}", transportMain.getSrcMsgId(), transportMain.getUserId());
                    String backoutReasonKey = "货主已取消货源";
                    Integer backoutReasonValue = 7;
//                    ResultMsgBean resultMsgBean = platTransportRemoteService.saveGoodsStatusNewForAssignCar(transportMain.getUserId(), transportMain.getSrcMsgId(), backoutReasonKey, backoutReasonValue);
                    SaveGoodsStatusDTO saveGoodsStatusDTO = new SaveGoodsStatusDTO();
                    saveGoodsStatusDTO.setUserId(transportMain.getUserId());
                    saveGoodsStatusDTO.setSrcMsgId(transportMain.getSrcMsgId());
                    saveGoodsStatusDTO.setBackoutReasonKey(backoutReasonKey);
                    saveGoodsStatusDTO.setBackoutReasonValue(backoutReasonValue);
                    try {
                        transportStatusRpcService.setGoodsBackOutNoInteraction(saveGoodsStatusDTO);
                    } catch (Exception e) {
                        //调用履约开票货源指派车方接口，如果失败则调用货源撤销方法撤销掉货源并返回履约接口给的错误msg信息
                        throw new BusinessException(GoodsErrorCode.ASSIGN_CAR_FAILURE);
                    }
                    log.error("开票货源指派车方生成订单失败，撤销货源成功,srcMsgId:{},userId:{}", transportMain.getSrcMsgId(), transportMain.getUserId());
                } else {
                    log.info("开票货源指派车方成功,srcMsgId:{},userId:{}", transportMain.getSrcMsgId(), transportMain.getUserId());
                }
            }
        } catch (Exception e) {
            log.error("开票货源指派车方失败后，主动下架货源操作失败", e);
            throw new BusinessException(GoodsErrorCode.ASSIGN_CAR_FAILURE);
        }
    }
}



