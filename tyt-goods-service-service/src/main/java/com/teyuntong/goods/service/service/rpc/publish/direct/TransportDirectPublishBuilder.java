package com.teyuntong.goods.service.service.rpc.publish.direct;

import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.builder.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发货构建器
 *
 * <AUTHOR>
 * @since 2025/02/18 13:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportDirectPublishBuilder {

    private final BaseTransportBuilder baseTransportBuilder;
    private final UserInfoTransportBuilder userInfoTransportBuilder;
    private final TransportLabelJsonBuilder transportLabelJsonBuilder;
    private final PriorityRecommendExpireBuilder priorityRecommendExpireBuilder;
    private final SpecialGoodsBuilder specialGoodsBuilder;
    private final CommissionTecFeeBuilder commissionTecFeeBuilder;
    private final TransportExtendBuilder transportExtendBuilder;

    /**
     * 校验直接发布主流程
     */
    public void buildChain(DirectPublishProcessBO processBO) {
        // 基础信息
        baseTransportBuilder.build(processBO);
        // 填充扩展表字段
        transportExtendBuilder.build(processBO);
        // 用户信息
        userInfoTransportBuilder.build(processBO);
        // 设置优先推荐过期时间
        priorityRecommendExpireBuilder.build(processBO);
        // 更新labelJson
        transportLabelJsonBuilder.build(processBO);
        // 专车发货需要重新计算运费
        specialGoodsBuilder.redirectBuild(processBO);
        // 设置佣金技术费
        commissionTecFeeBuilder.build(processBO);
    }


}
