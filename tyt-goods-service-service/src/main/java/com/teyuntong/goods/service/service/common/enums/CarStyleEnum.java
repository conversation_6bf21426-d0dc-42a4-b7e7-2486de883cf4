package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * @description 挂车样式
 * <AUTHOR>
 * @date 2023/10/09 16:42
 * @version 1.0
 */
public enum CarStyleEnum {

    FLAT("纯平"),
    HIGH( "高低高"),
    FLAT_BOARD( "平板"),
    HIGH_LOW_BOARD( "高低板"),
    LADDER("爬梯"),
    ;
    @Getter
    private String styleName;

    CarStyleEnum(String styleName) {
        this.styleName = styleName;
    }

}
