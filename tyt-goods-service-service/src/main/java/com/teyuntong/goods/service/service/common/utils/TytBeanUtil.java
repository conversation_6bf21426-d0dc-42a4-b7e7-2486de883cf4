package com.teyuntong.goods.service.service.common.utils;

import com.teyuntong.infra.common.definition.bean.PageData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Bean转化类
 *
 * <AUTHOR>
 * @since 2024/01/17 14:46
 */
@Slf4j
public class TytBeanUtil {

    public TytBeanUtil() {
    }

    public static <S, T> PageData<T> convertPageData(PageData<S> source, Class<T> targetClazz) {
        if (source == null) {
            return null;
        } else {
            PageData<T> tPageData = null;
            try {
                tPageData = new PageData<>();
                BeanUtils.copyProperties(source, tPageData);
                List<S> list = source.getList();
                if (list == null || list.size() == 0) {
                    return tPageData;
                }

                List<T> listTarget = new ArrayList<>();

                for (S r : list) {
                    T t = targetClazz.getDeclaredConstructor().newInstance();
                    BeanUtils.copyProperties(r, t);
                    listTarget.add(t);
                }

                tPageData.setList(listTarget);
            } catch (Exception var8) {
                log.error("PageData转换失败：", var8);
            }

            return tPageData;
        }
    }

    public static <S, T> T convertBean(S source, Class<T> targetClazz) {
        try {
            if (source != null) {
                T t = targetClazz.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, t);
                return t;
            }
        } catch (Exception e) {
            log.error("对象转换失败：", e);
        }
        return null;
    }

    public static <S, T> List<T> convertBeanList(List<S> source, Class<T> targetClazz) {
        List<T> targetList = new ArrayList<>();
        try {
            return listCopy(source, targetClazz, targetList) ? targetList : targetList;
        } catch (Exception var4) {
            log.error("List对象转换失败：", var4);
        }
        return targetList;
    }

    private static <S, T> boolean listCopy(List<S> source, Class<T> targetClazz, List<T> targetList) throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        if (!CollectionUtils.isEmpty(source)) {
            for (S r : source) {
                T t = targetClazz.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(r, t);
                targetList.add(t);
            }
            return false;
        } else {
            return true;
        }
    }

    public static List<Integer> convertLong2IntegerList(List<Long> source) {
        List<Integer> targetList = new ArrayList<>();
        try {
            if (CollectionUtils.isEmpty(source)) {
                return null;
            } else {
                source.forEach((x) -> {
                    int i = x.intValue();
                    targetList.add(i);
                });
                return targetList;
            }
        } catch (Exception e) {
            log.error("List整型转换失败：", e);
            return targetList;
        }
    }

    public static List<Long> convertInteger2LongList(List<Integer> source) {
        List<Long> targetList = new ArrayList<>();

        try {
            if (CollectionUtils.isEmpty(source)) {
                return null;
            } else {
                source.forEach((x) -> {
                    Long i = x.longValue();
                    targetList.add(i);
                });
                return targetList;
            }
        } catch (Exception e) {
            log.error("List整型转换失败i2l", e);
            return targetList;
        }
    }

    public static List<Integer> convertString2IntegerList(List<String> source) {
        List<Integer> targetList = new ArrayList<>();

        try {
            if (CollectionUtils.isEmpty(source)) {
                return null;
            } else {
                source.forEach((x) -> {
                    Integer i = Integer.valueOf(x);
                    targetList.add(i);
                });
                return targetList;
            }
        } catch (Exception e) {
            log.error("List字符型转Integer失败：", e);
            return targetList;
        }
    }

    public static Object mapToObject(Map<Object, Object> map, Class<?> beanClass) throws Exception {
        if (map == null) {
            return null;
        } else {
            Object obj = beanClass.getDeclaredConstructor().newInstance();
            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (map.get(field.getName()) != null || StringUtils.isBlank(MapUtils.getString(map, field.getName()))) {
                    int mod = field.getModifiers();
                    if (!Modifier.isStatic(mod) && !Modifier.isFinal(mod)) {
                        field.setAccessible(true);
                        field.set(obj, map.get(field.getName()));
                    }
                }
            }
            return obj;
        }
    }

    public static Map<String, Object> objectToMap(Object obj) throws Exception {
        if (obj == null) {
            return null;
        } else {
            Map<String, Object> map = new HashMap<>();
            Field[] declaredFields = obj.getClass().getDeclaredFields();
            for (Field field : declaredFields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(obj));
            }
            return map;
        }
    }
}
