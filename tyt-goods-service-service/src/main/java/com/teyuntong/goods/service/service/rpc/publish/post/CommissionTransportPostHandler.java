package com.teyuntong.goods.service.service.rpc.publish.post;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.FreeTecServiceFeeLogDO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TransportTecServiceFeeDO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TransportTecServiceFeeLogDO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TransportTecServiceFeeLogMapper;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TransportTecServiceFeeMapper;
import com.teyuntong.goods.service.service.biz.commission.service.FreeTecServiceFeeLogService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 抽佣货源后置处理handler
 *
 * <AUTHOR>
 * @since 2025/02/23 16:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommissionTransportPostHandler {

    private final TransportTecServiceFeeMapper transportTecServiceFeeMapper;
    private final FreeTecServiceFeeLogService freeTecServiceFeeLogService;
    private final TransportTecServiceFeeLogMapper transportTecServiceFeeLogMapper;

    private final TytConfigRemoteService tytConfigRemoteService;
    private final RedisUtil redisUtil;

    /**
     * 更新抽佣记录
     *
     * @param basePublishProcessBO
     * @param isRecalculateTecServiceFee 是否重新计算抽佣信息
     */
//    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO basePublishProcessBO, boolean isRecalculateTecServiceFee) {

        TransportMainDO transportMain = basePublishProcessBO.getTransportMain();
        TecServiceFeeConfigComputeResult computeResult = basePublishProcessBO.getConfigToComputeResult();
        if (Objects.nonNull(computeResult) && computeResult.getIsMultiCarHavePayOrder()) {
            log.info("抽佣货源数据记录，多车找货货源存在支付成功订单不更新技术服务费。srcMsgId:{}, computeResult:{}", transportMain.getSrcMsgId(), JSONObject.toJSONString(computeResult));
            return;
        }
        //记录抽佣货源信息
        this.saveCommissionTransportTecServiceFeeData(transportMain.getSrcMsgId(), computeResult);
        if (isRecalculateTecServiceFee) {
            if (basePublishProcessBO.isCommissionTransport()) {

                TransportTecServiceFeeLogDO transportTecServiceFeeLogDO = new TransportTecServiceFeeLogDO();
                transportTecServiceFeeLogDO.setSrcMsgId(transportMain.getSrcMsgId());
                transportTecServiceFeeLogDO.setOptionType(1);
                transportTecServiceFeeLogDO.setFromType(1);
                transportTecServiceFeeLogDO.setCreateTime(new Date());
                transportTecServiceFeeLogMapper.insert(transportTecServiceFeeLogDO);
            } else {
                //不抽佣则清除抽佣货源信息
                freeTecServiceFeeLogService.deleteBySrcMsgId(transportMain.getSrcMsgId());
                transportTecServiceFeeMapper.deleteBySrcMsgId(transportMain.getSrcMsgId());

                TransportTecServiceFeeLogDO transportTecServiceFeeLogDO = new TransportTecServiceFeeLogDO();
                transportTecServiceFeeLogDO.setSrcMsgId(transportMain.getSrcMsgId());
                transportTecServiceFeeLogDO.setOptionType(2);
                transportTecServiceFeeLogDO.setFromType(1);
                transportTecServiceFeeLogDO.setCreateTime(new Date());
                transportTecServiceFeeLogMapper.insert(transportTecServiceFeeLogDO);
            }
        }
    }

    private void saveCommissionTransportTecServiceFeeData(Long srcMsgId, TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult) {
        if (srcMsgId == null || tecServiceFeeConfigComputeResult == null) {
            return;
        }

        if (tecServiceFeeConfigComputeResult.getTransportProportionNum() != null) {
            redisUtil.set("commissionProportionTransportNum" + ":" + srcMsgId, String.valueOf(tecServiceFeeConfigComputeResult.getTransportProportionNum()), Duration.ofDays(1));
        }

        Integer defaultTecServiceFee = tytConfigRemoteService.getIntValue("default_tec_service_fee", 50);

        TransportTecServiceFeeDO tytTransportTecServiceFee = new TransportTecServiceFeeDO();
        tytTransportTecServiceFee.setSrcMsgId(srcMsgId);
        tytTransportTecServiceFee.setCreateTime(new Date());
        tytTransportTecServiceFee.setModifyTime(new Date());

        tytTransportTecServiceFee.setApplyTransportType(tecServiceFeeConfigComputeResult.getApplyTransportType());
        tytTransportTecServiceFee.setRefundFlagType(tecServiceFeeConfigComputeResult.getRefundFlagType());
        tytTransportTecServiceFee.setPricePublishType(tecServiceFeeConfigComputeResult.getPricePublishType());
        tytTransportTecServiceFee.setTecServiceFeeMin(tecServiceFeeConfigComputeResult.getTecServiceFeeMin());
        tytTransportTecServiceFee.setTecServiceFeeMax(tecServiceFeeConfigComputeResult.getTecServiceFeeMax());
        tytTransportTecServiceFee.setPriceMin(tecServiceFeeConfigComputeResult.getPriceMin());
        tytTransportTecServiceFee.setPriceMax(tecServiceFeeConfigComputeResult.getPriceMax());
        tytTransportTecServiceFee.setTecServiceFeeRate(tecServiceFeeConfigComputeResult.getTecServiceFeeRate());
        tytTransportTecServiceFee.setDiscountTime(tecServiceFeeConfigComputeResult.getDiscountTime());
        tytTransportTecServiceFee.setDiscount(tecServiceFeeConfigComputeResult.getDiscount());
        tytTransportTecServiceFee.setTransportProportionNum(tecServiceFeeConfigComputeResult.getTransportProportionNum());
        tytTransportTecServiceFee.setDiscountConfig(tecServiceFeeConfigComputeResult.getDiscountConfig());
        tytTransportTecServiceFee.setAllDiscount(tecServiceFeeConfigComputeResult.getAllDiscount());
        tytTransportTecServiceFee.setGoodTransportLabelType(tecServiceFeeConfigComputeResult.getGoodTransportLabelType());
        tytTransportTecServiceFee.setGoodTransportLabel(tecServiceFeeConfigComputeResult.getGoodTransportLabel());
        tytTransportTecServiceFee.setRouteType(tecServiceFeeConfigComputeResult.getRouteType());
        tytTransportTecServiceFee.setStartCity(tecServiceFeeConfigComputeResult.getStartCity());
        tytTransportTecServiceFee.setDestCity(tecServiceFeeConfigComputeResult.getDestCity());
        tytTransportTecServiceFee.setDistanceMin(tecServiceFeeConfigComputeResult.getDistanceMin());
        tytTransportTecServiceFee.setDistanceMax(tecServiceFeeConfigComputeResult.getDistanceMax());

        if (tecServiceFeeConfigComputeResult.getCommissionScore() != null) {
            tytTransportTecServiceFee.setCommissionScore(tecServiceFeeConfigComputeResult.getCommissionScore());
        }
        if (StringUtils.isNotBlank(tecServiceFeeConfigComputeResult.getGoodCarPriceTransportCarryPrice())) {
            tytTransportTecServiceFee.setGoodCarPriceTransportCarryPrice(tecServiceFeeConfigComputeResult.getGoodCarPriceTransportCarryPrice());
        }
        if (Objects.nonNull(tecServiceFeeConfigComputeResult.getCarMemberType()) && tecServiceFeeConfigComputeResult.getCarMemberType() == 0) {
            tytTransportTecServiceFee.setMemberBeforeFee(tecServiceFeeConfigComputeResult.getTecServiceFeeBeforeDiscount());
            tytTransportTecServiceFee.setMemberAfterFee(tecServiceFeeConfigComputeResult.getTecServiceFee());
            tytTransportTecServiceFee.setNoMemberBeforeFee(tecServiceFeeConfigComputeResult.getTecServiceFeeBeforeDiscount());
            tytTransportTecServiceFee.setNoMemberAfterFee(tecServiceFeeConfigComputeResult.getTecServiceFee());
            tytTransportTecServiceFee.setMemberInterestsWord(tecServiceFeeConfigComputeResult.getInterestsWord());
            tytTransportTecServiceFee.setMemberInterestsUrl(tecServiceFeeConfigComputeResult.getInterestsUrl());
            tytTransportTecServiceFee.setNoMemberInterestsWord(tecServiceFeeConfigComputeResult.getInterestsWord());
            tytTransportTecServiceFee.setNoMemberInterestsUrl(tecServiceFeeConfigComputeResult.getInterestsUrl());
            tytTransportTecServiceFee.setMemberShowPrivacyPhoneTab(tecServiceFeeConfigComputeResult.getPrivacyPhoneType());
            tytTransportTecServiceFee.setNoMemberShowPrivacyPhoneTab(tecServiceFeeConfigComputeResult.getPrivacyPhoneType());
            tytTransportTecServiceFee.setMemberFreeType(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeType());
            tytTransportTecServiceFee.setMemberFreeViewCount(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount());
            tytTransportTecServiceFee.setMemberFreeCallCount(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount());
            tytTransportTecServiceFee.setMemberFreeReadyType(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeIsReady() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeIsReady() ? 1 : 0);
            tytTransportTecServiceFee.setMemberFreeReadyTime(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeIsReadyTime());
            tytTransportTecServiceFee.setMemberFreeTime(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime());
            tytTransportTecServiceFee.setNoMemberFreeType(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeType());
            tytTransportTecServiceFee.setNoMemberFreeViewCount(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount());
            tytTransportTecServiceFee.setNoMemberFreeCallCount(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount());
            tytTransportTecServiceFee.setNoMemberFreeReadyType(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeIsReady() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeIsReady() ? 1 : 0);
            tytTransportTecServiceFee.setNoMemberFreeReadyTime(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeIsReadyTime());
            tytTransportTecServiceFee.setNoMemberFreeTime(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime());
            tytTransportTecServiceFee.setMemberUseCommissionStageType(tecServiceFeeConfigComputeResult.getUseCommissionScoreStageConfig() == null || !tecServiceFeeConfigComputeResult.getUseCommissionScoreStageConfig() ? 1 : 2);
            tytTransportTecServiceFee.setNoMemberUseCommissionStageType(tecServiceFeeConfigComputeResult.getUseCommissionScoreStageConfig() == null || !tecServiceFeeConfigComputeResult.getUseCommissionScoreStageConfig() ? 1 : 2);
        }
        if (tytTransportTecServiceFee.getMemberBeforeFee() == null) {
            tytTransportTecServiceFee.setMemberBeforeFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getMemberAfterFee() == null) {
            tytTransportTecServiceFee.setMemberAfterFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getNoMemberBeforeFee() == null) {
            tytTransportTecServiceFee.setNoMemberBeforeFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getNoMemberAfterFee() == null) {
            tytTransportTecServiceFee.setNoMemberAfterFee(new BigDecimal(defaultTecServiceFee));
        }
        if (tytTransportTecServiceFee.getMemberShowPrivacyPhoneTab() == null) {
            tytTransportTecServiceFee.setMemberShowPrivacyPhoneTab(1);
        }
        if (tytTransportTecServiceFee.getNoMemberShowPrivacyPhoneTab() == null) {
            tytTransportTecServiceFee.setNoMemberShowPrivacyPhoneTab(1);
        }
        if (tytTransportTecServiceFee.getMemberFreeType() == null) {
            tytTransportTecServiceFee.setMemberFreeType(1);
        }
        if (tytTransportTecServiceFee.getMemberFreeTime() == null) {
            tytTransportTecServiceFee.setMemberFreeTime(60);
        }
        if (tytTransportTecServiceFee.getNoMemberFreeType() == null) {
            tytTransportTecServiceFee.setNoMemberFreeType(1);
        }
        if (tytTransportTecServiceFee.getNoMemberFreeTime() == null) {
            tytTransportTecServiceFee.setNoMemberFreeTime(60);
        }
        if (tytTransportTecServiceFee.getMemberUseCommissionStageType() == null) {
            tytTransportTecServiceFee.setMemberUseCommissionStageType(1);
        }
        if (tytTransportTecServiceFee.getNoMemberUseCommissionStageType() == null) {
            tytTransportTecServiceFee.setNoMemberUseCommissionStageType(1);
        }
        TransportTecServiceFeeDO bySrcMsgId = transportTecServiceFeeMapper.getBySrcMsgId(srcMsgId);
        if (bySrcMsgId == null) {
            transportTecServiceFeeMapper.insert(tytTransportTecServiceFee);
        } else {
            tytTransportTecServiceFee.setId(bySrcMsgId.getId());
            transportTecServiceFeeMapper.updateById(tytTransportTecServiceFee);
        }

        List<Integer> needFreeTecTypeListResult = new ArrayList<>();
        boolean memberFree = false;
        boolean noMemberFree = false;

        if (CollectionUtils.isNotEmpty(tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList())) {
            needFreeTecTypeListResult.addAll(tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList());
            if (tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList().contains(5)) {
                memberFree = true;
                noMemberFree = true;
            }
        }

        freeTecServiceFeeLogService.deleteBySrcMsgId(srcMsgId);

        FreeTecServiceFeeLogDO tytFreeTecServiceFeeLog = new FreeTecServiceFeeLogDO();
        tytFreeTecServiceFeeLog.setSrcMsgId(srcMsgId);
        tytFreeTecServiceFeeLog.setNewTransportUserFree(0);
        tytFreeTecServiceFeeLog.setCityFree(0);
        tytFreeTecServiceFeeLog.setGoodCarPriceTransportFree(0);
        tytFreeTecServiceFeeLog.setMemberTimeOutFree(0);
        tytFreeTecServiceFeeLog.setNoMemberTimeOutFree(0);
        tytFreeTecServiceFeeLog.setCreateTime(new Date());
        tytFreeTecServiceFeeLog.setModifyTime(new Date());
        if (!needFreeTecTypeListResult.isEmpty()) {
            for (Integer code : needFreeTecTypeListResult) {
                if (code == 1) {
                    tytFreeTecServiceFeeLog.setNewTransportUserFree(1);
                }
                if (code == 3) {
                    tytFreeTecServiceFeeLog.setCityFree(1);
                }
                if (code == 4) {
                    tytFreeTecServiceFeeLog.setGoodCarPriceTransportFree(1);
                }
            }
            if (memberFree) {
                tytFreeTecServiceFeeLog.setMemberTimeOutFree(1);
            }
            if (noMemberFree) {
                tytFreeTecServiceFeeLog.setNoMemberTimeOutFree(1);
            }
        }
        freeTecServiceFeeLogService.save(tytFreeTecServiceFeeLog);
    }

}
