package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 订金类型枚举
 *
 * <AUTHOR>
 * @since 2024-08-23 10:49
 */
@Getter
public enum DepositTypeEnum {
    INTELLIGENT(1, "智能订金"),
    NORMAL(0, "手动订金")
    ;

    /**
     * 通过code获取定金类型枚举
     * 默认返回手动订金
     *
     * @param code
     * @return
     */
    public static DepositTypeEnum getDepositTypeByCode(Integer code) {
        for (DepositTypeEnum typeEnum : DepositTypeEnum.values()) {
            if (Objects.equals(typeEnum.getCode(), code)) {
                return typeEnum;
            }
        }
        return NORMAL;
    }

    private Integer code;
    private String name;
    DepositTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
