package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.transport.vo.PublishNoticeDataVO;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.PublicResourceRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.goods.search.SimilarityGoodsRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserLimitRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.limit.vo.TytExposureBlockRpcVO;
import com.teyuntong.user.service.client.permission.vo.UserExposureInfoVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 曝光卡使用校验，校验权限是否封禁，是否有曝光卡领取权益
 *
 * <AUTHOR>
 * @since 2025/03/09 16:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExposureCardUseChecker {

    private final UserLimitRemoteService userLimitRemoteService;
    private final UserPermissionRemoteService userPermissionRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final PublicResourceRemoteService publicResourceRemoteService;
    private final ExposureCardGiveawayService exposureCardGiveawayService;
    private final ABTestRemoteService abTestRemoteService;
    private final SimilarityGoodsRemoteService similarityGoodsRemoteService;

    public static final String EXPOSURE_CARD_LIMIT_PERPETUAL_MSG = "您（{tel}）因平台管控已被限制曝光卡使用权益。如有疑问请联系客服 400-6688-998";
    public static final String EXPOSURE_CARD_LIMIT_TEMPORARY_MSG = "您（{tel}）因平台管控已被限制曝光卡使用权益，恢复时间{year}年{month}月{day}日{hour}时{min}分。如有疑问请联系客服 400-6688-998";

    /**
     * 曝光卡使用校验
     */
    public void check(DirectPublishProcessBO processBO) {
        if (PublishOptEnum.REPUBLISH.equals(processBO.getOptEnum())) {

            // 融合版校验曝光
            if (processBO.getBaseParam().getClientFusion() != null) {
                // 判断当前货源有相似货源且位于非首位，点击“曝光”，阻断并toast“当前相似货多，无法使用曝光卡”
                String similarityCode = processBO.getOldMain().getSimilarityCode();
                Long srcMsgId = processBO.getDirectPublishBO().getSrcMsgId();
                Boolean goodsIsTop = similarityGoodsRemoteService.goodsIsTop(srcMsgId, similarityCode);
                if (Objects.nonNull(goodsIsTop) && !goodsIsTop) {
                    throw new BusinessException(GoodsErrorCode.REFRESH_NOT_ALLOWED);
                }
            }

            // 判断曝光卡权限是否封禁
            UserRpcVO user = processBO.getUser();
            TytExposureBlockRpcVO blockInfo = userLimitRemoteService.getUserExposureBlockInfo(user.getId());
            if (blockInfo != null && Objects.equals(blockInfo.getBlockStatus(), 1)) {

                String blockResult;
                if (blockInfo.getPermanentBlock()) {
                    blockResult = tytConfigRemoteService.getStringValue(ConfigKeyConstant.EXPOSURE_CARD_USE_LIMIT_PERPETUAL, EXPOSURE_CARD_LIMIT_PERPETUAL_MSG);
                    blockResult = blockResult.replace("{tel}", user.getCellPhone());
                } else {
                    blockResult = tytConfigRemoteService.getStringValue(ConfigKeyConstant.EXPOSURE_CARD_USE_LIMIT_TEMPORARY, EXPOSURE_CARD_LIMIT_TEMPORARY_MSG);
                    LocalDateTime endTime = blockInfo.getBlockEndTime() != null ?
                            new LocalDateTime(blockInfo.getBlockEndTime()) : LocalDateTime.now();
                    blockResult = blockResult.replace("{tel}", user.getCellPhone())
                            .replace("{year}", String.valueOf(endTime.getYear()))
                            .replace("{month}", String.valueOf(endTime.getMonthOfYear()))
                            .replace("{day}", String.valueOf(endTime.getDayOfMonth()))
                            .replace("{hour}", String.valueOf(endTime.getHourOfDay()))
                            .replace("{min}", String.valueOf(endTime.getMinuteOfHour()));
                }
                throw BusinessException.createException(GoodsErrorCode.EXPOSURE_CARD_LIMIT.getCode(), blockResult);
            }

            // 校验成长体系-曝光卡领取权益
            UserExposureInfoVO goodsCouponInfo = userPermissionRemoteService.getGoodsCouponInfo(user.getId());
            if (goodsCouponInfo != null) {
                // 用户无曝光次数，且有未领取的曝光卡，提示用户领取
                if (goodsCouponInfo.getUserExposurePermissionCount() == null || goodsCouponInfo.getUserExposurePermissionCount() == 0) {
                    // 有未领取的曝光卡
                    if (goodsCouponInfo.getUnreceivedExposureCount() != null && goodsCouponInfo.getUnreceivedExposureCount() > 0) {
                        PublishNoticeDataVO noticeDataVO = new PublishNoticeDataVO();
                        noticeDataVO.setType(5);
                        noticeDataVO.setContent("<span style=\"font-size:20px;\">暂无可用曝光卡，您有曝光卡待领取</span>");
                        noticeDataVO.setBtnText("前往领取");
                        noticeDataVO.setBtnLink(publicResourceRemoteService.getByName(ConfigKeyConstant.GOODS_MY_LEVEL_PAGE).getValue());
                        throw new BusinessException(GoodsErrorCode.EXPOSURE_CARD_RECEIVE, noticeDataVO);
                    } else {
                        // 判断是否命中曝光卡赠送规则
                        PublishNoticeDataVO noticeDataVO = new PublishNoticeDataVO();
                        Integer giveawayNum = exposureCardGiveawayService.getGiveawayNum(processBO.getDirectPublishBO().getSrcMsgId());
                        if (giveawayNum != null && giveawayNum > 0) {
                            noticeDataVO.setType(3);
                            noticeDataVO.setContent("<span style=\"font-size:20px;\">该货源线上成交<br>额外送您<span style=\"color:#FF5B00;\">" + giveawayNum + "</span>张曝光卡</span>");
                        } else {
                            noticeDataVO.setType(4);
                        }
                        noticeDataVO.setBtnText("获取曝光卡秘籍");
                        noticeDataVO.setBtnLink(tytConfigRemoteService.getStringValue(ConfigKeyConstant.EXPOSURE_CARD_GIVEAWAY_LINK));
                        throw new BusinessException(GoodsErrorCode.TRANSPORT_TOP_USE_UP, noticeDataVO);
                    }
                }
            }

            //如果用户在曝光卡使用次数限制AB测试中，判断该货源是否已达曝光次数限制，如果达到了就返回特定notice
            checkExposureCardUseLimit(processBO);
        }
    }

    public void checkExposureCardUseLimit(DirectPublishProcessBO processBO) {
        //如果用户在曝光卡使用次数限制AB测试中，判断该货源是否已达曝光次数限制，如果达到了就返回特定notice
        if (Objects.equals(abTestRemoteService.getUserType(AbtestKeyConstant.EXPOSURE_CARD_USE_LIMIT, processBO.getUser().getId()), 1)) {
            int maxUseTimes = exposureCardGiveawayService.getExposureCardMaxUseLimit(processBO);
            int usedTimes = exposureCardGiveawayService.getExposureCardUsedNum(processBO.getDirectPublishBO().getSrcMsgId());
            log.info("曝光卡使用次数限制 srcMsgId:{}, 用户id:{}，maxUseTimes:{}, usedTimes:{}"
                    , processBO.getDirectPublishBO().getSrcMsgId(), processBO.getUser().getId(), maxUseTimes, usedTimes);
            //如果该用户该货源有曝光卡使用次数限制并且已使用次数大于等于最大使用次数
            if (maxUseTimes != -1 && usedTimes >= maxUseTimes) {
                PublishNoticeDataVO noticeDataVO = new PublishNoticeDataVO();
                noticeDataVO.setType(6);
                noticeDataVO.setContent("<span style=\"font-size:20px;\">该货源最高使用<span style=\"color:#FF5B00;\">" + maxUseTimes + "</span>次曝光卡</span>");
                noticeDataVO.setBtnText("我知道了");

                Integer clientSign = LoginHelper.getBaseParam().getClientSign();
                if (clientSign != null && clientSign == 1) {
                    throw BusinessException.createException(GoodsErrorCode.EXPOSURE_CARD_USE_LIMIT.getCode(), "已达使用上限，该货源最高使用" + maxUseTimes + "次曝光卡");
                } else {
                    throw new BusinessException(GoodsErrorCode.EXPOSURE_CARD_USE_LIMIT, noticeDataVO);
                }
            }

            //透传
            if (maxUseTimes > 0) {
                processBO.getDirectPublishBO().setMaxExposureCardUseTimes(maxUseTimes);
                processBO.getDirectPublishBO().setExposureCardUsedTimes(usedTimes);
            }
        }
    }
}