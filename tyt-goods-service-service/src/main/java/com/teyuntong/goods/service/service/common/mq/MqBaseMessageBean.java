package com.teyuntong.goods.service.service.common.mq;

import lombok.Data;

/**
 * mq消息的基类
 *
 * <AUTHOR>
 * @date 2016-11-21下午1:56:04
 * @description
 */
@Data
public class MqBaseMessageBean {
	/*
	 * 消息的类型
	 */
	public int messageType;
	/*
	 * 消息的序列号，每个消息唯一
	 */
	public String messageSerailNum;

	/**
	 * 10010：货源下架MQ消息
	 */
	public static int MB_SYNC_TRANSPORT_WAYBILL_MESSAGE = 10010;


	public int getMessageType() {
		return messageType;
	}

	public void setMessageType(int messageType) {
		this.messageType = messageType;
	}

	public String getMessageSerailNum() {
		return messageSerailNum;
	}

	public void setMessageSerailNum(String messageSerailNum) {
		this.messageSerailNum = messageSerailNum;
	}

	@Override
	public String toString() {
		return "MqBaseMessageBean [messageType=" + messageType + ", messageSerailNum=" + messageSerailNum + "]";
	}
}
