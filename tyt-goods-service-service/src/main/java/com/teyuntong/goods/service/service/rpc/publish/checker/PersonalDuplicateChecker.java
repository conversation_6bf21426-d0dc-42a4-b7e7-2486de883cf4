package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 重货校验
 *
 * <AUTHOR>
 * @since 2025/02/21 16:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalDuplicateChecker {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final TransportMainService transportMainService;

    public void check(BasePublishProcessBO processBO, String assignCarTel) {
        TransportMainDO transportMain;
        String newHashCode;
        if (processBO instanceof PublishProcessBO) {
            transportMain = processBO.getTransportMain();
            // 编辑发布前面已经计算出了hashcode
            newHashCode = transportMain.getHashCode();
        } else if (processBO instanceof DirectPublishProcessBO) {
            transportMain = processBO.getOldMain();
            // 直接发布必须重新计算hashcode，不能使用旧hashcode
            newHashCode = TransportUtil.getNewHashCode(transportMain);
        } else {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }

        // 运满满货源不校验重货
        if (transportMain.getSourceType().equals(SourceTypeEnum.YMM.getCode())) {
            return;
        }
        // 开票货源指派车方货源不校验重货
        if (Objects.equals(transportMain.getInvoiceTransport(), 1) && StringUtils.isNotBlank(assignCarTel)) {
            return;
        }
        // 是否校验重货开关（0关闭；1开启）
        Integer duplicateSwitch = tytConfigRemoteService.getIntValue(ConfigKeyConstant.CHECK_PERSONAL_SIMILARITY, 0);
        if (Objects.equals(duplicateSwitch, 0)) {
            return;
        }

        // 只有直接发布、编辑发布、首发、自动重发校验重货，加价填价转一口价等操作不校验
        if (PublishOptEnum.isPublish(processBO.getOptEnum())) {
            boolean duplicateResult = transportMainService.checkDuplicateTransport(newHashCode, transportMain);
            if (duplicateResult) {
                throw new BusinessException(GoodsErrorCode.TRANSPORT_DUPLICATE);
            }
        }

    }
}
