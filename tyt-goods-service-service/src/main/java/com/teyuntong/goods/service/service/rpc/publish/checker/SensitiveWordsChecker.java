package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.teyuntong.goods.service.service.biz.goodsname.service.NullifyKeywordService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportAutoNullifyDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportAutoNullifyService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.outer.SensitiveWordsRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * 运满满货源校验
 *
 * <AUTHOR>
 * @since 2025/02/21 13:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SensitiveWordsChecker {

    private final NullifyKeywordService nullifyKeywordService;
    private final TransportAutoNullifyService transportAutoNullifyService;
    private final SensitiveWordsRemoteService sensitiveWordsRemoteService;

    /**
     * 发货校验名称和备注敏感词
     *
     * @param publishBO
     * @param user
     */
    public void checkSensitiveWords(PublishBO publishBO, UserRpcVO user) {
        String taskContent = publishBO.getTaskContent();
        String remark = publishBO.getRemark();

        if (publishBO.getInvoiceTransport() != null && publishBO.getInvoiceTransport() == 1 && StringUtils.isNotBlank(remark)) {
            //只有开票货源并且填写了备注才进行接下来的校验
            checkSensitiveWords(user.getId(), "", remark);
        }

        Set<String> nullifyKeyWordsSet = nullifyKeywordService.verifyKeywords(taskContent);
        boolean remarkFalse = false;
        if (CollUtil.isEmpty(nullifyKeyWordsSet)) {
            nullifyKeyWordsSet = nullifyKeywordService.verifyKeywords(remark);
            remarkFalse = true;
        }
        if (CollUtil.isNotEmpty(nullifyKeyWordsSet)) {
            String nullifyKeyWords = StringUtils.join(nullifyKeyWordsSet, ",");
            log.error("发布货源存在敏感词：{}，发布失败！", nullifyKeyWords);
            // 插入到无效货源表
            saveAutoNullifyTransport(publishBO, nullifyKeyWords, user);
            if (remarkFalse) {
                throw new BusinessException(GoodsErrorCode.NULLIFY_KEY_WORDS_REMARK);
            } else {
                throw new BusinessException(GoodsErrorCode.NULLIFY_KEY_WORDS_CONTENT);
            }
        }
    }

    public void checkSensitiveWords(Long userId, String taskContent, String remark) {
        SensitiveWordsVO sensitiveWordsVO = sensitiveWordsRemoteService.newCargoSensitiveCheck(userId, taskContent, remark);
        if (sensitiveWordsVO != null && (sensitiveWordsVO.getMachineRemarkHitSensitiveWords() || sensitiveWordsVO.getTaskContentHitSensitiveWords())) {
            BaseParamDTO baseParam = LoginHelper.getBaseParam();
            if (Objects.nonNull(baseParam)) {
                String clientVersion = baseParam.getClientVersion();
                if (StringUtils.isNotBlank(clientVersion) && NumberUtil.isNumber(clientVersion) && Integer.parseInt(clientVersion) < 6740) {
                    //老版本 app 直接通过 msg 透传
                    StringBuilder msg = new StringBuilder("专票货源：货物信息包含违法/违规敏感词");
                    if (!sensitiveWordsVO.getSensitiveWords().isEmpty()) {
                        for (String sensitiveWord : sensitiveWordsVO.getSensitiveWords()) {
                            msg.append("“").append(sensitiveWord).append("”、");
                        }
                        msg.delete(msg.length() - 1, msg.length());
                    }
                    throw BusinessException.createException(GoodsErrorCode.NULLIFY_KEY_WORDS_REMARK_NEW.getCode(), msg.toString());
                }
            }
            //新版本 app 通过 data 透传
            throw new BusinessException(GoodsErrorCode.NULLIFY_KEY_WORDS_REMARK_NEW, null, sensitiveWordsVO);
        }
    }


    private void saveAutoNullifyTransport(PublishBO publishBO, String nullifyKeyWords, UserRpcVO user) {
        //插入到记录表
        TransportAutoNullifyDO autoNullifyDO = new TransportAutoNullifyDO();
        autoNullifyDO.setTransId(0L);
        autoNullifyDO.setUserId(user.getId());
        autoNullifyDO.setTaskContent(publishBO.getTaskContent());
        autoNullifyDO.setTaskRemark(publishBO.getRemark());
        autoNullifyDO.setMatchingKeyword(nullifyKeyWords);
        autoNullifyDO.setUploadCellphone(publishBO.getTel());
        autoNullifyDO.setPlatId(LoginHelper.getBaseParam().getClientSign());
        autoNullifyDO.setStartPoint(publishBO.getStartPoint());
        autoNullifyDO.setDestPoint(publishBO.getDestPoint());
        autoNullifyDO.setStartProvinc(publishBO.getStartProvinc());
        autoNullifyDO.setStartCity(publishBO.getStartCity());
        autoNullifyDO.setStartArea(publishBO.getStartArea());
        autoNullifyDO.setDestProvinc(publishBO.getDestProvinc());
        autoNullifyDO.setDestCity(publishBO.getDestCity());
        autoNullifyDO.setDestArea(publishBO.getDestArea());
        autoNullifyDO.setIsInfoFee(publishBO.getIsInfoFee());
        autoNullifyDO.setCtime(new Date());
        autoNullifyDO.setMtime(new Date());
        autoNullifyDO.setState(-1);
        transportAutoNullifyService.save(autoNullifyDO);
    }


}
