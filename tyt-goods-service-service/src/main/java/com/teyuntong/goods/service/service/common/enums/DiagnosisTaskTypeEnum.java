package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 货源诊断任务类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-13 14:03
 */
@Getter
@AllArgsConstructor
public enum DiagnosisTaskTypeEnum {
    FILL_PRICE(1, "填价"),
    ADD_PRICE(2, "加价"),
    CHANGE_TO_FIX_PRICE(3, "转一口价"),
    COMPLETE_GOODS_INFO(4, "补全货物信息"),
    REFRESH(5, "曝光"),
    SYNC_YMM(6, "授权同步运满满"),
    ADD_PRICE_ASSISTANT(7, "添加回价助手")
    ;

    private final Integer code;
    private final String name;
}
