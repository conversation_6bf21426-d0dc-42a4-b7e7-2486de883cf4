package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 货源价格，0:全部货源 1：一口价 2：有价电议 3：无价电议
 *
 * <AUTHOR>
 * @since 2024/04/15 16:40
 */
@Getter
@AllArgsConstructor
public enum GoodsPriceTypeEnum {

    ALL(0, "全部货源"),
    FIXED_PRICE(1, "一口价"),
    PRICE_TEL(2, "有价电议"),
    PRICELESS_TEL(3, "无价电议"),
    ;

    private final Integer code;
    private final String name;

    /**
     * 获取枚举类型GoodsPriceTypeEnum的代码和名称的映射关系。
     */
    public static Map<Integer, String> getEnumMap() {
        return Arrays.stream(GoodsPriceTypeEnum.values())
                .collect(Collectors.toMap(
                        GoodsPriceTypeEnum::getCode,
                        GoodsPriceTypeEnum::getName
                ));
    }
}
