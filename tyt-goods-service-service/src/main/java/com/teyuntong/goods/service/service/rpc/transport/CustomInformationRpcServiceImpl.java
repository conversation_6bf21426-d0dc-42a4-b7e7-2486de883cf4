package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.dto.CustomInformationDTO;
import com.teyuntong.goods.service.client.transport.service.CustomInformationRpcService;
import com.teyuntong.goods.service.client.transport.vo.CustomInformationVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.CustomInformationDO;
import com.teyuntong.goods.service.service.biz.transport.service.CustomInformationService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户资料表服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
public class CustomInformationRpcServiceImpl implements CustomInformationRpcService {

    private final CustomInformationService customInformationService;

    @Override
    public CustomInformationVO query(CustomInformationDTO customInformationDTO) {
        CustomInformationDO customInformationDO = customInformationService.selectOne(customInformationDTO);
        return TytBeanUtil.convertBean(customInformationDO, CustomInformationVO.class);
    }

    @Override
    public CustomInformationVO queryByPhone(String goodsPhone) {
        if (StringUtils.isBlank(goodsPhone)) {
            throw new BusinessException(GoodsErrorCode.ERROR_PHONE_LACK);
        }
        CustomInformationDTO customInformationDTO = new CustomInformationDTO();
        customInformationDTO.setGoodsPhone(goodsPhone);
        CustomInformationDO customInformationDO = customInformationService.selectOne(customInformationDTO);
        return TytBeanUtil.convertBean(customInformationDO, CustomInformationVO.class);
    }

    @Override
    public void modifyPerformanceTime(String goodsPhone, Long tsId) {
        if (StringUtils.isBlank(goodsPhone) || tsId == null) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_LACK);
        }
        customInformationService.updatePerformanceTime(goodsPhone, tsId);
    }

}
