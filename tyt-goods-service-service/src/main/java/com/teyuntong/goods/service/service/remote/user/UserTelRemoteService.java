package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import com.teyuntong.user.service.client.user.service.UserTelRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "UserTelRemoteService", fallbackFactory = UserTelRemoteService.UserTelRemoteFallbackFactory.class)
public interface UserTelRemoteService extends UserTelRpcService {

    @Component
    class UserTelRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<UserTelRemoteService> {
        protected UserTelRemoteFallbackFactory() {
            super(true, UserTelRemoteService.class);
        }
    }
}