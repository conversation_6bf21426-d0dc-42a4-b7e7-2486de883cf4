package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.user.ExcellentGoodsCardRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserIdentityRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserLimitRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.goods.service.service.rpc.publish.post.ExcellentPermissionPostHandler;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.limit.vo.DepositBlockCheckResultRpcVO;
import com.teyuntong.user.service.client.permission.dto.CheckExcellentGoodsCardRpcDTO;
import com.teyuntong.user.service.client.permission.dto.CheckExcellentGoodsPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.dto.GainExcellentGoodsCardRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ExcellentGoodsTypeEnum;
import com.teyuntong.user.service.client.permission.vo.*;
import com.teyuntong.user.service.client.user.vo.UserIdentityLabelVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.teyuntong.goods.service.service.common.error.GoodsErrorCode.EXCELLENT_GOODS_DEPOSIT_ERROR;

/**
 * 优车1.0发货权益校验checker
 *
 * <AUTHOR>
 * @since 2025/02/21 14:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcellentTransportChecker {

    private final UserPermissionRemoteService userPermissionRemoteService;
    private final ExcellentGoodsCardRemoteService excellentGoodsCardRemoteService;
    private final UserLimitRemoteService userLimitRemoteService;
    private final UserIdentityRemoteService userIdentityRemoteService;
    private final ExcellentPermissionPostHandler excellentPermissionPostHandler;

    /**
     * 直接发布校验
     */
    public void checkDirectPublish(DirectPublishProcessBO processBO) {

        // 直接发布 校验优车发货权益
        if (!PublishOptEnum.DIRECT.equals(processBO.getOptEnum())) {
            return;
        }

        TransportMainDO oldMainDO = processBO.getOldMain();
        // 非优车1.0不校验优车权益
        if (!Objects.equals(oldMainDO.getExcellentGoods(), 1)) {
            return;
        }
        // 如果是黑名单，不管优车还是优车2.0,全部给提示
        DepositBlockCheckResultRpcVO depositBlock = userLimitRemoteService.getDepositBlock(oldMainDO.getUserId(), false);
        if (depositBlock != null && depositBlock.isBlock()) {
            throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服");
        }

        // 自动转优车2.0不校验优车权益
//        if (processBO.getDirectPublishBO().isAutomaticGoodCarPriceTransport()) {
//            return;
//        }

        // 新版融合发货不校验优车1.0
        if (processBO.getBaseParam().getClientFusion() != null) {
            return;
        }

        // 优车1.0校验权益
        if (oldMainDO.getExcellentCardId() == null) {
            // 优车发货卡校验类型：1:电议无价 2：电议有价 3：一口价
            ExcellentGoodsTypeEnum excellentPublishTypeEnum;
            if (Objects.equals(oldMainDO.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
                excellentPublishTypeEnum = ExcellentGoodsTypeEnum.FIXED;
            } else if (TransportUtil.hasPrice(oldMainDO.getPrice())) {
                excellentPublishTypeEnum = ExcellentGoodsTypeEnum.HAVEPRICE;
            } else {
                excellentPublishTypeEnum = ExcellentGoodsTypeEnum.NOPRICE;
            }

            CheckExcellentGoodsPermissionRpcDTO permissionRpcDTO = new CheckExcellentGoodsPermissionRpcDTO();
            permissionRpcDTO.setPublishType(excellentPublishTypeEnum.getCode());
            permissionRpcDTO.setUserId(oldMainDO.getUserId());

            checkExcellentGoodsPermission(excellentPublishTypeEnum, oldMainDO.getUserId());
        } else { // 使用优车发货卡
            Boolean havePublishExcellentGoodsPermission = userPermissionRemoteService.havePublishExcellentGoodsPermission(oldMainDO.getUserId());
            if (!Boolean.TRUE.equals(havePublishExcellentGoodsPermission)) {
                // 如果有优车发货卡，校验优车发货卡是否有效
                GainExcellentGoodsCardRpcDTO goodsCardRpcDTO = new GainExcellentGoodsCardRpcDTO();
                goodsCardRpcDTO.setUserId(oldMainDO.getUserId());
                Long excellentGoodsCardId = excellentGoodsCardRemoteService.gainExcellentGoodsCard(goodsCardRpcDTO);
                if (excellentGoodsCardId == null) {
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "当前无可用优车发货卡，无法发布优车货源");
                }
                processBO.getDirectPublishBO().setExcellCardId(excellentGoodsCardId);
            }
        }
    }

    private void checkExcellentGoodsPermission(ExcellentGoodsTypeEnum excellentGoodsTypeEnum, Long userId) {
        CheckExcellentGoodsPermissionRpcDTO permissionRpcDTO = new CheckExcellentGoodsPermissionRpcDTO();
        permissionRpcDTO.setPublishType(excellentGoodsTypeEnum.getCode());
        permissionRpcDTO.setUserId(userId);
        ExcellentGoodsPermissionRpcVO excellentGoodsPermissionInfo = excellentGoodsCardRemoteService.getExcellentGoodsPermissionInfo(permissionRpcDTO);
        if (excellentGoodsPermissionInfo == null) {
            throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT);
        }
        // 授权记录
        DepositAuthorizationRpcVO authorizationRpcVO = excellentGoodsPermissionInfo.getDepositAuthorizationRpcVO();
        if (authorizationRpcVO == null) {
            throw BusinessException.createException(GoodsErrorCode.EXCELLENT_GOODS_AUTH_ERROR);
        } else {
            if (authorizationRpcVO.getAuthStatus() == 0) {
                boolean isConfigAll = Boolean.TRUE.equals(authorizationRpcVO.getConfigIsAll());
                boolean firstTransportOwner = isFirstTransportOwner(userId);
                if (isConfigAll && firstTransportOwner) {
                    throw BusinessException.createException(GoodsErrorCode.EXCELLENT_GOODS_AUTH_ERROR_CONFIG);
                } else {
                    throw BusinessException.createException(GoodsErrorCode.EXCELLENT_GOODS_AUTH_ERROR);
                }
            }
        }
        //已授权，下面维持原有逻辑
        if (authorizationRpcVO.getBlackStatus() == 1) {
            throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服");
        }
        // 是否有退保证金操作
        List<DepositApplyAuditRpcVO> applyAuditList = excellentGoodsPermissionInfo.getDepositApplyAuditRpcVOList();
        if (CollectionUtils.isNotEmpty(applyAuditList)) {
            throw BusinessException.createException(EXCELLENT_GOODS_DEPOSIT_ERROR);
        }
        // 查询余额账户
        DepositAccountRpcVO availableAccount = excellentGoodsPermissionInfo.getDepositAccountAvailableRpcVO();
        if (availableAccount == null) {
            throw BusinessException.createException(EXCELLENT_GOODS_DEPOSIT_ERROR.getCode(), "账户未创建，请授权签约");
        }
        DepositAccountRpcVO frozenAccount = excellentGoodsPermissionInfo.getDepositAccountFrozenRpcVO();
        BigDecimal totalBalance = frozenAccount == null ? availableAccount.getBalance() : availableAccount.getBalance().add(frozenAccount.getBalance());

        if (authorizationRpcVO.getRequireAmount() == null || totalBalance.compareTo(authorizationRpcVO.getRequireAmount().divide(new BigDecimal(2))) < 0) {
            throw BusinessException.createException(EXCELLENT_GOODS_DEPOSIT_ERROR);
        }
        //优车发货卡校验类型：1:电议无价 2：电议有价 3：一口价
        checkExcellentGoodsRemainingCount(excellentGoodsPermissionInfo, userId, excellentGoodsTypeEnum.getCode());

    }

    /**
     * 校验优车权益
     *
     * @param publishBO
     * @param user
     */
    public void checkExcellentGoods(PublishBO publishBO, UserRpcVO user) {


        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.EXCELLENT.getCode())) {
            // 如果是黑名单，不管优车还是优车2.0,全部给提示
            DepositBlockCheckResultRpcVO depositBlock = userLimitRemoteService.getDepositBlock(user.getId(), false);
            if (depositBlock != null && depositBlock.isBlock()) {
                throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服");
            }
            if (publishBO.getExcellentCardId() == null) {
                // 优车权益校验类型：1:电议无价 2：电议有价 3：一口价
                ExcellentGoodsTypeEnum excellentGoodsTypeEnum = excellentPermissionPostHandler.getExcellentGoodsTypeEnum(publishBO.getPublishType(), publishBO.getPrice());
                checkExcellentGoodsPermission(excellentGoodsTypeEnum, user.getId());

            } else {
                // 如果有优车发货卡，校验优车发货卡是否有效
                CheckExcellentGoodsCardRpcDTO goodsCardRpcDTO = new CheckExcellentGoodsCardRpcDTO();
                goodsCardRpcDTO.setExcellentCardId(publishBO.getExcellentCardId());
                goodsCardRpcDTO.setUserId(user.getId());
                Boolean checkResult = excellentGoodsCardRemoteService.checkExcellentGoodsCard(goodsCardRpcDTO);
                if (!checkResult) {
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "当前优车发货卡已失效，无法发布优车货源");
                }
            }
        }
    }

    public void checkExcellentGoodsRemainingCount(ExcellentGoodsPermissionRpcVO excellentGoodsPermissionInfo, Long userId, Integer youchePublishType) {
        // 查询优货剩余使用次数
        ExcellentGoodsRemainCountRpcVO remainCount = excellentGoodsPermissionInfo.getExcellentGoodsRemainCountRpcVO();
        if (Objects.equals(youchePublishType, ExcellentGoodsTypeEnum.NOPRICE.getCode())) {
            if (remainCount.getRemainingCallNoPriceCount() != null && remainCount.getRemainingCallNoPriceCount() <= 0) {
                //电议无价次数已用尽
                if ((remainCount.getRemainingCount() != null && remainCount.getRemainingCount() > 0) || remainCount.getRemainingFixedPriceCount() == null) {
                    //总次数还有剩余
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "当前发布次数已用尽，暂不能直接发布，请切换电议有价或一口价");
                } else {
                    //总次数没有剩余
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "优车发布次数已用尽，暂不能发布");
                }
            }
        } else if (Objects.equals(youchePublishType, ExcellentGoodsTypeEnum.HAVEPRICE.getCode())) {
            if (remainCount.getRemainingCallPriceCount() != null && remainCount.getRemainingCallPriceCount() <= 0) {
                //电议有价次数已用尽
                if ((remainCount.getRemainingCount() != null && remainCount.getRemainingCount() > 0) || remainCount.getRemainingFixedPriceCount() == null) {
                    //总次数还有剩余
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "当前发布次数已用尽，暂不能直接发布，请切换一口价");
                } else {
                    //总次数没有剩余
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "优车发布次数已用尽，暂不能发布");
                }
            }
        } else if (Objects.equals(youchePublishType, ExcellentGoodsTypeEnum.FIXED.getCode())) {
            if (remainCount.getRemainingFixedPriceCount() != null && remainCount.getRemainingFixedPriceCount() <= 0) {
                //一口价次数已用尽
                if ((remainCount.getRemainingCount() != null && remainCount.getRemainingCount() > 0) || remainCount.getRemainingFixedPriceCount() == null) {
                    //总次数还有剩余
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "当前发布次数已用尽，暂不能直接发布，请切换电议");
                } else {
                    //总次数没有剩余
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "优车发布次数已用尽，暂不能发布");
                }
            }
        }
    }

    public boolean isFirstTransportOwner(Long userId) {
        //因为优车用户授权配置才得到了优车去授权资格
        UserIdentityLabelVO userIdentityLabel = userIdentityRemoteService.getUserIdentityLabel(userId);
        if (userIdentityLabel != null
                && userIdentityLabel.getGoodsTypeFirst() != null
                && (userIdentityLabel.getGoodsTypeFirst() == 1 || userIdentityLabel.getGoodsTypeFirst() == 2)
                && userIdentityLabel.getGoodsTypeSecond() != null
                && userIdentityLabel.getGoodsTypeSecond() == 1) {
            //一手货主
            return true;
        }
        return false;
    }


}
