package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.remote.basic.NoticePopupTemplRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserLimitRemoteService;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.trade.service.client.infofee.vo.TytTransportWaybillExVO;
import com.teyuntong.user.service.client.limit.vo.UserLimitInfoRpcVO;
import com.teyuntong.user.service.client.limit.vo.UserOrdersLimitInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 用户发货限制检查checker
 *
 * <AUTHOR>
 * @since 2025/02/21 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPublishLimitChecker {

    private final UserLimitRemoteService userLimitRemoteService;
    private final OrdersRemoteService ordersRemoteService;
    private final NoticePopupTemplRemoteService noticePopupTemplRemoteService;

    public static final String YYYY_MM_DD_HH_MM = "yyyy年MM月dd日HH时mm分";

    /**
     * 校验发货限制
     */

    public void check(UserRpcVO user) {

        // 客服系统可以设置发货限制
        Integer infoPublishFlag = user.getInfoPublishFlag();
        if (Objects.equals(infoPublishFlag, YesOrNoEnum.YES.getId())) {
            NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.发货权限已关闭);
            throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
        }
        UserLimitInfoRpcVO userLimitInfo = userLimitRemoteService.getUserLimitInfo(user.getId());
        if (userLimitInfo == null) {
            return;
        }
        // 如果发货被长期限制
        if (Objects.equals(userLimitInfo.getLimitMinutes(), -1)) {
            // userType: 1:车 2：货
            UserOrdersLimitInfoRpcVO ordersLimitInfo = userLimitRemoteService.getOrdersLimitInfo(user.getId(), true, 2);
            if (ordersLimitInfo != null) {
                Long waybillExId = ordersLimitInfo.getWaybillExId();
                TytTransportWaybillExVO wayBill = ordersRemoteService.getWayBillByExId(waybillExId);
                if (wayBill != null) {
                    String taskContent = wayBill.getTaskContent();
                    if (StringUtils.isNotBlank(taskContent) && wayBill.getTaskContent().length() > 6) {
                        taskContent = taskContent.substring(0, 6);
                    }
                    NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.账号权益限制提醒_长期_有订单);
                    if (noticePopupTemplVo != null) {
//                        String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone(), wayBill.getTsOrderNo(), wayBill.getStartPoint(), wayBill.getDestPoint(), taskContent);
                        String[] searchList = new String[]{"{param1}", "{param2}", "{param3}", "{param4}", "{param5}"};
                        String[] replacementList = new String[]{userLimitInfo.getCellPhone(), wayBill.getTsOrderNo(), wayBill.getStartPoint(), wayBill.getDestPoint(), taskContent};
                        String format = StringUtils.replaceEach(noticePopupTemplVo.getMasterContent(), searchList, replacementList);

                        noticePopupTemplVo.setMasterContent(format);
                        throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
                    }
                }
            } else {
                NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.账号权益限制提醒_长期_无订单);
//                String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone());
                String[] searchList = new String[]{"{param1}"};
                String[] replacementList = new String[]{userLimitInfo.getCellPhone()};
                String format = StringUtils.replaceEach(noticePopupTemplVo.getMasterContent(), searchList, replacementList);

                noticePopupTemplVo.setMasterContent(format);
                throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
            }
        }
        // 如果发货被短期限制
        if (Objects.nonNull(userLimitInfo.getLimitEndTime()) && userLimitInfo.getLimitEndTime().after(new Date())) {
            UserOrdersLimitInfoRpcVO ordersLimitInfo = userLimitRemoteService.getOrdersLimitInfo(user.getId(), false, 2);
            if (ordersLimitInfo != null) {
                Long waybillExId = ordersLimitInfo.getWaybillExId();
                TytTransportWaybillExVO wayBill = ordersRemoteService.getWayBillByExId(waybillExId);
                if (wayBill != null) {
                    String taskContent = wayBill.getTaskContent();
                    if (StringUtils.isNotBlank(taskContent) && wayBill.getTaskContent().length() > 6) {
                        taskContent = taskContent.substring(0, 6);
                    }
                    NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.账号权益限制提醒_短期_有订单);
                    if (noticePopupTemplVo != null) {
                        String limitEndTime = DateUtil.format(ordersLimitInfo.getGoodsLimitEndTime(), YYYY_MM_DD_HH_MM);
//                        String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone(), wayBill.getTsOrderNo(), wayBill.getStartPoint(), wayBill.getDestPoint(), taskContent, limitEndTime);
                        String[] searchList = new String[]{"{param1}", "{param2}", "{param3}", "{param4}", "{param5}", "{param6}"};
                        String[] replacementList = new String[]{userLimitInfo.getCellPhone(), wayBill.getTsOrderNo(), wayBill.getStartPoint(), wayBill.getDestPoint(), taskContent, limitEndTime};
                        String format = StringUtils.replaceEach(noticePopupTemplVo.getMasterContent(), searchList, replacementList);

                        noticePopupTemplVo.setMasterContent(format);
                        throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
                    }
                }
            } else {

                NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.账号权益限制提醒_短期_无订单);
                String limitEndTime = DateUtil.format(userLimitInfo.getLimitEndTime(), YYYY_MM_DD_HH_MM);
//                String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone(), limitEndTime);
                String[] searchList = new String[]{"{param1}", "{param2}"};
                String[] replacementList = new String[]{userLimitInfo.getCellPhone(), limitEndTime};
                String format = StringUtils.replaceEach(noticePopupTemplVo.getMasterContent(), searchList, replacementList);

                noticePopupTemplVo.setMasterContent(format);
                throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
            }

        }
    }

}
