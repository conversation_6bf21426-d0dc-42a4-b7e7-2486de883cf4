package com.teyuntong.goods.service.service.rpc.excellentgoods;

import com.teyuntong.goods.service.client.excellentgoods.service.ExcellentGoodsRpcService;
import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailVO;
import com.teyuntong.goods.service.service.biz.excellentgoods.service.ExcellentGoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class ExcellentGoodsRpcServiceImpl implements ExcellentGoodsRpcService {

    private final ExcellentGoodsService excellentGoodsService;

    @Override
    public List<TytExcellentGoodsCardUserDetailVO> getAllNoUseCarListByUserId(Long userId, Integer pageNum) {
        return excellentGoodsService.getAllNoUseCarListByUserId(userId, pageNum);
    }

    @Override
    public TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumByUserId(Long userId) {
        return excellentGoodsService.getAllCanUseCarCountNumByUserId(userId);
    }

    @Override
    public List<TytExcellentGoodsCardUserDetailVO> getAllCanUseCarListByUserId(Long userId, Integer pageNum) {
        return excellentGoodsService.getAllCanUseCarListByUserId(userId, pageNum);
    }
}
