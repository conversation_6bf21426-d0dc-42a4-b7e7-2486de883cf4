package com.teyuntong.goods.service.service.rpc.transport;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.client.transport.dto.GoodCarPriceTransportTabAndBIPriceDTO;
import com.teyuntong.goods.service.client.transport.dto.GoodCarPriceUserLeftDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportCarryDTO;
import com.teyuntong.goods.service.client.transport.service.GoodCarPriceTransportRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ExcellentPriceCountGainDO;
import com.teyuntong.goods.service.service.biz.transport.service.ExcellentPriceCountService;
import com.teyuntong.goods.service.service.biz.transport.service.GoodCarPriceTransportService;
import com.teyuntong.goods.service.service.biz.transport.service.impl.ExcellentPriceCountServiceImpl;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 优车2.0服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
public class GoodCarPriceTransportRpcServiceImpl implements GoodCarPriceTransportRpcService {

    private final GoodCarPriceTransportService goodCarPriceTransportService;
    private final ExcellentPriceCountService excellentPriceCountService;
    private final TytConfigRemoteService tytConfigRemoteService;

    @Override
    public GoodCarPriceTransportTabAndBIPriceDTO isShowGoodCarPriceTransportTab(TransportCarryDTO transportCarryDTO) {
        return goodCarPriceTransportService.isShowGoodCarPriceTransportTab(transportCarryDTO);
    }

    @Override
    public GoodCarPriceUserLeftDTO showLeftTimes() {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        List<ExcellentPriceCountGainDO> gainList = excellentPriceCountService.validGainList(userId);

        GoodCarPriceUserLeftDTO userLeftDTO = new GoodCarPriceUserLeftDTO();
        if (CollectionUtils.isNotEmpty(gainList)) {
            // 发放次数
            userLeftDTO.setGiveawayNums(gainList.stream()
                    .filter(t -> ExcellentPriceCountServiceImpl.SourceEnum.FIRST_GIVE.getCode().equals(t.getSource())
                            || ExcellentPriceCountServiceImpl.SourceEnum.MONTH_GIVE.getCode().equals(t.getSource()))
                    .mapToInt(ExcellentPriceCountGainDO::getGainCount).sum());
            // 赠送次数=成交赠送次数
            userLeftDTO.setGiftNums(gainList.stream()
                    .filter(t -> Objects.equals(t.getSource(), ExcellentPriceCountServiceImpl.SourceEnum.DEAL_GIVE.getCode()))
                    .mapToInt(ExcellentPriceCountGainDO::getGainCount).sum());
            // 剩余次数
            userLeftDTO.setRemainNums(gainList.stream().mapToInt(t -> t.getGainCount() - t.getUsedCount()).sum());

            // 最早过期时间
            Date latestExpireDate = gainList.get(0).getExpireDateEnd();
            // 如果在7天内，就算过期
            if (latestExpireDate.before(DateUtil.offsetDay(new Date(), 7))) {
                userLeftDTO.setExpireDate(DateUtil.format(latestExpireDate, "MM月dd日"));
                // 即将过期次数
                userLeftDTO.setExpireNums(gainList.stream()
                        .filter(t -> Objects.equals(t.getExpireDateEnd(), latestExpireDate))
                        .mapToInt(ExcellentPriceCountGainDO::getGainCount).sum());
            }

            // 提示文案
            userLeftDTO.setContent(tytConfigRemoteService.getStringValue(ConfigKeyConstant.GOOD_CAR_PRICE_LEFT_TIME_PROMPT));
        }
        return userLeftDTO;
    }

    /**
     * 获取用户转电议剩余次数，-1代表不允许转电议
     */
    @Override
    public Integer getRemainCount(Long userId) {
        return excellentPriceCountService.getRemainCount(userId);
    }

    /**
     * 保存用户发布优车2.0电议货源次数
     */
    @Override
    public void saveTransferTeleUseCount(Long userId, Long srcMsgId) {
        excellentPriceCountService.saveUseCount(userId, srcMsgId);
    }

    /**
     * 记录用户发布优车2.0电议货源后成交赠送次数
     */
    @Override
    public void saveTransferTeleDealCount(Long userId, Long srcMsgId) {
        excellentPriceCountService.saveDealGain(userId, srcMsgId);
    }

}
