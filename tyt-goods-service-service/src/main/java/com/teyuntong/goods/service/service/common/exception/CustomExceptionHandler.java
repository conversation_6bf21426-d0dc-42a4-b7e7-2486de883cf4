package com.teyuntong.goods.service.service.common.exception;

import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.web.advice.ExceptionHandlerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.circuitbreaker.NoFallbackAvailableException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/19 17:17
 */
@Slf4j
@RestControllerAdvice
public class CustomExceptionHandler extends ExceptionHandlerBase {

    /**
     * TODO 只是为了演示使用，以后会删掉
     */
    @ExceptionHandler(NoFallbackAvailableException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    WebResult<Object> handleNoFallbackAvailableException(NoFallbackAvailableException exception) {
        log.error("服务内部异常: ", exception);
        String message = Optional.of(exception).map(Exception::getCause).map(Throwable::getMessage).orElse("");
        return WebResult.error(CommonErrorCode.INTERNAL_ERROR, message);
    }
}
