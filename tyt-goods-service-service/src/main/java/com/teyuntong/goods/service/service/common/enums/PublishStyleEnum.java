package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 货源类型（电议1，一口价2）
 *
 * <AUTHOR>
 * @since 2024/12/02 14:18
 */
@Getter
@AllArgsConstructor
public enum PublishStyleEnum {

    NEW_PUBLISH(1, "新发货源"),
    TODAY_PUBLISH(2, "当天编辑发布货源"),
    HISTORY_PUBLISH(3, "历史编辑发布货源");

    private final Integer code;
    private final String name;

    public static PublishStyleEnum getByCode(Integer code) {
        for (PublishStyleEnum publishStyle : PublishStyleEnum.values()) {
            if (Objects.equals(publishStyle.getCode(), code)) {
                return publishStyle;
            }
        }
        return NEW_PUBLISH;
    }

}
