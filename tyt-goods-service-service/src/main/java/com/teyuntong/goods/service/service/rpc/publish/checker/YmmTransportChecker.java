package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCompanyDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbCargoSyncInfoDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMbMergeDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCompanyService;
import com.teyuntong.goods.service.service.biz.transport.service.MbCargoSyncInfoService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMbMergeService;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 运满满货源校验
 *
 * <AUTHOR>
 * @since 2025/02/21 13:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class YmmTransportChecker {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final MbCargoSyncInfoService mbCargoSyncInfoService;
    private final DispatchCompanyService dispatchCompanyService;
    private final TransportMbMergeService transportMbMergeService;

    /**
     * 校验运满满货源直接发布
     */
    public void checkDirectPublish(TransportMainDO mainDO) {
        if (SourceTypeEnum.YMM.getCode().equals(mainDO.getSourceType())) {
            // 当前用户是否是调度身份
            List<DispatchCompanyDO> companyGoodsUsers = dispatchCompanyService.selectByUserId(mainDO.getId());
            if (CollectionUtils.isEmpty(companyGoodsUsers)) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "当前用户非调度身份");
            }
            TransportMbMergeDO transportMbMerge = transportMbMergeService.selectBySrcMsgId(mainDO.getSrcMsgId());
            if (transportMbMerge == null) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源不存在，看看其它货源吧！");
            }

            // 货源已发布
            if (transportMbMerge.getStatus() == 0) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已发布，看看其它货源吧！");
            }
            TransportMbMergeDO transportMbMerge2 = transportMbMergeService.selectByCargoId(transportMbMerge.getCargoId());
            if (transportMbMerge2 != null) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已发布，看看其它货源吧！");
            }

            // 货源已发布
            MbCargoSyncInfoDO mbCargoSyncInfoDO = mbCargoSyncInfoService.getByCargoId(transportMbMerge.getCargoId());
            if (mbCargoSyncInfoDO.getDelFlag() == 1) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已下架，看看其它货源吧！");
            }

            // 该货源已有信息变更，不允许发布
            if (!transportMbMerge.getCargoVersion().equals(mbCargoSyncInfoDO.getCargoVersion())) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已有信息变更，不允许发布");
            }

        }
    }


    /**
     * 运满满货源发布校验
     *
     * @param publishBO
     * @param user
     * @return
     */
    public void checkPublish(PublishBO publishBO, UserRpcVO user) {
        if (publishBO.getSourceType() != null && publishBO.getSourceType().equals(SourceTypeEnum.YMM.getCode())) {

            List<DispatchCompanyDO> companyGoodsUsers = dispatchCompanyService.selectByUserId(user.getId());
            if (CollectionUtils.isEmpty(companyGoodsUsers)) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "当前用户非调度身份");
            }

            // 校验是否是下架状态
            MbCargoSyncInfoDO transportYMM = mbCargoSyncInfoService.getByCargoId(publishBO.getCargoId());
            if (transportYMM == null) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源不存在，看看其它货源吧！");

            }
            if (transportYMM.getDelFlag() == 1) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已下架，看看其它货源吧！");

            }
            if (Objects.equals(publishBO.getCargoVersion(), transportYMM.getCargoVersion())) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已有信息变更，不允许发布");

            }
            if (Objects.equals(publishBO.getRefundFlag(), transportYMM.getDepositReturn())) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "不可修改退还状态");
            }

            if (publishBO.getInfoFee().compareTo(new BigDecimal(transportYMM.getDepositAmt())) != 0) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "不可修改订金金额");
            }
            //校验该货源是否已发布
            TransportMbMergeDO transportMbMerge = transportMbMergeService.selectByCargoId(publishBO.getCargoId());
            if (transportMbMerge != null) {
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "该货源已发布，看看其它货源吧！");
            }
        }
    }

}
