package com.teyuntong.goods.service.service.rpc.specialcar;

import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.goods.service.client.specialcar.dto.SigningSuccessDTO;
import com.teyuntong.goods.service.client.specialcar.service.SigningCarRpcService;
import com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarUserVO;
import com.teyuntong.goods.service.client.specialcar.vo.InvoiceDriverVO;
import com.teyuntong.goods.service.client.specialcar.vo.UserCarVO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarDO;
import com.teyuntong.goods.service.service.biz.specialcar.service.SigningCarInfoService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SigningCarService;
import com.teyuntong.goods.service.service.biz.specialcar.service.SigningDriverBlackService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TytSpecialCarDispatchFailureDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCargoOwnerService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCooperativeService;
import com.teyuntong.goods.service.service.biz.transport.service.SpecialCarDispatchFailureService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.DriverRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.car.vo.CarRpcVO;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import com.teyuntong.user.service.client.driver.vo.InvoiceDriverRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/06/13 13:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class SigningCarRpcServiceImpl implements SigningCarRpcService {

    private final UserRemoteService userRemoteService;
    private final DriverRemoteService driverRemoteService;
    private final SigningCarService signingCarService;
    private final TransportMainService transportMainService;
    private final SigningDriverBlackService signingDriverBlackService;
    private final SigningCarInfoService signingCarInfoService;

    @Autowired
    private OrdersRemoteService ordersRemoteService;

    @Autowired
    private SpecialCarDispatchFailureService specialCarDispatchFailureService;

    @Autowired
    private DispatchCooperativeService dispatchCooperativeService;

    @Autowired
    private DispatchCargoOwnerService dispatchCargoOwnerService;


    @Override
    public void updateSigningSuccessNum(SigningSuccessDTO signingSuccessDTO) {
        UserRpcVO carUser = userRemoteService.getUser(signingSuccessDTO.getCarUserId());
        InvoiceDriverRpcVO driver = driverRemoteService.getInfoByDriverId(signingSuccessDTO.getDriverId());
        //是否专车车辆
        SigningCarDO car = signingCarService.getByCellPhone(carUser.getCellPhone());
        boolean check = selectCheck(signingSuccessDTO.getSrcMsgId(), carUser, car);
        if (check) {
            boolean checkDriver = getCheckDriver(driver, car);
            if (checkDriver) {
                //更新合作次数
                signingCarService.updateCooperNum(car.getId());
                //更新指派次数
                signingCarInfoService.updateAssignNum(car.getId(), driver.getPhone());
            }
        }
    }

    @Override
    public void checkSigningCarUser(Long orderId) {
        try {
            TransportOrdersVO orders = ordersRemoteService.getById(orderId);
            if(null == orders || !"10".equals(orders.getCostStatus())){
                return;
            }
            UserRpcVO carUser = userRemoteService.getUser(orders.getPayUserId());

            //是否专车车辆
            SigningCarDO car = signingCarService.getByCellPhone(carUser.getCellPhone());

            Boolean check = selectCheck(orders,carUser,car);
            if(check){
                log.info("更新指派次数车主判断【{}】",check);
                if(null == orders.getDriverId()){
                    List<SigningCarUserVO> vos = signingCarService.getSigningId(car.getId());
                    if(CollectionUtils.isEmpty(vos)){
                        return;
                    }
                    for(SigningCarUserVO vo : vos){
                        if(!Objects.equals(2, vo.getStatus())){
                            signingCarService.updateNumId(vo.getId());
                        }
                    }

                }else{
                    InvoiceDriverRpcVO driver = driverRemoteService.getInfoByDriverId(orders.getDriverId());
                    Boolean checkDriver = getCheckDriver(driver, car);
                    log.info("更新指派次数司机判断【{}】",checkDriver);
                    if(checkDriver){
                        signingCarService.updateSigningNUm(car.getId(),driver.getPhone());
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新指派次数失败:", e);
        }
    }

    @Override
    public boolean checkSigningDriver(Long mainId, Long userId, Long driverId) {
        TransportMainDO main = transportMainService.getTransportMainForId(mainId);
        InvoiceDriverRpcVO driver = driverRemoteService.getInfoByDriverId(driverId);

        if (main.getExcellentGoods() != 2){
            return true;
        }
        TytSpecialCarDispatchFailureDO tytSpecialCarDispatchFailure = specialCarDispatchFailureService.selectBySrcMsgId(main.getId());
        if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
            return true;
        }
        SigningCarDO tytSigningCar = signingCarService.getByUserId(userId);
        if(null == tytSigningCar){
            return false;
        }
        int blackNum = signingDriverBlackService.checkDriverBlackStatus(driver.getDriverUserId());
        if (0 < blackNum){
            return false;
        }
        Integer num = signingCarInfoService.getByIdName(tytSigningCar.getId(),driver.getPhone());
        if (0 >= num){
            return false;
        }
        Long cargoOwnerId = main.getCargoOwnerId();
        if (!Objects.isNull(cargoOwnerId) && cargoOwnerId != 0){
            DispatchCooperativeDO cooperative = dispatchCooperativeService.getById(cargoOwnerId);
            if (Objects.isNull(cooperative)){
                return false;
            }
            DispatchCargoOwnerDO owner = dispatchCargoOwnerService.selectByCooperativeId(cooperative.getId());
            String[] signing = tytSigningCar.getSigning().split(",");
            if (Objects.isNull(owner) || (owner.getAssignCar() != 0 && !Arrays.asList(signing).contains(cooperative.getCooperativeName()))){
                return false;
            }
        }
        return true;
    }

    private Boolean selectCheck(TransportOrdersVO orders, UserRpcVO carUser, SigningCarDO car ) {
        try {
            if (orders == null || orders.getTsId() == null || orders.getPayUserId() == null) {
                return false;
            }
            TransportMainDO transportMain = transportMainService.getTransportMainForId(orders.getTsId());
            if(null == transportMain){
                return false;
            }
            if(2 != transportMain.getExcellentGoods()){
                return false;
            }
            //是宏信货主发货
            if(Objects.isNull(carUser)){
                return false;
            }

            if(Objects.isNull(car)){
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("查询是否满足合作次数条件失败:", e);
        }
        return false;
    }

    private boolean selectCheck(Long srcMsgId, UserRpcVO carUser, SigningCarDO car) {
        try {
            if (srcMsgId == null) {
                return false;
            }
            TransportMainDO transportMain = transportMainService.getTransportMainForId(srcMsgId);
            if (null == transportMain) {
                return false;
            }
            if (2 != transportMain.getExcellentGoods()) {
                return false;
            }
            //是宏信货主发货
            if (Objects.isNull(carUser)) {
                return false;
            }
            return Objects.nonNull(car);
        } catch (Exception e) {
            log.error("查询是否满足合作次数条件失败:", e);
        }
        return false;
    }

    public boolean getCheckDriver(InvoiceDriverRpcVO driver, SigningCarDO car) {
        try {
            if (Objects.isNull(driver)) {
                return false;
            }
            //司机是否在黑名单
            int blackNum = signingDriverBlackService.checkDriverBlackStatus(driver.getDriverUserId());
            int num = signingCarInfoService.getCountBySigningIdAndPhone(car.getId(), driver.getPhone());
            return 0 == blackNum && 1 == driver.getVerifyStatus() && 0 < num;
        } catch (Exception e) {
            log.error("查询是否满足合作次数条件失败:", e);
        }
        return false;
    }

    @Override
    public void updateCarStatusByUserId(Long userId, Integer status) {
        signingCarService.updateCarStatusByUserId(userId, status);
    }


    @Override
    public boolean getSigningCarBlack(Long userId) {
        return signingCarService.getSigningCarBlack(userId);
    }

    @Override
    public boolean checkCarDriver(Long userId, Long id, Long carId, Long driverId) {
        return signingCarService.checkCarDriver(userId, id, carId, driverId);
    }

    @Override
    public Map<String, List<UserCarVO>> getSigningCarList(Long userId) {
        List<CarRpcVO> signingCarList = signingCarService.getSigningCarList(userId);
        Map<String, List<UserCarVO>> carMap = new HashMap<>();
        if (CollectionUtils.isEmpty(signingCarList)) {
            return carMap;
        }
        List<UserCarVO> yCar = new ArrayList<>();
        List<UserCarVO> nCar = new ArrayList<>();
        for (CarRpcVO bean : signingCarList) {
            UserCarVO userCarVO = BeanUtil.copyProperties(bean, UserCarVO.class);
            if ("1".equals(bean.getAuth())) {
                yCar.add(userCarVO);
            } else {
                nCar.add(userCarVO);
            }
        }
        carMap.put("yCar", yCar);
        carMap.put("nCar", nCar);
        return carMap;
    }


    @Override
    public Map<String, List<InvoiceDriverVO>> getSigningUserList(Long userId, Long id) {
        TransportMainDO transportMainDO = transportMainService.getById(id);
        if(Objects.isNull(transportMainDO)){
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }
        Map<String,List<InvoiceDriverVO>> driverMap = new HashMap<>();
        SigningCarDO signingCarDO = signingCarService.getByUserId(userId);

        List<InvoiceDriverRpcVO> drivers = driverRemoteService.getLimitByUserId(userId);

        if(CollectionUtils.isEmpty(drivers)){
            return driverMap;
        }
        List<InvoiceDriverVO> invoiceDriverVOS = BeanUtil.copyToList(drivers, InvoiceDriverVO.class);
        //只要该专车货源允许大厅抢单，则所有司机均可选
        TytSpecialCarDispatchFailureDO tytSpecialCarDispatchFailure = specialCarDispatchFailureService.selectBySrcMsgId(id);
        if (tytSpecialCarDispatchFailure != null && tytSpecialCarDispatchFailure.getDeclareInPublic() != null && tytSpecialCarDispatchFailure.getDeclareInPublic() == 1) {
            driverMap.put("yDriver",invoiceDriverVOS);
            return driverMap;
        }
        //如果不是签约商，司机都为不符合
        if(null == signingCarDO){
            driverMap.put("nDriver",invoiceDriverVOS);
            return driverMap;
        }

        Long cargoOwnerId = transportMainDO.getCargoOwnerId();
        DispatchCargoOwnerDO owner = null;
        DispatchCooperativeDO cooperative = null;
        if (!Objects.isNull(cargoOwnerId) && cargoOwnerId != 0){
            cooperative = dispatchCooperativeService.selectByCooperativeId(cargoOwnerId);
            if (null != cooperative){
                owner = dispatchCargoOwnerService.getByCooperativeId(cooperative.getId());
            }
        }
        List<InvoiceDriverVO> yInvoices = new ArrayList<>();
        List<InvoiceDriverVO> nInvoices = new ArrayList<>();
        for(InvoiceDriverRpcVO driver : drivers){
            InvoiceDriverVO invoiceDriverVO = BeanUtil.copyProperties(driver, InvoiceDriverVO.class);
            if (checkDriver(driver,signingCarDO,cargoOwnerId,owner, cooperative)){
                yInvoices.add(invoiceDriverVO);
            }else{
                nInvoices.add(invoiceDriverVO);
            }
        }
        driverMap.put("yDriver",yInvoices);
        driverMap.put("nDriver",nInvoices);
        return driverMap;
    }

    private boolean checkDriver(InvoiceDriverRpcVO driver, SigningCarDO tytSigningCar, Long cargoOwnerId, DispatchCargoOwnerDO owner, DispatchCooperativeDO cooperative){
        int blackNum = signingDriverBlackService.checkDriverBlackStatus(driver.getDriverUserId());
        if(0 < blackNum){
            return false;
        }
        //判断是否在签约车辆里面
        int num = signingCarInfoService.getCountBySigningIdAndPhone(tytSigningCar.getId(),driver.getPhone());
        if (0 >= num) {
            return false;
        }
        if (Objects.isNull(cargoOwnerId) || 0 == cargoOwnerId) {
            return true;
        }
        if (Objects.isNull(cooperative)){
            return false;
        }
        if (Objects.isNull(owner)) {
            return false;
        }
        if (owner.getAssignCar() == 0) {
            return true;
        }

        String[] signing = tytSigningCar.getSigning().split(",");
        return Arrays.asList(signing).contains(cooperative.getCooperativeName());
    }
}
