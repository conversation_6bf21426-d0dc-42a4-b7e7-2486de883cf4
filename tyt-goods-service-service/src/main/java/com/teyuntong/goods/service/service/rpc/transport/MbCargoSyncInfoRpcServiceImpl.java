package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.service.MbCargoSyncInfoRpcService;
import com.teyuntong.goods.service.client.transport.vo.MbCargoSyncInfoVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MbCargoSyncInfoDO;
import com.teyuntong.goods.service.service.biz.transport.service.MbCargoSyncInfoService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportSyncYmmService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequiredArgsConstructor
public class MbCargoSyncInfoRpcServiceImpl implements MbCargoSyncInfoRpcService {

    private final MbCargoSyncInfoService mbCargoSyncInfoService;

    @Override
    public MbCargoSyncInfoVO selectMbCargoSyncInfoBySrcMsgId(Long srcMsgId) {
        MbCargoSyncInfoDO mbCargoSyncInfoDO = mbCargoSyncInfoService.selectMbCargoSyncInfoBySrcMsgId(srcMsgId);
        MbCargoSyncInfoVO mbCargoSyncInfoVO = new MbCargoSyncInfoVO();
        if (mbCargoSyncInfoDO == null) {
            return null;
        }
        BeanUtils.copyProperties(mbCargoSyncInfoDO, mbCargoSyncInfoVO);
        return mbCargoSyncInfoVO;
    }

    /**
     * 运满满货主与调度关系绑定
     *
     * @param srcMsgId
     */
    @Override
    public void ymmCargoBindDispatch(Long srcMsgId) {
        mbCargoSyncInfoService.ymmCargoBindDispatch(srcMsgId);
    }
}
