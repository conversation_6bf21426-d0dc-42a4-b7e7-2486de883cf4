package com.teyuntong.goods.service.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.OrderRiskRpcService;
import com.teyuntong.trade.service.client.orders.service.TransportOrderSnapshotRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * trade 订单接口
 *
 * <AUTHOR>
 * @since 2024/08/13 10:50
 */
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "OrderRiskRemoteService",
        fallbackFactory = OrderRiskRemoteService.OrderRiskRemoteServiceFallback.class)
public interface OrderRiskRemoteService extends OrderRiskRpcService {
    @Component
    class OrderRiskRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<OrderRiskRemoteService> {
        public OrderRiskRemoteServiceFallback() {
            super(true, OrderRiskRemoteService.class);
        }
    }
}
