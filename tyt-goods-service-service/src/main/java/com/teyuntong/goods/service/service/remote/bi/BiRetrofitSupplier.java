package com.teyuntong.goods.service.service.remote.bi;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import com.teyuntong.infra.common.retrofit.suppplier.RetrofitSupplier;
import io.github.resilience4j.timelimiter.autoconfigure.TimeLimiterProperties;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/13 16:51
 */
@Component
@RequiredArgsConstructor
public class BiRetrofitSupplier implements RetrofitSupplier {

    private final ObjectMapper mapper;
    private final LogInfoInterceptor logInfoInterceptor;
    private final TimeLimiterProperties timeLimiterProperties;
    private final TytConfigRemoteService configRemoteService;
    private final Environment environment;

    // 这个是好货模型所在的服务器
    private static final String HOST_URL = "tyt:config:private:bi_data_model";

    @Override
    public Retrofit getRetrofit() {

        String activeProfile = environment.getActiveProfiles()[0];
        String baseUrl = "http://**************:8080";
        if (!Objects.equals(activeProfile, "local")) {
            baseUrl = configRemoteService.getStringValue(HOST_URL,"http://bi-qualitymodel.teyuntong.private:8080");
        }

        Duration defaultTime = timeLimiterProperties.getConfigs().get("default").getTimeoutDuration();

        return new Retrofit.Builder()
                .baseUrl(baseUrl)
                .client(new OkHttpClient.Builder()
                        .connectTimeout(defaultTime == null ? Duration.ofSeconds(3) : defaultTime)
                        .readTimeout(defaultTime == null ? Duration.ofSeconds(3) : defaultTime)
                        .addInterceptor(logInfoInterceptor)
                        .build())
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .build();
    }
}
