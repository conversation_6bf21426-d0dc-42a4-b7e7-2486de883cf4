package com.teyuntong.goods.service.service.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 好评率展示类型
 * @date 2023/11/06 11:26
 */
public enum RatingTypeEnum {

    NOT_SHOW_RATING(0, "不展示好评率"),

    SHOW_RATING( 1,"展示好评率"),

    RECENT_RECEIVE_POSITIVE( 2,"近期有好评"),

    RECENT_NOT_RECEIVE_POSITIVE( 3,"近期无好评"),

    RECENT_NO_RATE( 4,"暂无评价");



    private Integer code;

    private String value;

    RatingTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
