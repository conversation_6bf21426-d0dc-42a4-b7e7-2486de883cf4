package com.teyuntong.goods.service.service.rpc.transport;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.commission.service.CommissionService;
import com.teyuntong.goods.service.service.biz.recommend.dto.ExcellentGoodsPriceConfigDTO;
import com.teyuntong.goods.service.service.biz.recommend.service.ExcellentGoodsPriceConfigService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ChangePriceLogDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.ChangePriceLogMapper;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCompanyService;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 建议价 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class ThPriceRpcServiceImpl implements ThPriceRpcService {

    private final ThPriceService thPriceService;

    private final DispatchCompanyService dispatchCompanyService;

    private final ChangePriceLogMapper changePriceLogMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final CommissionService commissionService;

    private final ExcellentGoodsPriceConfigService excellentGoodsPriceConfigService;

    private final String EARLIEST_PRICE_CACHE_KEY = "EarliestPrice:";

    @Override
    public CarryPriceVO getThPrice(TransportCarryReq transportCarryReq) {
        return thPriceService.getThPrice(transportCarryReq);
    }

    @Override
    public Boolean isHavePriceModelTwo(TransportCarryReq transportCarryReq) {
        return thPriceService.isHavePriceModelTwo(transportCarryReq);
    }

    @Override
    public void changePirceLog(ChangePriceLogReq changePriceLogReq) {
        if (changePriceLogReq.getSrcMsgId() == null || changePriceLogReq.getPrice() == null || changePriceLogReq.getPublishType() == null || changePriceLogReq.getOperationType() == null) {
            return;
        }
        String key = EARLIEST_PRICE_CACHE_KEY + changePriceLogReq.getSrcMsgId();
        String earliestPriceStr = stringRedisTemplate.opsForValue().get(key);
        if (earliestPriceStr == null && changePriceLogReq.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            //初始价格不存在，记录初始价格
            stringRedisTemplate.opsForValue().setIfAbsent(key, changePriceLogReq.getPrice().toString(), Duration.ofDays(1));
        }

        // 查询最近一条记录，如果price和publishType相同，则不记录
        ChangePriceLogDO latestLog = changePriceLogMapper.getLatestLogBySrcMsgId(changePriceLogReq.getSrcMsgId());
        if (latestLog != null
                && Objects.equals(latestLog.getPrice(), changePriceLogReq.getPrice())
                && Objects.equals(latestLog.getPublishType(), changePriceLogReq.getPublishType())) {
            return;
        }

        // 记录价格变动日志
        ChangePriceLogDO changePriceLogDO = new ChangePriceLogDO();
        changePriceLogDO.setSrcMsgId(changePriceLogReq.getSrcMsgId());
        changePriceLogDO.setPrice(changePriceLogReq.getPrice());
        changePriceLogDO.setPublishType(changePriceLogReq.getPublishType());
        changePriceLogDO.setOperationType(changePriceLogReq.getOperationType());
        changePriceLogDO.setCreateTime(new Date());
        log.info("记录价格变动日志：{}", JSON.toJSONString(changePriceLogDO));
        changePriceLogMapper.insert(changePriceLogDO);
    }

    @Override
    public Map<Long, BigDecimal> getTransportEarliestPrice(GetEarliestPriceReq getEarliestPriceReq) {
        HashMap<Long, BigDecimal> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(getEarliestPriceReq.getSrcMsgIds())) {
            // 构建所有需要查询的 Redis key
            List<String> keys = getEarliestPriceReq.getSrcMsgIds().stream()
                    .map(srcMsgId -> EARLIEST_PRICE_CACHE_KEY + srcMsgId)
                    .collect(Collectors.toList());

            // 批量获取 Redis 值
            List<String> values = stringRedisTemplate.opsForValue().multiGet(keys);

            // 处理结果
            if (values != null) {
                for (int i = 0; i < keys.size(); i++) {
                    String value = values.get(i);
                    if (value != null) {
                        try {
                            Long srcMsgId = getEarliestPriceReq.getSrcMsgIds().get(i);
                            result.put(srcMsgId, new BigDecimal(value));
                        } catch (NumberFormatException e) {
                            // 忽略无法转换为 BigDecimal 的值
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 发货时获取技术服务费，只针对代调账户发的专车非平台
     *
     * @param publishDTO
     */
    @Override
    public TecServiceFeeVO tecServiceFee(PublishDTO publishDTO) {
        TecServiceFeeVO tecServiceFeeVO = new TecServiceFeeVO();
        tecServiceFeeVO.setShowInputBox(0);

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();

        // 非代调账户不返回佣金
        if (dispatchCompanyService.countByUserId(userId) == 0) {
            return tecServiceFeeVO;
        }
        // 非专车 或 签约合作商是平台，不返回佣金
        if (publishDTO.getCargoOwnerId() == null || publishDTO.getCargoOwnerId() == 0L || publishDTO.getCargoOwnerId() == 1L) {
            return tecServiceFeeVO;
        }

        tecServiceFeeVO.setShowInputBox(1);
        // 无价显示输入框，但金额为空
        if (TransportUtil.nonPrice(publishDTO.getPrice())) {
            return tecServiceFeeVO;
        }

        // 设置代调发货，专车
        publishDTO.setExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
        publishDTO.setSourceType(SourceTypeEnum.DISPATCH.getCode()); // 代调
        BigDecimal tecServiceFee = commissionService.getTecServiceFeeWhenPublish(publishDTO, userId);
        tecServiceFeeVO.setTecServiceFee(tecServiceFee);
        return tecServiceFeeVO;
    }

}
