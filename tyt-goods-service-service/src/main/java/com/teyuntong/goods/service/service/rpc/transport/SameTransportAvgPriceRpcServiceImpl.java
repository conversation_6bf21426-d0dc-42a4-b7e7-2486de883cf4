package com.teyuntong.goods.service.service.rpc.transport;

import com.aliyun.openservices.ons.shaded.commons.lang3.StringUtils;
import com.teyuntong.goods.service.client.transport.dto.SameTransportAvgPriceQueryRpcDTO;
import com.teyuntong.goods.service.client.transport.service.SameTransportAvgPriceRpcService;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.SameTransportAvgPriceResultVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceQueryDTO;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceResultDTO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.UseCarTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 相似货源成交价RPC服务实现
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class SameTransportAvgPriceRpcServiceImpl implements SameTransportAvgPriceRpcService {

    private final TransportAfterOrderDataService transportAfterOrderDataService;

    @Override
    public SameTransportAvgPriceResultVO getSameTransportAvgPrice(Long srcMsgId, Long userId) {
        SameTransportAvgPriceResultDTO resultDTO = transportAfterOrderDataService.getSameTransportAvgPrice(srcMsgId, userId);
        return convertToVO(resultDTO);
    }

    @Override
    public SameTransportAvgPriceResultVO getSameTransportAvgPrice(SameTransportAvgPriceQueryRpcDTO queryRpcDTO) {
        return transportAfterOrderDataService.getSameTransportAvgPriceAndCarryPrice(queryRpcDTO);
    }

    @Override
    public BigDecimal getSameTransportAvgPriceByDays(Long srcMsgId, int days) {
        return transportAfterOrderDataService.getSameTransportAvgPriceByDays(srcMsgId, days);
    }

    /**
     * 将DTO转换为VO
     */
    private SameTransportAvgPriceResultVO convertToVO(SameTransportAvgPriceResultDTO resultDTO) {
        if (resultDTO == null) {
            return null;
        }
        SameTransportAvgPriceResultVO resultVO = new SameTransportAvgPriceResultVO();
        BeanUtils.copyProperties(resultDTO, resultVO);
        return resultVO;
    }
}
