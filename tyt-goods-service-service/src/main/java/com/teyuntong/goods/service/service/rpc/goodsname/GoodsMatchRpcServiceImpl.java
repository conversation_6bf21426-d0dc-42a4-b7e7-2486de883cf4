package com.teyuntong.goods.service.service.rpc.goodsname;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.service.client.goodsname.dto.CheckScreeningWordDto;
import com.teyuntong.goods.service.client.goodsname.dto.GoodsMatchRpcDto;
import com.teyuntong.goods.service.client.goodsname.service.GoodsMatchRpcService;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsBrandListVo;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchEsVo;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchVo;
import com.teyuntong.goods.service.service.biz.goodsname.enums.MachineTypeEnum;
import com.teyuntong.goods.service.service.biz.goodsname.mybatis.es.entity.GoodsMatchEsDO;
import com.teyuntong.goods.service.service.biz.goodsname.service.GoodsMatchService;
import com.teyuntong.goods.service.service.biz.goodsname.service.MachineTypePromptWordService;
import com.teyuntong.goods.service.service.biz.goodsname.service.NullifyKeywordService;
import com.teyuntong.goods.service.service.biz.transport.service.SeckillGoodsTransportService;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.checker.SensitiveWordsChecker;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.outer.export.service.client.sensitivewords.vo.SensitiveWordsVO;
import com.teyuntong.trade.service.client.orders.dto.TransportOrdersRpcDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.annotation.HighLight;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.alibaba.nacos.common.utils.StringUtils.COMMA;
import static com.teyuntong.goods.service.service.common.error.GoodsErrorCode.NULLIFY_KEY_WORDS_REMARK;


/**
 * <AUTHOR>
 * @since 2024/4/28 13:23
 */

@Slf4j
@RestController
@RequiredArgsConstructor
public class GoodsMatchRpcServiceImpl implements GoodsMatchRpcService {

    private final GoodsMatchService goodsMatchService;
    private final NullifyKeywordService nullifyKeywordService;
    private final MachineTypePromptWordService machineTypePromptWordService;

    private final TytConfigRemoteService tytConfigRemoteService;

    private final SeckillGoodsTransportService seckillGoodsTransportService;

    private final OrdersRemoteService ordersRemoteService;
    private final SensitiveWordsChecker sensitiveWordsChecker;



    @Override
    public GoodsMatchVo searchGoodsMatch(@Validated @RequestBody GoodsMatchRpcDto goodsMatchRpcDto) {
        GoodsMatchVo goodsMatchVo = new GoodsMatchVo();
        goodsMatchVo.setKeyword(goodsMatchRpcDto.getKeywords());
        goodsMatchVo.setStandardName(goodsMatchRpcDto.getStandardName());
        // 判断是否是非法词汇
        Set<String> matchKeywordsSet = nullifyKeywordService.verifyKeywords(goodsMatchRpcDto.getKeywords());
        if (CollUtil.isNotEmpty(matchKeywordsSet)) {
            String matchKeywords = StringUtils.join(matchKeywordsSet, COMMA);
            log.warn("触发关键字：{}", matchKeywords);
            goodsMatchVo.setMachineType(MachineTypeEnum.ILLEGAL.code);
            return goodsMatchVo;
        }


        List<GoodsMatchEsDO> goodsMatchList = goodsMatchService.searchGoodsMatch(goodsMatchRpcDto);
        if (CollUtil.isNotEmpty(goodsMatchList)) {
            List<GoodsMatchEsVo> goodsMatchEsVoList = TytBeanUtil.convertBeanList(goodsMatchList, GoodsMatchEsVo.class);
            goodsMatchVo.setList(goodsMatchEsVoList);
            goodsMatchVo.setMachineType(MachineTypeEnum.CONFORMANCE.code);
        } else {
            goodsMatchVo.setMachineType(MachineTypeEnum.AUDIT.code);
        }

        // 判断是否是非标提示词
        goodsMatchVo.setNonStandard(machineTypePromptWordService.nonStandard(goodsMatchRpcDto.getKeywords()));

        // 判断是否出添加货源的提示,命中标准货源或者别名都不弹,只有第一页才判断是否弹窗，后续都以第一页为准
        if (CollUtil.isNotEmpty(goodsMatchList) && goodsMatchRpcDto.getPageNum() == 1) {
            String showName = goodsMatchList.get(0).getShowName();
            // 判断是否完全高亮,如果完全高亮，则不弹
            boolean allHighLight = checkAllHighLight(showName);
            if (allHighLight) {
                goodsMatchVo.setAddGoodsNamePop(YesOrNoEnum.NO.getId());
            }

        }
        // 查询所有的标准货名
        Set<String> analysisKeywordsSet = goodsMatchService.analysisKeywords(goodsMatchRpcDto.getKeywords());
        if (CollUtil.isNotEmpty(analysisKeywordsSet)) {
            // 过滤出来所有的货类
            Set<String> secondClassSet = goodsMatchList.stream().map(GoodsMatchEsDO::getSecondClass).collect(Collectors.toSet());
            // 取出analysisKeywordsSet和secondClassSet的交集
            analysisKeywordsSet.retainAll(secondClassSet);
            String standardName = goodsMatchRpcDto.getStandardName();
            if (CollUtil.isNotEmpty(analysisKeywordsSet)) {
                String join = String.join(COMMA, analysisKeywordsSet);
                if (StringUtils.isNotBlank(standardName)) {
                    Set<String> collect = Arrays.stream(standardName.split(COMMA)).collect(Collectors.toSet());
                    // 将之前的搜索和这次的搜索取并集
                    collect.addAll(analysisKeywordsSet);
                    standardName = String.join(COMMA, collect);
                } else {
                    standardName = join;
                }
            }
            goodsMatchVo.setStandardName(standardName);
        }
        return goodsMatchVo;
    }

    private boolean checkAllHighLight(String showName) {
        if (StringUtils.isBlank(showName)) {
            return false;
        }
        try {
            Field showNameFiled = GoodsMatchEsDO.class.getDeclaredField("showName");
            showNameFiled.setAccessible(true);
            HighLight annotation = showNameFiled.getAnnotation(HighLight.class);
            String preTag = annotation.preTag();
            String postTag = annotation.postTag();
            if (showName.startsWith(preTag) && showName.endsWith(postTag)) {
                return true;
            }
        } catch (NoSuchFieldException e) {
            log.error(e.getMessage());
        }
        return false;
    }

    @Override
    public Boolean screeningWordCheck(CheckScreeningWordDto checkScreeningWordDto) {
        if (checkScreeningWordDto.getInvoiceTransport() != null && checkScreeningWordDto.getInvoiceTransport() == 1) {
            //开票货源校验集团敏感词
            LoginUserDTO loginUser = LoginHelper.getLoginUser();
            if (loginUser != null && loginUser.getUserId() != null) {
                sensitiveWordsChecker.checkSensitiveWords(loginUser.getUserId(), checkScreeningWordDto.getTaskContent(), checkScreeningWordDto.getMachineRemark());
            }
        }

        Set<String> matchKeywordsSet = nullifyKeywordService.verifyKeywords(checkScreeningWordDto.getTaskContent() + checkScreeningWordDto.getMachineRemark());
        if (CollUtil.isNotEmpty(matchKeywordsSet)) {
            SensitiveWordsVO sensitiveWordsVO = new SensitiveWordsVO();
            sensitiveWordsVO.setTaskContentHitSensitiveWords(false);
            sensitiveWordsVO.setMachineRemarkHitSensitiveWords(false);
            for (String matchKeywords : matchKeywordsSet) {
                if (checkScreeningWordDto.getTaskContent().contains(matchKeywords)) {
                    sensitiveWordsVO.setTaskContentHitSensitiveWords(true);
                }
                if (checkScreeningWordDto.getMachineRemark().contains(matchKeywords)) {
                    sensitiveWordsVO.setMachineRemarkHitSensitiveWords(true);
                }
                if (sensitiveWordsVO.getTaskContentHitSensitiveWords() && sensitiveWordsVO.getMachineRemarkHitSensitiveWords()) {
                    break;
                }
            }
            sensitiveWordsVO.setSensitiveWords(matchKeywordsSet);
            throw new BusinessException(GoodsErrorCode.NULLIFY_KEY_WORDS_REMARK_NEW, null, sensitiveWordsVO);
        }

        return true;
    }

    /**
     * 货名搜索品牌型号
     */
    @Override
    public GoodsBrandListVo searchGoodsBrand(GoodsMatchRpcDto goodsMatchRpcDto) {
        return goodsMatchService.searchGoodsBrand(goodsMatchRpcDto);
    }



    @Override
    public Boolean isEdit(Long srcMsgId, Integer type) {

        if(seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)){
            throw new BusinessException(GoodsErrorCode.SECKILL_TRANSPORT_IS_LOCK);
        }

        TransportOrdersRpcDTO transportOrdersRpcDTO = new TransportOrdersRpcDTO();
        transportOrdersRpcDTO.setTsId(srcMsgId);
        transportOrdersRpcDTO.setCostStatus(15);
        transportOrdersRpcDTO.setCtime(TimeUtil.weeHours(new Date(), 0));
        int ordersCount = ordersRemoteService.isEdit(transportOrdersRpcDTO);
        if (ordersCount > 0){
            throw new BusinessException(GoodsErrorCode.SECKILL_TRANSPORT_IS_LOCK, "已有车主支付订金，不可编辑再发布");
        }

        return true;
    }
}