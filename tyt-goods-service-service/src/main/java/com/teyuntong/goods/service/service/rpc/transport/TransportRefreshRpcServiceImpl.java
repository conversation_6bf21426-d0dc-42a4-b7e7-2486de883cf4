package com.teyuntong.goods.service.service.rpc.transport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.client.transport.service.TransportRefreshRpcService;
import com.teyuntong.goods.service.client.transport.vo.RefreshContentVO;
import com.teyuntong.goods.service.service.biz.transport.dto.TransportRefreshDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportRefreshService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.OptionalInt;
import java.util.concurrent.TimeUnit;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.RESEND_EXCELLENT_KEY;
import static jodd.util.StringPool.COMMA;

/**
 * 货源刷新
 *
 * <AUTHOR>
 * @since 2025-02-16 16:13
 */
@RestController
@Slf4j
public class TransportRefreshRpcServiceImpl implements TransportRefreshRpcService {
    @Autowired
    private TransportRefreshService transportRefreshService;
    @Autowired
    private TransportService transportService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private TytConfigRemoteService configRemoteService;

    /**
     * 刷新一次（依赖刷新定时，最多有1分钟延迟）
     *
     * @param srcMsgId      货源ID
     * @param refreshSource 刷新来源
     */
    @Override
    public void refreshTransport(Long srcMsgId, String refreshSource) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        if (Objects.isNull(srcMsgId) || StringUtils.isBlank(refreshSource)) {
            log.info("refreshTransport param error, srcMsgId:{}, refreshSource:{}", srcMsgId, refreshSource);
            return;
        }
        TransportDO transportDO = transportService.getLastBySrcMygId(srcMsgId);
        if (Objects.isNull(transportDO)) {
            log.info("refreshTransport transport is null, srcMsgId:{}, refreshSource:{}", srcMsgId, refreshSource);
            return;
        }

        TransportRefreshDTO refreshDTO = new TransportRefreshDTO();
        refreshDTO.setTransportDO(transportDO);
        refreshDTO.setRefreshConfigId(99998L);
        refreshDTO.setCurTime(new Date());
        refreshDTO.setRefreshInterval(0);
        refreshDTO.setBatchNo(refreshSource);
        refreshDTO.setRefreshCountCacheValue(0);
        refreshDTO.setRefreshCountCacheKey(refreshSource);
        transportRefreshService.refreshTransport(refreshDTO);

        // 更新缓存刷新次数
        String cacheKey = RedisKeyConstant.CANCEL_GIVE_REFRESH_CACHE_PREFIX + loginUser.getUserId();
        String cacheRefreshValue = stringRedisTemplate.opsForValue().get(cacheKey);
        int refreshCount = 1;
        if (StringUtils.isNotBlank(cacheRefreshValue)) {
            refreshCount = Integer.parseInt(cacheRefreshValue) + 1;
        }
        long tomorrowZeroSeconds = TimeUtil.getTomorrowZeroSeconds();
        stringRedisTemplate.opsForValue().set(cacheKey, String.valueOf(refreshCount), tomorrowZeroSeconds, TimeUnit.SECONDS);
    }


    @Override
    public Integer minRefreshInterval(Long userId) {
        Integer minRefreshInterval = null;
        // 获取设置的刷新次数及刷新间隔,如果没有配置，就走默认设置
        RefreshContentVO minIntervalContent = transportRefreshService.getMinIntervalContent(userId);
        if (minIntervalContent != null) {
            minRefreshInterval = Integer.valueOf(minIntervalContent.getInterval());
        } else {
            minRefreshInterval = getExcellentDefaultInterval();
        }
        return minRefreshInterval;
    }

    private Integer getExcellentDefaultInterval() {

        String excellentTopConfig = configRemoteService.getStringValue(RESEND_EXCELLENT_KEY, "");
        if (StringUtils.isNotBlank(excellentTopConfig)) {
            JSONArray jsonArray = JSONUtil.parseArray(excellentTopConfig);
            List<RefreshContentVO> refreshContentVOList = JSONUtil.toList(jsonArray, RefreshContentVO.class);
            if (CollUtil.isNotEmpty(refreshContentVOList)) {
                OptionalInt min = refreshContentVOList.stream().map(RefreshContentVO::getInterval).filter(Objects::nonNull).mapToInt(Integer::parseInt).min();
                if (min.isPresent()) {
                    return min.getAsInt();
                }
            }
        }
        return null;
    }


}
