
package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDispatchDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMbMergeDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportDispatchService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMbMergeService;
import com.teyuntong.goods.service.service.common.enums.ClientSignEnum;
import com.teyuntong.goods.service.service.common.enums.PublishPlatformEnum;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.user.CsMaintainedCustomRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.user.service.client.custom.dto.CsMaintainedCustomDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 修改运满满同步数据
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddMbCargoPostHandler {

    private final TransportMbMergeService transportMbMergeService;
    /**
     * 发布货源时如果是运满满货源，同步添加到运满满关系表中
     *
     * @param transportMain
     * @param publishBO
     */
    public void saveMbCargoSyncInfo(PublishBO publishBO, TransportMainDO transportMain) {
        if (Objects.equals(transportMain.getSourceType(), SourceTypeEnum.YMM.getCode())) {
            TransportMbMergeDO mbMergeDO = transportMbMergeService.selectBySrcMsgId(transportMain.getId());
            if (mbMergeDO == null) {
                mbMergeDO = new TransportMbMergeDO();
                mbMergeDO.setCargoId(publishBO.getCargoId());
                mbMergeDO.setSrcMsgId(transportMain.getId());
                mbMergeDO.setCargoVersion(publishBO.getCargoVersion());
                mbMergeDO.setStatus(0);
                mbMergeDO.setCreateTime(new Date());
                mbMergeDO.setUpdateTime(new Date());
                transportMbMergeService.save(mbMergeDO);
            } else {
                if (publishBO.getCargoVersion() != null) {
                    mbMergeDO.setCargoVersion(publishBO.getCargoVersion());
                }
                mbMergeDO.setStatus(0);
                mbMergeDO.setUpdateTime(new Date());
                transportMbMergeService.updateById(mbMergeDO);
            }
        }
    }

}
