package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户经分身份enum
 *
 * <AUTHOR>
 * @since 2024/10/17 11:36
 */
@Getter
@AllArgsConstructor
public enum UserBiIdentityEnum {
    // 1:物流公司 2:货站 3:企业货主 4:个人货主
    LOGISTICS_COMPANY(1, "物流公司"),
    CARGO_TERMINAL(2, "货站"),
    ENTERPRISE_CARGO_OWNER(3, "企业货主"),
    SELF_CARGO_OWNER(4, "个人货主");

    private final Integer code;
    private final String name;

    /**
     * 是否是直客
     */
    public static boolean isCargoOwner(Integer code) {
        return ENTERPRISE_CARGO_OWNER.code.equals(code) || SELF_CARGO_OWNER.code.equals(code);
    }
}
