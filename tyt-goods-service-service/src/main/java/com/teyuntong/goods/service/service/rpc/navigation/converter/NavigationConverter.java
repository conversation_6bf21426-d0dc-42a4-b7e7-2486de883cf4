package com.teyuntong.goods.service.service.rpc.navigation.converter;

import com.teyuntong.goods.service.client.navigation.dto.NavigationTruckDTO;
import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/01/24 10:36
 */
@Mapper
public interface NavigationConverter {
    NavigationConverter INSTANCE = Mappers.getMapper(NavigationConverter.class);

    DistanceRpcDTO to(NavigationTruckDTO navigationTruckDTO);
}
