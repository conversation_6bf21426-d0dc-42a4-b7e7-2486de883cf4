package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.driver.service.DriverRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/06/13 13:37
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "driverRemoteService", fallbackFactory = DriverRemoteService.DriverRemoteFallbackFactory.class)
public interface DriverRemoteService extends DriverRpcService {

    @Component
    class DriverRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<DriverRemoteService> {
        protected DriverRemoteFallbackFactory() {
            super(true, DriverRemoteService.class);
        }
    }
}
