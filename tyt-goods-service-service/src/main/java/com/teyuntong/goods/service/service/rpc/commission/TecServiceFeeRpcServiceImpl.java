package com.teyuntong.goods.service.service.rpc.commission;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.commission.dto.TecServiceFeeComputeDTO;
import com.teyuntong.goods.service.client.commission.service.TecServiceFeeRpcService;
import com.teyuntong.goods.service.client.commission.vo.TecServiceFeeConfigComputeResultVO;
import com.teyuntong.goods.service.client.transport.dto.TransportMainDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportMainExtendDTO;
import com.teyuntong.goods.service.client.transport.vo.CommissionTypeVO;
import com.teyuntong.goods.service.client.transport.vo.DrawCommissionReq;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.commission.CalcCommissionTecFeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 抽佣技术付服务费
 *
 * <AUTHOR>
 * @since 2025-07-25 14:30
 */
@RestController
@Slf4j
public class TecServiceFeeRpcServiceImpl implements TecServiceFeeRpcService {
    @Resource
    private CalcCommissionTecFeeService calcCommissionTecFeeService;

    /**
     * 计算抽佣技术服务费
     *
     * @param computeDTO
     * @return
     */
    @Override
    public TecServiceFeeConfigComputeResultVO computeTecServiceFeeByTransportData(TecServiceFeeComputeDTO computeDTO) {
        log.info("computeTecServiceFeeByTransportData param:{}", JSONObject.toJSONString(computeDTO));
        TransportMainDTO transportMainDTO = computeDTO.getTransportMain();
        TransportMainDO transportMain = new TransportMainDO();
        BeanUtils.copyProperties(transportMainDTO, transportMain);

        TransportMainDO oldMain = null;
        TransportMainDTO oldMainDTO = computeDTO.getOldMain();
        if (Objects.nonNull(oldMainDTO) && Objects.nonNull(oldMainDTO.getSrcMsgId())) {
            oldMain = new TransportMainDO();
            BeanUtils.copyProperties(oldMainDTO, oldMain);
        }

        TransportMainExtendDTO mainExtendDTO = computeDTO.getMainExtend();
        TransportMainExtendDO mainExtend = new TransportMainExtendDO();
        BeanUtils.copyProperties(mainExtendDTO, mainExtend);

        BasePublishProcessBO publishProcessBO = new BasePublishProcessBO();
        publishProcessBO.setTransportMain(transportMain);
        publishProcessBO.setOldMain(oldMain);
        publishProcessBO.setMainExtend(mainExtend);
        TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = calcCommissionTecFeeService.computeTecServiceFeeBtTransportDataV2(publishProcessBO, computeDTO.getIsExcellentGoodsTwo());
        TecServiceFeeConfigComputeResultVO resultVO = new TecServiceFeeConfigComputeResultVO();
        if (Objects.nonNull(tecServiceFeeConfigComputeResult)) {
            BeanUtils.copyProperties(tecServiceFeeConfigComputeResult, resultVO);
            resultVO.setMeetCommissionRules(YesOrNoEnum.YES.getId());
            boolean multiCarHavePayOrder = calcCommissionTecFeeService.isMultiCarHavePayOrder(oldMain);
            resultVO.setIsMultiCarHavePayOrder(multiCarHavePayOrder);
        }
        log.info("computeTecServiceFeeByTransportData result:{}", JSONObject.toJSONString(resultVO));
        return resultVO;
    }


    @Override
    public CommissionTypeVO checkCommission(DrawCommissionReq req) {
        return calcCommissionTecFeeService.checkCommission(req);
    }
}
