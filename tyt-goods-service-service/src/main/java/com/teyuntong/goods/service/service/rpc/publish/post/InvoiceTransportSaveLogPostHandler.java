package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.service.biz.invoice.converter.InvoiceConverter;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceEnterpriseDO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportEnterpriseLogDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportEnterpriseLogService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 开票货源记录发布时货主的企业信息
 *
 * <AUTHOR>
 * @since 2025/03/07 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceTransportSaveLogPostHandler {

    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final InvoiceEnterpriseService invoiceEnterpriseService;

    /**
     * 发布保存开票信息
     *
     * @param transportMain
     * @param publishBO
     */
    public void saveForPublish(TransportMainDO transportMain, PublishBO publishBO) {


        if (Objects.equals(transportMain.getInvoiceTransport(), 1)) {
            try {
                Long userId = transportMain.getUserId();
                Long srcMsgId = transportMain.getSrcMsgId();

                InvoiceEnterpriseDO invoiceEnterprise = invoiceEnterpriseService.getByCertigierUserId(userId);
                TransportEnterpriseLogDO newLog = InvoiceConverter.INSTANCE.toLogDO(invoiceEnterprise);
                newLog.setId(null);
                newLog.setEnterpriseId(invoiceEnterprise.getId());
                newLog.setSrcMsgId(srcMsgId);
                newLog.setInvoiceSubjectId(publishBO.getInvoiceSubjectId());
                newLog.setServiceProviderCode(publishBO.getServiceProviderCode());
                newLog.setAssignCarTel(publishBO.getAssignCarTel());
                newLog.setEnterpriseTaxRate(transportMain.getEnterpriseTaxRate());

                newLog.setConsigneeName(publishBO.getConsigneeName());
                newLog.setConsigneeTel(publishBO.getConsigneeTel());
                newLog.setConsigneeEnterpriseName(publishBO.getConsigneeEnterpriseName());

                newLog.setCollectedPrice(publishBO.getCollectedPrice());
                newLog.setPrepaidPrice(publishBO.getPrepaidPrice());
                newLog.setReceiptPrice(publishBO.getReceiptPrice());
                newLog.setPaymentsType(publishBO.getPaymentsType());

                newLog.setCreateTime(new Date());
                newLog.setModifyTime(new Date());

                transportEnterpriseLogService.deleteBySrcMsgId(srcMsgId);
                transportEnterpriseLogService.save(newLog);
            } catch (Exception e) {
                log.error("开票货源保存发货时的企业信息异常", e);
            }
        }
    }

    /**
     * 直接发布保存开票信息
     *
     * @param processBO
     */
    public void saveForDirectPublish(BasePublishProcessBO processBO) {
        TransportMainDO newMainDO = processBO.getTransportMain();
        if (Objects.equals(newMainDO.getInvoiceTransport(), 1)) {
            //直接发布用旧货源ID查询旧货源的开票主体ID和服务商code，然后用新生成的货源ID保存
            try {
                Long userId = newMainDO.getUserId();
                Long srcMsgId = newMainDO.getSrcMsgId();
                Long oldSrcMsgId = processBO.getOldMain().getSrcMsgId();
                log.info("开票货源保存发货时的企业信息 货主ID:{}，货源ID:{}", userId, srcMsgId);

                InvoiceEnterpriseDO invoiceEnterprise = invoiceEnterpriseService.getByCertigierUserId(userId);
                TransportEnterpriseLogDO newLog = InvoiceConverter.INSTANCE.toLogDO(invoiceEnterprise);
                TransportEnterpriseLogDO oldLog = transportEnterpriseLogService.getBySrcMsgId(oldSrcMsgId);

                newLog.setId(null);
                newLog.setInvoiceSubjectId(oldLog.getInvoiceSubjectId());
                newLog.setSrcMsgId(srcMsgId);
                newLog.setEnterpriseId(invoiceEnterprise.getId());
                newLog.setServiceProviderCode(oldLog.getServiceProviderCode());
                newLog.setAssignCarTel(oldLog.getAssignCarTel());
                newLog.setEnterpriseTaxRate(oldLog.getEnterpriseTaxRate());

                newLog.setConsigneeName(oldLog.getConsigneeName());
                newLog.setConsigneeTel(oldLog.getConsigneeTel());
                newLog.setConsigneeEnterpriseName(oldLog.getConsigneeEnterpriseName());

                newLog.setCollectedPrice(oldLog.getCollectedPrice());
                newLog.setPrepaidPrice(oldLog.getPrepaidPrice());
                newLog.setReceiptPrice(oldLog.getReceiptPrice());
                newLog.setPaymentsType(oldLog.getPaymentsType());

                newLog.setCreateTime(new Date());
                newLog.setModifyTime(new Date());

                transportEnterpriseLogService.deleteBySrcMsgId(srcMsgId);
                transportEnterpriseLogService.save(newLog);
            } catch (Exception e) {
                log.error("开票货源保存发货时的企业信息异常", e);
            }
        }
    }

}
