package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportCarryDTO;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.ExcellentPriceCountService;
import com.teyuntong.goods.service.service.biz.transport.service.GoodCarPriceTransportService;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.enums.UseCarTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.user.ExcellentGoodsCardRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.permission.dto.GainExcellentGoodsCardRpcDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 自动转优车定价货源checker
 *
 * <AUTHOR>
 * @since 2025/02/21 11:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AutoConvertExcellentGoodsChecker {

    private final GoodCarPriceTransportService goodCarPriceTransportService;
    private final ExcellentGoodsCardRemoteService excellentGoodsCardRemoteService;
    private final ExcellentPriceCountService excellentPriceCountService;
    private final ThPriceService thPriceService;


    /**
     * 直接发布判断
     */
    public void checkDirectPublish(DirectPublishProcessBO processBO) {

        // 新版融合发货不允许自动转优车
        if (processBO.getBaseParam().getClientFusion() != null) {
            return;
        }

        TransportMainDO mainDO = processBO.getOldMain();
        // 专车货源不自动转
        if (ExcellentGoodsEnums.isSpecial(mainDO.getExcellentGoods())) {
            return;
        }

        // 拼车不自动转优车
        if (Objects.equals(UseCarTypeEnum.PART.getCode(), processBO.getOldMainExtend().getUseCarType())) {
            return;
        }

        // 用户发优车2.0电议货源
        checkExcellentGoodsTele(processBO);

        // 自动转优车2.0
        TransportCarryDTO transportCarryDTO = buildTransportCarryDTO(processBO.getDirectPublishBO(), mainDO);
        boolean autoConvertExcellent = goodCarPriceTransportService.checkTransportIsGoodCarPriceTransport(transportCarryDTO);
        if (autoConvertExcellent) {
            processBO.getDirectPublishBO().setGoodCarPriceTransport(1);
            processBO.getDirectPublishBO().setAutomaticGoodCarPriceTransport(true);

            // 历史货源重新发布，自动发一张优车发货卡
            if (processBO.getDirectPublishBO().isHistoryGoods()) {
                GainExcellentGoodsCardRpcDTO goodsCardRpcDTO = new GainExcellentGoodsCardRpcDTO();
                goodsCardRpcDTO.setUserId(mainDO.getUserId());
                goodsCardRpcDTO.setNew(true);
                Long excellentGoodsCardId = excellentGoodsCardRemoteService.gainExcellentGoodsCard(goodsCardRpcDTO);
                if (excellentGoodsCardId == null) {
                    throw BusinessException.createException(GoodsErrorCode.NO_PUBLISH_EXCELLENT.getCode(), "当前无可用优车发货卡，无法发布优车货源");
                }
                processBO.getDirectPublishBO().setExcellCardId(excellentGoodsCardId);
            }
        }
    }


    private TransportCarryDTO buildTransportCarryDTO(DirectPublishBO directPublishBO, TransportMainDO mainDO) {

        TransportCarryDTO transportCarryDTO = new TransportCarryDTO();
        transportCarryDTO.setStartProvince(mainDO.getStartProvinc());
        transportCarryDTO.setStartCity(mainDO.getStartCity());
        transportCarryDTO.setStartArea(mainDO.getStartArea());
        transportCarryDTO.setGoodTypeName(mainDO.getGoodTypeName());
        transportCarryDTO.setUserId(mainDO.getUserId());
        transportCarryDTO.setGoodsWeight(mainDO.getWeight());

        // 如果客户端修改了货源信息，则使用客户端信息
        UpdateGoodsInfoDTO updateGoodsInfo = directPublishBO.getUpdateGoodsInfoDTO();
        transportCarryDTO.setGoodsLength(updateGoodsInfo == null ? mainDO.getLength() : updateGoodsInfo.getLength());
        transportCarryDTO.setGoodsWide(updateGoodsInfo == null ? mainDO.getWide() : updateGoodsInfo.getWide());
        transportCarryDTO.setGoodsHigh(updateGoodsInfo == null ? mainDO.getHigh() : updateGoodsInfo.getHigh());
        transportCarryDTO.setGoodsHigh(updateGoodsInfo == null ? mainDO.getWeight() : updateGoodsInfo.getWeight());

        boolean isChangeDest = updateGoodsInfo != null
                && updateGoodsInfo.getDestLatitude() != null && updateGoodsInfo.getDestLongitude() != null;
        transportCarryDTO.setDestProvince(isChangeDest ? updateGoodsInfo.getDestProvinc() : mainDO.getDestProvinc());
        transportCarryDTO.setDestCity(isChangeDest ? updateGoodsInfo.getDestCity() : mainDO.getDestCity());
        transportCarryDTO.setDestArea(isChangeDest ? updateGoodsInfo.getDestArea() : mainDO.getDestArea());
        BigDecimal distance = isChangeDest ? updateGoodsInfo.getDistance() : mainDO.getDistance();
        transportCarryDTO.setDistance(distance == null ? "0" : distance.toString());

        // 如果客户端修改了价格和发货类型，则使用客户端价格
        transportCarryDTO.setPrice(StringUtils.isNotBlank(directPublishBO.getPrice()) ? directPublishBO.getPrice() : mainDO.getPrice());
        transportCarryDTO.setPublishType(directPublishBO.getPublishType() != null ? directPublishBO.getPublishType() : mainDO.getPublishType());

        transportCarryDTO.setOldSrcMsgId(mainDO.getSrcMsgId());

        return transportCarryDTO;
    }

    /**
     * 用户发优车2.0电议货源条件：
     * 1. 在优车2.0电议配置中
     * 2. 有剩余次数
     * 3. 自动转不消耗电议次数
     */
    public void checkExcellentGoodsTele(BasePublishProcessBO processBO) {
        Long userId = processBO.getUser().getId();

        // 编辑发布时手动选择 优车2.0+电议
        if (processBO instanceof PublishProcessBO publishProcessBO) {
            if (Objects.equals(publishProcessBO.getPublishBO().getGoodCarPriceTransport(), 1)
                    && publishProcessBO.getPublishBO().isUserManualExcellentGoods() // 用户手动勾选优车2.0电议才校验次数
                    && Objects.equals(publishProcessBO.getPublishBO().getPublishType(), PublishTypeEnum.TELE.getCode())) {
                checkRemainCount(userId, "您优车电议次数不足，请选择一口价");
                publishProcessBO.getPublishBO().setNeedUseExcellentGoodsTele(true);
            }

            // 直接发布
        } else if (processBO instanceof DirectPublishProcessBO directPublishProcessBO) {

            // 历史货源直接发布 优车2.0+电议
            if (Objects.equals(directPublishProcessBO.getOldMain().getExcellentGoodsTwo(), 2)
                    && Objects.equals(directPublishProcessBO.getOldMain().getPublishType(), PublishTypeEnum.TELE.getCode())
                    && directPublishProcessBO.getDirectPublishBO().isHistoryGoods()) {
                checkRemainCount(userId, "您优车电议次数不足，请编辑再发布");
                directPublishProcessBO.getDirectPublishBO().setNeedUseExcellentGoodsTele(true);

                // 当天货源 优车2.0 + 转电议
            } else if (Objects.equals(directPublishProcessBO.getOldMain().getExcellentGoodsTwo(), 2)
                    && directPublishProcessBO.getOptEnum().equals(PublishOptEnum.TRANSFER_TELE)) {
                checkRemainCount(userId, "您优车电议次数不足，无法转电议，请撤销再重发");
                directPublishProcessBO.getDirectPublishBO().setNeedUseExcellentGoodsTele(true);
            }

        }

    }

    /**
     * 校验是否有优车2.0电议次数
     */
    private void checkRemainCount(Long userId, String errorMsg) {
        Integer remainCount = excellentPriceCountService.getRemainCount(userId);
        if (remainCount <= 0) {
            throw BusinessException.createException(GoodsErrorCode.EXCELLENT_PRICE_TELE_LIMIT.getCode(), errorMsg);
        }
    }


    /**
     * 新老版本兼容：
     * <pre>
     *  a. 老版本优车（1.0、2.0），在新版货源根据定价金额映射极速优车、快速优车、特惠优车，若原价格低于特惠优车或无价，映射为特惠优车
     *  b. 老版本优车，若在新版未成功获取优车定价的
     *      i. 有运费：用户出价+原来的议价方式（一口价、电议）
     *      ii. 无运费：普通找车
     *  c. 老版本货源若是优车电议，在新版本也展示为极速优车、快速优车、特惠优车+电议
     *  d. 老版本普货一口价，新版本展示为用户出价+一口价
     *  e. 老版本普货电议有价，新版本展示为用户出价+电议
     *  f. 老版本电议无价，新版本展示为普通找车
     *  g. 以上映射货源，在新版本APP进行直接发布时，按照原货源直接发布
     * </pre>
     */
    public void setExcellentGoodsType(DirectPublishProcessBO processBO) {

        TransportMainDO oldMain = processBO.getOldMain();
        boolean hasPrice = TransportUtil.hasPrice(processBO.getDirectPublishBO().getPrice())
                || TransportUtil.hasPrice(oldMain.getPrice()); // 优先判断填价加价的价格，否则使用旧货源价格

        PublishGoodsTypeEnum goodsTypeEnum;
        if (ExcellentGoodsEnums.isNormal(oldMain.getExcellentGoods())) {
            // 普通发货，有价=>用户出价，无价=>普通找车
            if (hasPrice) {
                goodsTypeEnum = PublishGoodsTypeEnum.USER_PRICE_GOODS;
            } else {
                goodsTypeEnum = PublishGoodsTypeEnum.NORMAL_GOODS;
            }
        } else if (ExcellentGoodsEnums.isSpecial(oldMain.getExcellentGoods())) {
            goodsTypeEnum = PublishGoodsTypeEnum.SPECIAL_GOODS;
        } else {
            CarryPriceVO thPrice = thPriceService.getThPrice(oldMain);
            if (thPrice != null) {
                processBO.setThPrice(thPrice); // extendBuilder需要记录

                String newPrice = processBO.getDirectPublishBO().getPrice();
                String price = StringUtils.isNotBlank(newPrice) ? newPrice : oldMain.getPrice();

                goodsTypeEnum = TransportUtil.judgeExcellentGoodsLevel(price,
                        thPrice.getFixPriceMin(), thPrice.getFixPriceMax(), thPrice.getFixPriceFast());

                // 原价格低于特惠优车或无价，映射为特惠优车
                if (goodsTypeEnum == null) {
                    goodsTypeEnum = PublishGoodsTypeEnum.EXCELLENT_GOODS;
                }
            } else {
                // 若在新版未成功获取优车定价，有价=>用户出价，无价=>普通找车
                if (hasPrice) {
                    goodsTypeEnum = PublishGoodsTypeEnum.USER_PRICE_GOODS;
                } else {
                    goodsTypeEnum = PublishGoodsTypeEnum.NORMAL_GOODS;
                }
            }
        }

        processBO.getDirectPublishBO().setPublishGoodsTypeEnum(goodsTypeEnum);
    }

}
