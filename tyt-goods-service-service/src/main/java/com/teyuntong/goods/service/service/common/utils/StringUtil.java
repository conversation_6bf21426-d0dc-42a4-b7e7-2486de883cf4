package com.teyuntong.goods.service.service.common.utils;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * User: Administrator
 * Date: 14-2-8
 * Time: 下午10:26
 */
@Slf4j
public class StringUtil {
    static final String pattern = "【包邮】|【限时促销】|发票";
    public static boolean isAvalidContent(String s) {
        if(s != null) {
            if(s.matches(pattern) || s.contains("http://")) {
                return false;
            }
        }
        return true;
    }

    /*
     * 判断是否为浮点数，包括double和float
     * @param str 传入的字符串
     * @return 是浮点数返回true,否则返回false
   */
    public static boolean isDouble(String str) {
        if(str==null||str.equals("")) {
            return false;
        }

        boolean result = true;

        try {
            double v = Double.parseDouble(str);
        } catch (Exception e) {
            log.error("Str is not a number : " + str);
            result = false;
        }

        //boolean number = NumberUtils.isNumber(str);
        //Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        //Pattern pattern =Pattern.compile("^[0-9]*(.[0-9]+)? *$");

        return result;
    }

    public static String getRealIp(HttpServletRequest request) {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");

            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");

            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");

            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");

            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = (String) ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        if("0:0:0:0:0:0:0:1".equals(ip)){
            ip = "127.0.0.1";
        }
        return ip;

    }

    /**
     * 过滤表情符
     * @param str
     * @return
     */
    public static String filterEmotion(String str){
    	return str.replaceAll("/[\u4E00-\u9FB0]{2}", "");
    }
    /**
     * 只保留汉字，字母，数字
     * @param str
     * @return
     */
    public static String filterPunctuation(String str){
    	return str.replaceAll("[^0-9a-zA-Z\u4e00-\u9fa5]","");
    }
    /**
     * 只保留汉字，字母，数字和标点
     * @param str
     * @return
     */
    public static String filterChinesePunctuation(String str){
        String pattern = "[\u4E00-\u9FA5]|[\\w]|[,.，。@#￥*！：!~$]";// 汉字
        return str.replaceAll(pattern,"");
    }
    /**
     * 过滤空格
     * @param str
     * @return
     */
    public static String filterBlank(String str){
    	return str.replaceAll(" ", "");
    }
    /**
     * 过滤[数字].字符，为处理老版本bug
     * @param str
     * @return
     */
    public static String filterTyt(String str){
    	if(str==null) {
            return null;
        }
    	return str=str.replaceAll("\\[[0-9]{1,}\\]\\.", "");
    }

    public static String formatUserName(String name,String userId){
    	String userName=" ";
    	if(StringUtils.isNotBlank(name)&&!"null".equals(name)){
    		userName=name;
    	}else{
//    		if(userId.length()>4){
//    			userName="会员"+userId.substring(userId.length()-4);
//    		}else{
//    			userName="会员"+userId;
//    		}
    		userName="用户"+userId;
    	}

    	return userName;
    }

    public static String subStr(String str){
        int beginIndex=str.indexOf("[");
        int endIndex=str.indexOf("]");
        if(beginIndex>-1&&endIndex>-1){
            return str.substring(endIndex+2);
        }else{
            return str;
        }
    }

    /**
     * 获得货物的hashcode,精准推荐使用
     *
     * @param transport
     * @return
     */
    public static String getHashCodeNewByRecommend(TransportDO transport) {
    	if(transport == null){
            System.out.println("HashException：获得货物的hashcode,精准推荐使用，货物为空");
            return "";
        }
        String code = transport.getStartPoint() + transport.getDestPoint();
        if (org.springframework.util.StringUtils.hasLength(transport.getTel())) {
            code += transport.getTel();
        }
        if (org.springframework.util.StringUtils.hasLength(transport.getTel3())) {
            code += transport.getTel3();
        }
        if (org.springframework.util.StringUtils.hasLength(transport.getTel4())) {
            code += transport.getTel4();
        }
        code += transport.getStatus();
        code += TimeUtil.formatDate(new Date());
       /*if (org.springframework.util.StringUtils.hasLength(transport.getPubQQ() + ""))
            code += transport.getPubQQ();*/
 /*       if (org.springframework.util.StringUtils.hasLength(transport.getStartDetailAdd()))
            code += transport.getStartDetailAdd();
        if (org.springframework.util.StringUtils.hasLength(transport.getDestDetailAdd()))
            code += transport.getDestDetailAdd();*/
      /*  if (org.springframework.util.StringUtils.hasLength(transport.getLength()))
            code += transport.getLength();
        if (org.springframework.util.StringUtils.hasLength(transport.getWide()))
            code += transport.getWide();
        if (org.springframework.util.StringUtils.hasLength(transport.getHigh()))
            code += transport.getHigh();*/
//        if (org.springframework.util.StringUtils.hasLength(transport.getPrice()))
//            code += transport.getPrice();
       /* if (org.springframework.util.StringUtils.hasLength(transport.getWeight()))
            code += transport.getWeight();*/
  /*      if (org.springframework.util.StringUtils.hasLength(transport.getIsSuperelevation()))
            code += transport.getIsSuperelevation();*/
       /* if (org.springframework.util.StringUtils.hasLength(transport.getRemark()))
            code += transport.getRemark();*/
        //新增字段  货物标准化
        if (org.springframework.util.StringUtils.hasLength(transport.getUserId()+"")) {
            code += transport.getUserId();
        }
    /*    if (org.springframework.util.StringUtils.hasLength(transport.getIsStandard()+""))
            code += transport.getIsStandard();*/
        if (org.springframework.util.StringUtils.hasLength(transport.getMatchItemId()+"")) {
            code += transport.getMatchItemId();
        }
        if (org.springframework.util.StringUtils.hasLength(transport.getGoodNumber()+"")) {
            code += transport.getGoodNumber();
            }

        //code += transport.getIsIn0foFee();// 需要支付跟不需要支付算两条信息
        return code.hashCode() + "";
    }

    /**
     *  根据出发地目的地生成地图数据对应的key
     * @param startProvinc
     * @param startCity
     * @param startArea
     * @param destProvinc
     * @param destCity
     * @param destArea
     * @return
     */
    public static String getMapKey(String startProvinc, String startCity, String startArea, String destProvinc, String destCity, String destArea){
        StringBuffer sb = new StringBuffer();
        sb.append(startProvinc).append("-").append(startCity).append("-").append(startArea).append("__").append(destProvinc).append("-").append(destCity).append("-").append(destArea);
        return MD5Util.GetMD5Code(sb.toString());
    }

    //将指定位置字符串替换为4个星号
    public static String replaceStr4StarByIndex(String str,int begin,int end){
        if(StringUtils.isEmpty(str)){
            return null;
        }
        int length = str.length();
        System.out.println(length);
        if(begin < 0  || begin >= length || end >= length){
            System.out.println("截取范围超出字符串长度");
            return null;
        }
        String temp = str.substring(0,begin)+"****"+str.substring(end,length);
        return temp;
    }

    /**
     * 隐藏手机号中间4位
     *
     * @param mobile
     * @return
     */
    public static String replaceMobile(String mobile) {
        mobile = mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        return mobile;
    }


    //将传入的值除以100，然后返回字符串形式
    public static String getMovePointLeft2(Integer value){
        if(value == null){
            return null;
        }
        return new BigDecimal(value).movePointLeft(2).toString();
    }

    /**
     * 任何一个字符串相等，则返回true
     * @param string
     * @param searchStrings
     * @return
     */
    public static boolean equalsWithAny(final CharSequence string, final CharSequence... searchStrings) {
        if (StringUtils.isEmpty(string) || ArrayUtils.isEmpty(searchStrings)) {
            return false;
        }
        for (final CharSequence searchString : searchStrings) {
            if (StringUtils.equals(string, searchString)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 扰乱11位字符串的 6-8位；
     * @param phone 11位字符串
     * @return
     */

    public static String disturbPhone(String phone){
        if(StringUtils.isEmpty(phone)){
            return phone;
        }
        char s [] =phone.toCharArray();
        if (s.length<11){
            return phone;
        }

        s[6]=(char) ((phone.charAt(1)+phone.charAt(8)-phone.charAt(6))%9+48);
        s[7]=(char) ((phone.charAt(1)+phone.charAt(9)-phone.charAt(7))%9 +48);
        s[8]=(char) ((phone.charAt(1)+phone.charAt(10)-phone.charAt(8))%9 +48);

        return  new String(s);

    }

    /**
     * 用户昵称校验
     * @param userName
     * @return
     */
    private static final String name1="特运通";
    private static final String name2="邦利德";
    private static final String name3="TYT";
    private static final String name4="TEYUNTONG";
    public static boolean checkUserName(String userName){
        String chinese = userName.replaceAll("\\s*", "").replaceAll("[^(\\u4e00-\\u9fa5)]", "");
        if (chinese.contains(name1)){
            return false;
        }
        if (chinese.contains(name2)){
            return false;
        }
        String s = userName.replaceAll("[ +|\\pP\\p{Punct}]","");
        String unChinese=s.toUpperCase();
        if (unChinese.contains(name3)){
            return false;
        }
        if (unChinese.contains(name4)){
            return false;
        }
        return true;
    }

    /**
     * 隐藏字符串中的手机号
     * @param value
     * @return
     */
    public static String hidePhoneInStr(String value){
        if(StringUtils.isBlank(value)){
            return value;
        }
        String reg = "\\d{11,}";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(value);
        while(matcher.find()){
            value = value.replace(matcher.group(),"***");
        }
        return value;
    }

    /**
     * 是否是数字包括小数
     * @param text
     * @param separator
     * @return
     */
    public static boolean splitAllNumber(String text, String separator){
        boolean result = true;

        String[] splitArray = text.split(separator);

        for(String oneText: splitArray){

            if(!NumberUtils.isNumber(oneText)){
                result = false;
                break;
            }

        }
        return result;
    }

    /**
     * 是否都是整数
     * @param text
     * @param separator
     * @return
     */
    public static boolean splitAllDigits(String text, String separator){
        boolean result = true;

        String[] splitArray = text.split(separator);

        for(String oneText: splitArray){
            if(!NumberUtils.isDigits(oneText)){
                result = false;
                break;
            }
        }
        return result;
    }

    /**
     * 获取字符串
     * @param text
     * @param defaultValue
     * @return
     */
    public static String getStringValue(String text, String defaultValue){
        if(text == null){
            text = defaultValue;
        }
        return text;
    }

    /**
     * 清除乱码，防止mysql 存不进去.
     * @param text text
     * @return String
     */
    public static String clearGarbledCode (String text){
        if(text == null){
            return null;
        }

        String reg = "[\ud800\udc00-\udbff\udfff\ud800-\udfff]";

        String result = text.replaceAll(reg, "");

        boolean equals = text.equals(result);
        //System.out.println(equals);

        return result;
    }

    /**
     * 是否包含空字符串.
     * @param textArray textArray
     * @return boolean
     */
    public static boolean hasBlank(String ... textArray){

        if(textArray == null){
            return false;
        }

        for(String oneText : textArray){
            if(StringUtils.isBlank(oneText)){
                return true;
            }
        }
        return false;
    }


    public static  void main(String[] args) {
//         System.out.println(StringUtil.isAvalidContent("http://redirect.simba.taobao.com"));
//         System.out.println(StringUtil.isAvalidContent("【包邮】"));
//         System.out.println(StringUtil.isAvalidContent("【限时促销】"));
//        System.out.println(StringUtil.isAvalidContent("内蒙"));

//        System.out.println(isNumeric("222.22"));
//        System.out.println(isDouble(null));
//        System.out.println("今天天气好好啊，很开心很开心 /微笑/微笑/微笑".replaceAll("/[\u4E00-\u9FB0]{2}", ""));
//        System.out.println("今天天气好好啊，ww很开心cc123,?r".replaceAll("[^0-9a-zA-Z\u4e00-\u9fa5]",""));
//          System.out.println(("nin").hashCode());
//    	String aa="[2334wwwwww].3333";
//    	String ss=aa.replaceAll("\\[.*\\]\\.", "");
//    	System.out.println(filterTyt(""));
//        String s="1//3";
//        System.out.println(s.split("/").length);
//        String rStatus = "3";
//        System.out.println(equalsWithAny(rStatus, "3", "5", "4"));i
        //13498040064
        System.out.println("13498009764");
        System.out.println(disturbPhone("13498009764"));

        System.out.println("ddd=="+isDouble(".005"));


    }

}

