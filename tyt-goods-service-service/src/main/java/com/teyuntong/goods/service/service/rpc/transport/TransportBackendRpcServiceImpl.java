package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.dto.TransportBackendDTO;
import com.teyuntong.goods.service.client.transport.service.TransportBackendRpcService;
import com.teyuntong.goods.service.client.transport.vo.TransportBackendVO;
import com.teyuntong.goods.service.service.biz.transport.config.BackendGoodsStatusEnum;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportBackendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportBackendService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 运输主表后台（小程序）服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
public class TransportBackendRpcServiceImpl implements TransportBackendRpcService {

    private final TransportBackendService transportBackendService;

    @Override
    public TransportBackendVO query(TransportBackendDTO transportBackendDTO) {
        TransportBackendDO transportBackendDO = transportBackendService.selectBySrcMsgId(transportBackendDTO.getSrcMsgId());
        return TytBeanUtil.convertBean(transportBackendDO, TransportBackendVO.class);
    }

    @Override
    public TransportBackendVO queryBySrcMsgId(Long srcMsgId) {
        if (srcMsgId == null || srcMsgId == 0L) {
            throw new BusinessException(GoodsErrorCode.ERROR_SRC_MSG_ID_LACK);
        }
        TransportBackendDO transportBackendDO = transportBackendService.selectBySrcMsgId(srcMsgId);
        return TytBeanUtil.convertBean(transportBackendDO, TransportBackendVO.class);
    }

    @Override
    public void modifyConfirmLoadingStatus(Long srcMsgId) {
        if (srcMsgId == null || srcMsgId == 0L) {
            throw new BusinessException(GoodsErrorCode.ERROR_SRC_MSG_ID_LACK);
        }
        TransportBackendDTO transportBackendDTO = new TransportBackendDTO();
        transportBackendDTO.setSrcMsgId(srcMsgId);
        transportBackendDTO.setStatus(BackendGoodsStatusEnum.CONFIRM_LOADING.getStatus());
        transportBackendDTO.setCarriageTime(new Date());
        transportBackendService.updateBySrcMsgId(transportBackendDTO);
    }

}
