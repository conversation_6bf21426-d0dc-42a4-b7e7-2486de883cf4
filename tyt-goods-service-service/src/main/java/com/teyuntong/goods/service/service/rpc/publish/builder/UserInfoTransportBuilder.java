package com.teyuntong.goods.service.service.rpc.publish.builder;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.utils.IdCardUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.UserPermissionTypeEnum;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserSubRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 用户信息构建器
 *
 * <AUTHOR>
 * @since 2025/02/21 15:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoTransportBuilder {

    private final ABTestRemoteService abTestRemoteService;
    private final UserRemoteService userRemoteService;
    private final UserPermissionRemoteService userPermissionRemoteService;

    public void build(DirectPublishProcessBO processBO) {
        TransportMainDO newMainDO = processBO.getTransportMain();
        Long userId = processBO.getUser().getId();

        UserRpcVO user = userRemoteService.getUser(userId);
        newMainDO.setVerifyFlag(user.getVerifyFlag());
        newMainDO.setVerifyPhotoSign(user.getVerifyPhotoSign());
        newMainDO.setNickName(makeNickName(newMainDO.getExcellentGoods(), user));
        newMainDO.setPubQq(user.getQq());
        newMainDO.setUploadCellphone(user.getCellPhone());
        newMainDO.setIsCar(user.getIsCar());
        newMainDO.setUserPart(user.getUserPart());
        newMainDO.setRegTime(user.getCtime());

        // 权益查询
        UserPermissionRpcDTO userPermissionRpcDTO = new UserPermissionRpcDTO();
        userPermissionRpcDTO.setServicePermissionTypeId(UserPermissionTypeEnum.GOODS_VIP.getTypeId());
        userPermissionRpcDTO.setUserId(userId);
        List<UserPermissionRpcVO> userPermissionList = userPermissionRemoteService.getUserPermissionByUserId(userPermissionRpcDTO);
        newMainDO.setUserType(CollectionUtils.isNotEmpty(userPermissionList) ? 1 : 0);

        // userShowName
        String userShowName;
        if (StringUtils.isEmpty(user.getTrueName()) || StringUtils.isEmpty(user.getIdCard())) {
            userShowName = user.getUserName();
        } else {
            userShowName = user.getTrueName().charAt(0) + IdCardUtil.getCallGender(user.getIdCard());
        }
        newMainDO.setUserShowName(userShowName);

        // 设置用户信用积分及等级
        ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(userId);
        newMainDO.setTotalScore(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getTotalScore).orElse(BigDecimal.ZERO));
        newMainDO.setRankLevel(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getRankLevel).orElse(1));

        // 用户成交单数
        UserSubRpcVO userSub = userRemoteService.getUserSubById(userId);
        newMainDO.setTradeNum(Optional.ofNullable(userSub).map(UserSubRpcVO::getDealNum).orElse(0));

    }

    public String makeNickName(Integer excellentGoods, UserRpcVO user) {
        boolean isShowUserName = false;

        if (Objects.equals(excellentGoods, 1)) {
            //如果是优车并且该货主不在AB测试中才将昵称赋值为X老板
            Integer userType = abTestRemoteService.getUserType("is_show_username", user.getId());
            if (Objects.equals(userType, 1)) {
                isShowUserName = true;
            }
        } else {
            Integer userType = abTestRemoteService.getUserType("show_boss_nick_name", user.getId());
            if (Objects.equals(userType, 0)) {
                isShowUserName = true;
            }
        }
        if (isShowUserName) {
            return StringUtils.isBlank(user.getUserName()) ? "用户" + user.getId() : user.getUserName();
        } else {
            String surname = StringUtils.isBlank(user.getTrueName()) ? "" : Character.toString(user.getTrueName().charAt(0));
            return surname + "老板";
        }
    }
}
