package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.transport.dto.GoodsPointDTO;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.enums.UseCarTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 拼车货源校验是否满足拼车条件
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CarpoolTransportChecker {

    private final TransportMainRpcService transportMainRpcService;

    /**
     * 直接发布校验
     */
    public void check(DirectPublishProcessBO processBO) {

        TransportMainExtendDO mainExtend = processBO.getMainExtend();
        if (Objects.nonNull(mainExtend) && Objects.equals(UseCarTypeEnum.PART.getCode(), mainExtend.getUseCarType())) {

            TransportMainDO oldMain = processBO.getOldMain();
            GoodsPointDTO goodsPointDTO = new GoodsPointDTO();
            goodsPointDTO.setStartCity(oldMain.getStartCity());
            goodsPointDTO.setDestCity(oldMain.getDestCity());
            goodsPointDTO.setDistance(oldMain.getDistance());
            goodsPointDTO.setWeight(StringUtils.isBlank(oldMain.getWeight()) ? null : new BigDecimal(oldMain.getWeight()));
            goodsPointDTO.setUserId(oldMain.getUserId());
            goodsPointDTO.setInvoiceTransport(oldMain.getInvoiceTransport());

            Boolean b = transportMainRpcService.checkMatchCarpool(goodsPointDTO);
            if (!Objects.equals(b, Boolean.TRUE)) {
                throw new BusinessException(GoodsErrorCode.CARPOOL_PUBLISH_ERROR);
            }
        }
    }


    /**
     * 直接发布校验
     */
    public void check(PublishBO publishBO, UserRpcVO user) {

        Integer useCarType = publishBO.getUseCarType();
        if (Objects.equals(UseCarTypeEnum.PART.getCode(), useCarType)) {

            GoodsPointDTO goodsPointDTO = new GoodsPointDTO();
            goodsPointDTO.setStartCity(publishBO.getStartCity());
            goodsPointDTO.setDestCity(publishBO.getDestCity());
            goodsPointDTO.setDistance(publishBO.getDistance());
            goodsPointDTO.setWeight(StringUtils.isBlank(publishBO.getWeight()) ? null : new BigDecimal(publishBO.getWeight()));
            goodsPointDTO.setUserId(user.getId());
            goodsPointDTO.setInvoiceTransport(publishBO.getInvoiceTransport());

            Boolean b = transportMainRpcService.checkMatchCarpool(goodsPointDTO);
            if (!Objects.equals(b, Boolean.TRUE)) {
                throw BusinessException.createException(GoodsErrorCode.CARPOOL_PUBLISH_ERROR.getCode(), "不满足拼车条件，请选择整车发货");
            }
        }
    }


}
