package com.teyuntong.goods.service.service.rpc.publish.commission;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.AppCallLogMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportViewLogMapper;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TecServiceFeeDiscountConfigMapper;
import com.teyuntong.goods.service.service.biz.commission.service.*;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.DispatchCompanyMapper;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.bi.BiGoodModelResult;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.outer.export.service.client.common.old.enums.ResponseEnum;
import com.teyuntong.outer.export.service.client.common.old.exception.TytException;
import com.teyuntong.trade.service.client.orders.enums.OrderPayStatusEnum;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.time.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;

/**
 * 计算抽佣技术服务费service
 *
 * <AUTHOR>
 * @since 2025/02/18 16:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CalcCommissionTecFeeService {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final ABTestRemoteService abTestRemoteService;
    private final TransportMainService transportMainService;
    private final DrawCommissionRuleService drawCommissionRuleService;
    private final RedisUtil redisUtil;
    private final TecServiceFeeConfigService tecServiceFeeConfigService;
    private final TecServiceFeeV2ConfigService tecServiceFeeV2ConfigService;
    private final DispatchCompanyMapper dispatchCompanyMapper;
    private final TecServiceFeeV2RouteConfigService tecServiceFeeV2RouteConfigService;
    private final TecServiceFeeStageConfigService tecServiceFeeStageConfigService;
    private final TecServiceFeeV2StageConfigService tecServiceFeeV2StageConfigService;
    private final TecServiceFeeProportionConfigService tecServiceFeeProportionConfigService;
    private final TecServiceFeeV2ProportionConfigService tecServiceFeeV2ProportionConfigService;
    private final TecServiceFeeDiscountConfigMapper tecServiceFeeDiscountConfigMapper;
    private final TecServiceFeeV2DiscountConfigService tecServiceFeeV2DiscountConfigService;
    private final AppCallLogMapper appCallLogMapper;
    private final TransportViewLogMapper transportViewLogMapper;
    private final ThPriceRpcService thPriceRpcService;
    private final OrdersRemoteService ordersRemoteService;

    public TecServiceFeeConfigComputeResult makeTecServiceFeeData(BasePublishProcessBO basePublishProcessBO,
                                                                  boolean isExcellentGoodsTwo) {

        TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = null;
        TransportMainDO transportMain = basePublishProcessBO.getTransportMain();
        TransportMainDO oldMain = basePublishProcessBO.getOldMain();
        BiGoodModelResult biGoodModelResult = basePublishProcessBO.getBiGoodModelResult();
        // 如果bi给的标识为抽佣，就走抽佣逻辑，否则不走抽佣逻辑
        log.info("抽佣数据构建，userId:{}, taskContent:{}, biGoodModelResult：{}", transportMain.getUserId(),
                transportMain.getTaskContent(), JSONObject.toJSONString(biGoodModelResult));
        if (Objects.equals(transportMain.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) || biGoodModelResult == null
                || isGoodMediumTransport(basePublishProcessBO.getMainExtend().getGoodTransportLabel()) || Objects.equals(biGoodModelResult.getCommission_flag(), 0)) {
            Integer userType = abTestRemoteService.getUserType(AbtestKeyConstant.COMMISSION_TEC_SERVICE_FEE_V2, transportMain.getUserId());
            if (Objects.equals(userType, YesOrNoEnum.YES.getId())) {
                tecServiceFeeConfigComputeResult = this.computeTecServiceFeeBtTransportDataV2(basePublishProcessBO, isExcellentGoodsTwo);
            } else {
                tecServiceFeeConfigComputeResult = this.computeTecServiceFeeBtTransportData(basePublishProcessBO, isExcellentGoodsTwo);
            }
        }
        log.info("抽佣数据构建，userId:{}, taskContent:{}, tecServiceFeeConfigComputeResult：{}", transportMain.getUserId(),
                transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
        transportMain.setTecServiceFee(null);

        if (tecServiceFeeConfigComputeResult != null) {
            // 多车找货，并且有支付成功订单，不再重新计算技术服务费
            if (isMultiCarHavePayOrder(oldMain)) {
                tecServiceFeeConfigComputeResult.setIsMultiCarHavePayOrder(true);
            } else {
                BigDecimal tecServiceFee = tecServiceFeeConfigComputeResult.getTecServiceFee();
                if (tecServiceFee == null) {
                    tecServiceFee = new BigDecimal(tytConfigRemoteService.getIntValue(ConfigKeyConstant.DEFAULT_TEC_SERVICE_FEE, 50));
                }
                transportMain.setTecServiceFee(tecServiceFee);
                //抽佣货源打标
                TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
                if (transportLabelJson == null) {
                    transportLabelJson = new TransportLabelJson();
                }
                transportLabelJson.setCommissionTransport(1);
                transportLabelJson.setCommissionLabel(biGoodModelResult == null ? "" : biGoodModelResult.getCommission_label());
                transportLabelJson.setMeetCommissionRules(basePublishProcessBO.getMeetCommissionRules());
                transportMain.setLabelJson(transportLabelJson.getJsonText());
            }
        } else {
            //抽佣货源标识去掉
            TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson != null) {
                if (transportLabelJson.getCommissionTransport() != null) {
                    transportLabelJson.setCommissionTransport(null);
                    //如果曾经抽佣现在不抽了，要把main表技术服务费字段修改为0
                    transportMain.setTecServiceFee(new BigDecimal(0));
                }
                transportLabelJson.setCommissionLabel(biGoodModelResult == null ? "" : biGoodModelResult.getCommission_label());
                transportLabelJson.setMeetCommissionRules(basePublishProcessBO.getMeetCommissionRules());
                transportMain.setLabelJson(transportLabelJson.getJsonText());
            } else {
                if (biGoodModelResult != null) {
                    transportLabelJson = new TransportLabelJson();
                    transportLabelJson.setCommissionLabel(biGoodModelResult.getCommission_label());
                    transportMain.setLabelJson(transportLabelJson.getJsonText());
                }
            }

        }
        return tecServiceFeeConfigComputeResult;
    }

    /**
     * 是否是多车找货，并且有支付成功订单
     *
     * @param oldMain
     * @return
     */
    public boolean isMultiCarHavePayOrder(TransportMainDO oldMain) {
        boolean result = false;
        if (Objects.nonNull(oldMain) && Objects.nonNull(oldMain.getShuntingQuantity()) && oldMain.getShuntingQuantity() > 1) {
            try {

                if (Objects.nonNull(oldMain.getSrcMsgId()) && oldMain.getCtime().compareTo(TimeUtil.getStartOfDay(new Date())) > 0) {
                    List<TransportOrdersVO> orders = ordersRemoteService.getByTsId(oldMain.getSrcMsgId());
                    if (CollectionUtils.isNotEmpty(orders)) {
                        long paySuccessCount = orders.stream().filter(v -> Objects.equals(v.getPayStatus(), OrderPayStatusEnum.PAY_SUCCESS.getCode())).count();
                        result = paySuccessCount > 0;
                    }
                }
            } catch(Exception e){
                log.error("isMultiCarHavePayOrder error, srcMsgId:{}", oldMain.getSrcMsgId(), e);
            }
            log.info("isMultiCarHavePayOrder result:{}, srcMsgId:{}", result, oldMain.getSrcMsgId());
        }
        return result;
    }

    /**
     * 抽佣技术服务费计算v2
     *
     * @param basePublishProcessBO
     * @param isExcellentGoodsTwo
     * @return
     */
    public TecServiceFeeConfigComputeResult computeTecServiceFeeBtTransportDataV2(BasePublishProcessBO basePublishProcessBO, boolean isExcellentGoodsTwo) {
        TransportMainDO transportMain = basePublishProcessBO.getTransportMain();
        if (transportMain.getRefundFlag() == null) {
            log.info("抽佣金额计算v2-货源订金退还方式为空。userId: {}, taskContent:{}", transportMain.getUserId(), transportMain.getTaskContent());
            return null;
        }
        TransportMainDO oldMain = basePublishProcessBO.getOldMain();
        TransportMainExtendDO mainExtend = basePublishProcessBO.getMainExtend();
        BigDecimal commissionScore = mainExtend.getCommissionScore();
        if (commissionScore == null || commissionScore.compareTo(BigDecimal.ZERO) < 0) {
            commissionScore = new BigDecimal("-1");
        }

        boolean isCommissionTransport = getIsCommissionTransport(transportMain, mainExtend, basePublishProcessBO, commissionScore);
        if (!isCommissionTransport) {
            log.info("抽佣金额计算v2-不满足抽佣条件，userId：{}, taskContent:{}", transportMain.getUserId(), transportMain.getTaskContent());
            return null;
        }

        // 无价货源优车指导价
        String goodCarPriceTransportCarryPrice = null;
        // 抽佣金额计算原始运费
        String priceString = transportMain.getPrice();
        if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
            // 使用优车指导价
            priceString = makePriceByGoodCarPriceTransportCarryPrice(transportMain);
            if (StringUtils.isNotBlank(priceString) && new BigDecimal(priceString).compareTo(BigDecimal.ZERO) > 0) {
                goodCarPriceTransportCarryPrice = priceString;
            }
        }
        log.info("抽佣金额计算v2- userId：{}, taskContent:{},本次发货计算抽佣金额时原始运费为：{}", transportMain.getUserId(), transportMain.getTaskContent(), priceString);

        // 订金不可退的：按照司机净得运费计算百分比（净得运费=运费-订金）
        if (Objects.equals(RefundFlagEnum.NO_RETURN.getCode(), transportMain.getRefundFlag())) {
            if (Objects.nonNull(transportMain.getInfoFee()) && StringUtils.isNotBlank(priceString)) {
                BigDecimal purePrice = new BigDecimal(priceString).subtract(transportMain.getInfoFee());
                if (purePrice.compareTo(BigDecimal.ZERO) > 0) {
                    priceString = purePrice.toString();
                }
            }
        }
        log.info("抽佣金额计算v2- userId：{}, taskContent:{},本次发货计算抽佣金额时最终运费为：{}", transportMain.getUserId(), transportMain.getTaskContent(), priceString);

        // 默认技术服务费
        Integer defaultTecServiceFee = tytConfigRemoteService.getIntValue("default_tec_service_fee", 50);
        if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
            // 用户没有手填运费并且也没有通过BI接口获取到建议运费
            defaultTecServiceFee = tytConfigRemoteService.getIntValue("default_tec_service_fee_no_price", 20);
        }

        // 货源首发时间间隔
        Long firstPublishTimeMinute = getFirstPublishTimeMinute(oldMain);

        // 满足条件的抽佣技术服务费配置
        TecServiceFeeV2ConfigDO tecServiceFeeConfig = getTecServiceFeeV2ConfigDO(mainExtend, transportMain);
        if (Objects.isNull(tecServiceFeeConfig)) {
            return getDefaultTecServiceFeeConfigComputeResult(isExcellentGoodsTwo, defaultTecServiceFee, commissionScore, firstPublishTimeMinute, transportMain, mainExtend);
        }

        TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = new TecServiceFeeConfigComputeResult();
        BeanUtils.copyProperties(tecServiceFeeConfig, tecServiceFeeConfigComputeResult);
        tecServiceFeeConfigComputeResult.setUseCommissionScoreStageConfig(true);
        tecServiceFeeConfigComputeResult.setCommissionScore(commissionScore);
        tecServiceFeeConfigComputeResult.setCarMemberType(0);
        tecServiceFeeConfigComputeResult.setGoodCarPriceTransportCarryPrice(goodCarPriceTransportCarryPrice);
        tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(new BigDecimal(defaultTecServiceFee));

        // 满足超时免佣条件一的时间，如果不满足则为null
        Date matchConditionEarliestTime = getMatchConditionEarliestTime(oldMain, tecServiceFeeConfigComputeResult);
        log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 满足条件一时间：{}, 货源首发时间间隔:{}", transportMain.getUserId(),
                transportMain.getTaskContent(), matchConditionEarliestTime, firstPublishTimeMinute);

        // 未满足条件一的情况下是否符合条件二直接免佣
        boolean overTimeToFree = isOverTimeToFree(matchConditionEarliestTime, tecServiceFeeConfigComputeResult, firstPublishTimeMinute);
        log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 超时免佣:{}", transportMain.getUserId(), transportMain.getTaskContent(), overTimeToFree);
        // 货源是否满足条件免佣
        boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(transportMain, mainExtend, isExcellentGoodsTwo, tecServiceFeeConfigComputeResult);
        log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 条件免佣:{}", transportMain.getUserId(), transportMain.getTaskContent(), matchConditionToFree);

        // 货源是否免佣
        boolean freeTec = overTimeToFree || matchConditionToFree;
        if (freeTec) {
            tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(0));
        } else {
            tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
        }

        BigDecimal distance = transportMain.getDistance();
        if (Objects.isNull(distance) || distance.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 未获取到运距。结果：{}", transportMain.getUserId(),
                    transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
            return tecServiceFeeConfigComputeResult;
        }

        // 获取抽佣路线配置
        List<TecServiceFeeV2RouteConfigDO> routeConfigDOS = tecServiceFeeV2RouteConfigService.getRouteConfigs(tecServiceFeeConfig.getId(), transportMain.getStartCity(), transportMain.getDestCity());
        if (CollectionUtils.isEmpty(routeConfigDOS)) {
            log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 未获取到路线配置列表. 结果：{}", transportMain.getUserId(),
                    transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
            return tecServiceFeeConfigComputeResult;
        }


        TecServiceFeeV2RouteConfigDO specificRoute = getRouteConfigByType(routeConfigDOS, RouteTypeEnum.SPECIFIC_ROUTE);
        TecServiceFeeV2RouteConfigDO universalRoute = getRouteConfigByType(routeConfigDOS, RouteTypeEnum.UNIVERSAL);
        if (Objects.isNull(specificRoute)) {
            if (Objects.isNull(universalRoute)) {
                log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 未获取到路线配置1. 结果：{}", transportMain.getUserId(),
                        transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
                return tecServiceFeeConfigComputeResult;
            }
            computeByRouteConfig(tecServiceFeeConfigComputeResult, universalRoute, priceString, transportMain, defaultTecServiceFee, distance, oldMain, matchConditionEarliestTime, freeTec);
        } else {
            boolean specificRouteComputeSuccess = computeByRouteConfig(tecServiceFeeConfigComputeResult, specificRoute, priceString, transportMain, defaultTecServiceFee, distance, oldMain, matchConditionEarliestTime, freeTec);
            if (!specificRouteComputeSuccess) {
                if (Objects.isNull(universalRoute)) {
                    log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 未获取到路线配置2. 结果：{}", transportMain.getUserId(),
                            transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
                    return tecServiceFeeConfigComputeResult;
                }
                computeByRouteConfig(tecServiceFeeConfigComputeResult, universalRoute, priceString, transportMain, defaultTecServiceFee, distance, oldMain, matchConditionEarliestTime, freeTec);
            }
        }

        log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 最终计算结果：{}", transportMain.getUserId(),
                transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
        return tecServiceFeeConfigComputeResult;
    }

    /**
     * 根据路线配置计算技术服务费
     *
     * @param tecServiceFeeConfigComputeResult
     * @param route
     * @param priceString
     * @param transportMain
     * @param defaultTecServiceFee
     * @param distance
     * @param oldMain
     * @param matchConditionEarliestTime
     * @param freeTec
     * @return
     */
    private boolean computeByRouteConfig(TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult,
                                                                  TecServiceFeeV2RouteConfigDO route,
                                                                  String priceString,
                                                                  TransportMainDO transportMain,
                                                                  Integer defaultTecServiceFee,
                                                                  BigDecimal distance,
                                                                  TransportMainDO oldMain,
                                                                  Date matchConditionEarliestTime,
                                                                  boolean freeTec) {
        boolean computeSuccess = false;
        tecServiceFeeConfigComputeResult.setRouteType(route.getRouteType());
        tecServiceFeeConfigComputeResult.setStartCity(route.getStartCity());
        tecServiceFeeConfigComputeResult.setDestCity(route.getDestCity());

        List<TecServiceFeeV2StageConfigDO> stageConfigs = tecServiceFeeV2StageConfigService.getStageConfigs(route.getId());
        if (CollectionUtils.isEmpty(stageConfigs) || StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) <= 0) {
            log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{},未获取到阶梯配置. 结果：{}", transportMain.getUserId(),
                    transportMain.getTaskContent(), route.getId(), route.getRouteType(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
            return computeSuccess;
        }

        BigDecimal tecServiceFee = new BigDecimal(defaultTecServiceFee);
        BigDecimal tecServiceFeeBeforeDiscount = new BigDecimal(defaultTecServiceFee);
        BigDecimal price = new BigDecimal(priceString);

        // 抽佣金额计算抹零方式 0：对10向下取整；1:向下取整
        Integer tecServiceFeeRoundingType = tytConfigRemoteService.getIntValue("tec_service_fee_rounding_type", 0);
        for (TecServiceFeeV2StageConfigDO stageConfig : stageConfigs) {
            log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{}, distance:{}, 阶梯配置：{}", transportMain.getUserId(),
                    transportMain.getTaskContent(), route.getId(), route.getRouteType(), distance, JSONObject.toJSONString(stageConfig));
            if (Objects.nonNull(stageConfig.getDistanceMin()) && Objects.nonNull(stageConfig.getDistanceMax())
                    && distance.compareTo(stageConfig.getDistanceMin()) > 0 && distance.compareTo(stageConfig.getDistanceMax()) <= 0) {

                List<TecServiceFeeV2ProportionConfigDO> proportionConfigs = tecServiceFeeV2ProportionConfigService.getByStageId(stageConfig.getId());
                int transportProportionNum = ThreadLocalRandom.current().nextInt(100, 200);
                if (oldMain != null && oldMain.getSrcMsgId() != null) {
                    String transportNumString = redisUtil.getString("commissionProportionTransportNum" + ":" + oldMain.getSrcMsgId());
                    if (StringUtils.isNotBlank(transportNumString) && StringUtils.isNumeric(transportNumString.trim())) {
                        transportProportionNum = Integer.parseInt(transportNumString);
                    }
                }

                tecServiceFeeConfigComputeResult.setDistanceMin(stageConfig.getDistanceMin());
                tecServiceFeeConfigComputeResult.setDistanceMax(stageConfig.getDistanceMax());
                tecServiceFeeConfigComputeResult.setTransportProportionNum(transportProportionNum);

                TecServiceFeeV2ProportionConfigDO serviceFeeProportionConfig = null;
                BigDecimal tecServiceFeeRate = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(proportionConfigs)) {
                    serviceFeeProportionConfig = proportionConfigs.get(transportProportionNum % proportionConfigs.size());
                    if (serviceFeeProportionConfig.getTecServiceFeeRate() != null && serviceFeeProportionConfig.getTecServiceFeeRate().compareTo(BigDecimal.ZERO) > 0) {
                        tecServiceFeeRate = serviceFeeProportionConfig.getTecServiceFeeRate();
                    }
                }

                // 在配置的运费高低区间内，左开右闭
                BigDecimal tecServiceFeeSnap = price.multiply(tecServiceFeeRate.movePointLeft(2)).setScale(2, RoundingMode.DOWN);
                if (stageConfig.getTecServiceFeeMin() != null && stageConfig.getTecServiceFeeMax() != null) {
                    if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMin()) < 0) {
                        tecServiceFeeSnap = stageConfig.getTecServiceFeeMin();
                    } else if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMax()) > 0) {
                        tecServiceFeeSnap = stageConfig.getTecServiceFeeMax();
                    }
                }
                log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{}, 折前技术服务费：{}", transportMain.getUserId(),
                        transportMain.getTaskContent(), route.getId(), route.getRouteType(), tecServiceFeeSnap);

                // 算最终折扣前抹零，并记录折前价格
                if (tecServiceFeeRoundingType == 0) {
                    tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                } else {
                    tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                }
                BigDecimal tecServiceFeeBeforeDiscountSnap = tecServiceFeeSnap;

                TecServiceFeeV2DiscountConfigDO discountConfig = null;
                // 最终打折节点，记录折前价
                if (matchConditionEarliestTime != null && serviceFeeProportionConfig != null) {
                    // 取折扣用新方法
                    long matchConditionEarliestTimeBetweenNowMin = Duration.between(matchConditionEarliestTime.toInstant(), new Date().toInstant()).toMinutes();
                    discountConfig = tecServiceFeeV2DiscountConfigService.getByProportionIdAndDiscountTimeMin(serviceFeeProportionConfig.getId(), matchConditionEarliestTimeBetweenNowMin);
                    log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{}, 命中折扣配置：{}", transportMain.getUserId(),
                            transportMain.getTaskContent(), route.getId(), route.getRouteType(), JSONObject.toJSONString(discountConfig));
                    if (discountConfig != null && discountConfig.getDiscount() != null) {
                        tecServiceFeeSnap = tecServiceFeeSnap.multiply(discountConfig.getDiscount().movePointLeft(1)).setScale(2, RoundingMode.DOWN);
                        if (discountConfig.getDiscount().compareTo(BigDecimal.ZERO) == 0) {
                            if (tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList() != null) {
                                tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList().add(FreeTecTypeEnum.OVER_TIME_FREE.getCode());
                            } else {
                                List<Integer> needFreeTecTypeList = new ArrayList<>();
                                needFreeTecTypeList.add(FreeTecTypeEnum.OVER_TIME_FREE.getCode());
                                tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                            }
                            freeTec = true;
                        }
                    }
                }
                log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{}, 折后技术服务费：{}", transportMain.getUserId(),
                        transportMain.getTaskContent(), route.getId(), route.getRouteType(), tecServiceFeeSnap);

                // 订金不可退时，折后技术服务费<=订金，使用折后技术服务费，否则使用订金
                if (Objects.equals(transportMain.getRefundFlag(), RefundFlagEnum.NO_RETURN.getCode()) && tecServiceFeeSnap.compareTo(transportMain.getInfoFee()) > 0) {
                    tecServiceFeeSnap = transportMain.getInfoFee();
                    log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{}, 折后技术服务费大于订金，使用定金：{}", transportMain.getUserId(),
                            transportMain.getTaskContent(), route.getId(), route.getRouteType(), tecServiceFeeSnap);
                }
                //算完最终折后价后再次抹零
                if (tecServiceFeeRoundingType == 0) {
                    tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                } else {
                    tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                }

                tecServiceFee = tecServiceFeeSnap;
                tecServiceFeeBeforeDiscount = tecServiceFeeBeforeDiscountSnap;

                tecServiceFeeConfigComputeResult.setTecServiceFeeMax(stageConfig.getTecServiceFeeMax());
                tecServiceFeeConfigComputeResult.setTecServiceFeeMin(stageConfig.getTecServiceFeeMin());
                tecServiceFeeConfigComputeResult.setTecServiceFeeRate(tecServiceFeeRate);
                if (discountConfig != null) {
                    tecServiceFeeConfigComputeResult.setDiscountTime(discountConfig.getDiscountTime());
                    tecServiceFeeConfigComputeResult.setDiscount(discountConfig.getDiscount());
                }

                if (serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                    List<TecServiceFeeV2DiscountConfigDO> discountConfigs = tecServiceFeeV2DiscountConfigService.getByProportionId(serviceFeeProportionConfig.getId());
                    if (CollectionUtils.isNotEmpty(discountConfigs)) {
                        tecServiceFeeConfigComputeResult.setDiscountConfig(JSON.toJSONString(discountConfigs));

                        // 构造BI要的整体折扣信息例如：60,8.0,120,5.0,1440,0.0
                        StringBuilder allDiscount = new StringBuilder();
                        for (TecServiceFeeV2DiscountConfigDO tecServiceFeeDiscountConfig : discountConfigs) {
                            allDiscount.append(tecServiceFeeDiscountConfig.getDiscountTime()).append(",").append(tecServiceFeeDiscountConfig.getDiscount()).append(",");
                        }
                        if (!allDiscount.isEmpty()) {
                            allDiscount.deleteCharAt(allDiscount.length() - 1);
                        }
                        tecServiceFeeConfigComputeResult.setAllDiscount(allDiscount.toString());
                    }
                }
                computeSuccess = true;
            }
        }
        if (freeTec) {
            log.info("抽佣金额计算v2- userId：{}, taskContent:{}, routeId:{}, routeType:{}, 命中免佣规则，设置技术服务费为0", transportMain.getUserId(),
                    transportMain.getTaskContent(), route.getId(), route.getRouteType());
            // 超时免佣
            tecServiceFee = new BigDecimal(0);
        }
        tecServiceFeeConfigComputeResult.setTecServiceFee(tecServiceFee);
        tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(tecServiceFeeBeforeDiscount);

        return computeSuccess;
    }

    /**
     * 获取最终使用的路线配置
     *
     * @param routeConfigDOS
     * @return
     */
    @Nullable
    private TecServiceFeeV2RouteConfigDO getRouteConfigByType(List<TecServiceFeeV2RouteConfigDO> routeConfigDOS, RouteTypeEnum routeTypeEnum) {
        TecServiceFeeV2RouteConfigDO route = routeConfigDOS.stream()
                .filter(v -> Objects.equals(v.getRouteType(), routeTypeEnum.getCode()))
                .findFirst()
                .orElse(null);
        return route;
    }

    /**
     * 是否符合免佣方式2，超时直接免佣
     *
     * @param matchConditionEarliestTime
     * @param tecServiceFeeConfigComputeResult
     * @param firstPublishTimeMinute
     * @return
     */
    private boolean isOverTimeToFree(Date matchConditionEarliestTime, TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult, Long firstPublishTimeMinute) {
        boolean overTimeToFree = false;
        if (matchConditionEarliestTime == null) {
            overTimeToFree = checkIsNeedFreeTecServiceFeeByTime(tecServiceFeeConfigComputeResult, firstPublishTimeMinute);
            if (overTimeToFree) {
                List<Integer> needFreeTecTypeList = new ArrayList<>();
                needFreeTecTypeList.add(FreeTecTypeEnum.OVER_TIME_FREE.getCode());
                tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(needFreeTecTypeList);
            }
        }
        return overTimeToFree;
    }

    /**
     * 获取满足抽佣配置条件一的时间
     *
     * @param oldMain
     * @param tecServiceFeeConfigComputeResult
     * @return
     */
    @Nullable
    private Date getMatchConditionEarliestTime(TransportMainDO oldMain, TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult) {
        Date matchConditionEarliestTime = null;
        if (oldMain != null && oldMain.getSrcMsgId() != null) {
            matchConditionEarliestTime = getMatchConditionLastTime(oldMain, tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount(), tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount());
        } else {
            if ((tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount() == 0)
                    || (tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount() == 0)) {
                //当日新发货源并且查看拨打配置有0的，则满足条件一的时间为当前发货时间
                matchConditionEarliestTime = new Date();
            }
        }
        return matchConditionEarliestTime;
    }

    /**
     * 获取货源首发时间间隔
     *
     * @param oldMain
     * @return
     */
    @Nullable
    private static Long getFirstPublishTimeMinute(TransportMainDO oldMain) {
        Long firstPublishTimeMinute = null;
        if (oldMain != null && oldMain.getId() != null && oldMain.getCtime() != null) {
            // 判断oldTransportMain.getCtime()是不是今天
            LocalDate ctimeLocalDate = oldMain.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now()) && oldMain.getStatus() != null && oldMain.getStatus() != 4) {
                firstPublishTimeMinute = Duration.between(oldMain.getCtime().toInstant(), new Date().toInstant()).toMinutes();
            } else {
                //不是当天，则时间差为0
                firstPublishTimeMinute = 0L;
            }
        }
        return firstPublishTimeMinute;
    }

    /**
     * 默认技术服务费计算结果
     *
     * @param isExcellentGoodsTwo
     * @param defaultTecServiceFee
     * @param commissionScore
     * @param firstPublishTimeMinute
     * @param transportMain
     * @return
     */
    @NotNull
    private TecServiceFeeConfigComputeResult getDefaultTecServiceFeeConfigComputeResult(boolean isExcellentGoodsTwo,
                                                                                        Integer defaultTecServiceFee,
                                                                                        BigDecimal commissionScore,
                                                                                        Long firstPublishTimeMinute,
                                                                                        TransportMainDO transportMain,
                                                                                        TransportMainExtendDO mainExtend) {
        TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = new TecServiceFeeConfigComputeResult();
        tecServiceFeeConfigComputeResult.setCarMemberType(0);
        tecServiceFeeConfigComputeResult.setPrivacyPhoneType(1);
        tecServiceFeeConfigComputeResult.setFreeTecServiceFeeType(1);
        tecServiceFeeConfigComputeResult.setFreeTecServiceFeeTime(60);
        tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
        tecServiceFeeConfigComputeResult.setUseCommissionScoreStageConfig(true);
        tecServiceFeeConfigComputeResult.setCommissionScore(commissionScore);
        tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(new BigDecimal(defaultTecServiceFee));

        if (tecServiceFeeConfigComputeResult.getFreeTecServiceFeeType() != null &&
                tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime() != null &&
                firstPublishTimeMinute != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeType() == 1 &&
                firstPublishTimeMinute > tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime()) {
            tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(0));
            List<Integer> needFreeTecTypeList = new ArrayList<>();
            needFreeTecTypeList.add(FreeTecTypeEnum.OVER_TIME_FREE.getCode());
            tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(needFreeTecTypeList);
        }

        boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(transportMain, mainExtend, isExcellentGoodsTwo, tecServiceFeeConfigComputeResult);
        if (matchConditionToFree) {
            log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 未获取到技术服务费配置。命中免佣条件", transportMain.getUserId(),
                    transportMain.getTaskContent());
            tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(0));
        }
        log.info("抽佣金额计算v2- userId：{}, taskContent:{}, 未获取到技术服务费配置。默认结果：{}", transportMain.getUserId(),
                transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfigComputeResult));
        return tecServiceFeeConfigComputeResult;
    }

    /**
     * 获取抽佣技术服务费配置
     *
     * @param mainExtend
     * @param transportMain
     * @return
     */
    private TecServiceFeeV2ConfigDO getTecServiceFeeV2ConfigDO(TransportMainExtendDO mainExtend, TransportMainDO transportMain) {
        TecServiceFeeV2ConfigDO tecServiceFeeV2Config = new TecServiceFeeV2ConfigDO();
        // 好货标签
        if (Objects.isNull(mainExtend.getGoodTransportLabel())) {
            tecServiceFeeV2Config.setGoodTransportLabel(0);
        } else {
            tecServiceFeeV2Config.setGoodTransportLabel(mainExtend.getGoodTransportLabel());
        }
        // 适用货源
        Integer applyTransportType = getApplyTransportType(transportMain);
        tecServiceFeeV2Config.setApplyTransportType(applyTransportType);
        // 订金模式
        RefundFlagTypeEnum refundFlagTypeEnum = Objects.equals(RefundFlagEnum.NO_RETURN.getCode(), transportMain.getRefundFlag()) ? RefundFlagTypeEnum.NOT_GIVE_BACK : RefundFlagTypeEnum.GIVE_BACK;
        tecServiceFeeV2Config.setRefundFlagType(refundFlagTypeEnum.getCode());
        // 价格模式
        Integer pricePublishType = getPricePublishType(transportMain);
        tecServiceFeeV2Config.setPricePublishType(pricePublishType);
        log.info("抽佣金额计算v2- userId：{}, taskContent:{},本次发货计算抽佣金额时查询抽佣配置参数：{}", transportMain.getUserId(),
                transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeV2Config));
        TecServiceFeeV2ConfigDO tecServiceFeeConfig = tecServiceFeeV2ConfigService.getConfigs(tecServiceFeeV2Config);
        log.info("抽佣金额计算v2- userId：{}, taskContent:{},本次发货计算抽佣金额时匹配到的抽佣配置为：{}", transportMain.getUserId(),
                transportMain.getTaskContent(), JSONObject.toJSONString(tecServiceFeeConfig));
        return tecServiceFeeConfig;
    }

    /**
     * 获取是否是抽佣货源
     *
     * @param transportMain
     * @param mainExtend
     * @param commissionScore
     * @return
     */
    private boolean getIsCommissionTransport(TransportMainDO transportMain, TransportMainExtendDO mainExtend,
                                             BasePublishProcessBO basePublishProcessBO, BigDecimal commissionScore) {
        boolean isCommissionTransport = false;
        BigDecimal checkScore;
        if (StringUtils.isNotBlank(transportMain.getPrice()) && new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            checkScore = commissionScore;
        } else {
            checkScore = mainExtend.getGoodModelScore();
        }
        log.info("抽佣金额计算v2-根据分数判断落在哪个阶梯：{}", checkScore);

        DrawCommissionReq drawCommissionReq = new DrawCommissionReq();
        drawCommissionReq.setSourceType(transportMain.getSourceType());
        drawCommissionReq.setExcellentGoods(transportMain.getExcellentGoods());
        drawCommissionReq.setGoodCarPriceTransport(ExcellentGoodsTwoEnum.YES.getCode().equals(transportMain.getExcellentGoodsTwo()) ? 1 : 0);
        drawCommissionReq.setPublishType(transportMain.getPublishType());
        drawCommissionReq.setHavePrice(StringUtils.isNotBlank(transportMain.getPrice()) && !transportMain.getPrice().equals("0") ? 1 : 0);
        drawCommissionReq.setRefundFlag(transportMain.getRefundFlag());
        drawCommissionReq.setCommissionTime(new Date());
        drawCommissionReq.setUserId(transportMain.getUserId());
        drawCommissionReq.setInvoiceTransport(transportMain.getInvoiceTransport());
        drawCommissionReq.setGoodsModelScore(checkScore);
        drawCommissionReq.setGoodTransportTabel(mainExtend.getGoodTransportLabel() == null ? 0 : mainExtend.getGoodTransportLabel());

        CommissionTypeVO commissionTypeVO = this.checkCommission(drawCommissionReq);
        if (commissionTypeVO != null && commissionTypeVO.getDrawCommission() != null) {
            basePublishProcessBO.setMeetCommissionRules(commissionTypeVO.getMeetCommissionRules());
            if (commissionTypeVO.getDrawCommission() == 1) {
                isCommissionTransport = true;
            }
        }
        return isCommissionTransport;
    }

    /**
     * 获取价格模式
     *
     * @param transportMain
     * @return
     */
    private Integer getPricePublishType(TransportMainDO transportMain) {
        if (Objects.equals(transportMain.getPublishType(), PublishTypeEnum.TELE.getCode())) {
            if (TransportUtil.hasPrice(transportMain.getPrice())) {
                //电议有价
                return PricePublishTypeEnum.HAVE_PRICE_TEL.getCode();
            } else {
                //电议无价
                return PricePublishTypeEnum.NO_PRICE_TEL.getCode();
            }
        }
        //一口价
        return PricePublishTypeEnum.FIX_PRICE.getCode();
    }

    /**
     * 判断抽佣适用货源
     *
     * @param transportMain
     * @return
     */
    private Integer getApplyTransportType(TransportMainDO transportMain) {
        // 判断货源是否为专车
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(transportMain.getExcellentGoods())) {
            if (transportMain.getCargoOwnerId() == null || transportMain.getCargoOwnerId() == 1 || transportMain.getCargoOwnerId() == 0) {
                //专车签约合作商为平台
                return ApplyTransportTypeEnum.SPECIAL_PLAT.getCode();
            } else {
                return ApplyTransportTypeEnum.SPECIAL_NON_PLAT.getCode();
            }
        }

        // 判断货源是否为YMM
        if (Objects.equals(SourceTypeEnum.YMM.getCode(), transportMain.getSourceType())) {
            return ApplyTransportTypeEnum.YMM.getCode();
        }

        // 是否为优车2.0
        if (ExcellentGoodsTwoEnum.YES.getCode().equals(transportMain.getExcellentGoodsTwo())) {
            return ApplyTransportTypeEnum.EXCELLENT_2.getCode();
        }

        //判断货源是否为优车1.0
        if (ExcellentGoodsEnums.EXCELLENT.getCode().equals(transportMain.getExcellentGoods())) {
            return ApplyTransportTypeEnum.EXCELLENT_1.getCode();
        }

        // 普货
        return ApplyTransportTypeEnum.NORMAL.getCode();
    }

    public TecServiceFeeConfigComputeResult computeTecServiceFeeBtTransportData(BasePublishProcessBO basePublishProcessBO, boolean isExcellentGoodsTwo) {
        TransportMainDO transportMain = basePublishProcessBO.getTransportMain();
        TransportMainDO oldMain = basePublishProcessBO.getOldMain();
        TransportMainExtendDO mainExtend = basePublishProcessBO.getMainExtend();

        //调用BI接口判断该货源是否抽佣，如果抽佣则通过配置计算技术服务费，并给该货源打上抽佣货源标识
        boolean isCommissionTransport = false;
        if (transportMain.getRefundFlag() == null) {
            return null;
        }

        Long firstPublishTimeMinute = null;
        if (oldMain != null && oldMain.getId() != null && oldMain.getCtime() != null) {
            //判断oldTransportMain.getCtime()是不是今天
            LocalDate ctimeLocalDate = oldMain.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now()) && oldMain.getStatus() != null && oldMain.getStatus() != 4) {
                firstPublishTimeMinute = Duration.between(oldMain.getCtime().toInstant(), new Date().toInstant()).toMinutes();
            } else {
                //不是当天，则时间差为0
                firstPublishTimeMinute = 0L;
            }
        }

        TecServiceFeeConfigDO tytTecServiceFeeConfig = new TecServiceFeeConfigDO();

        //判断货源是否为专车
        boolean isSpecialCar = ExcellentGoodsEnums.SPECIAL.getCode().equals(transportMain.getExcellentGoods());

        //判断货源是否为YMM
        boolean isYMM = transportMain.getSourceType().equals(SourceTypeEnum.YMM.getCode());

        //判断货源是否为优车1.0
        boolean isExcellentGoods = ExcellentGoodsEnums.EXCELLENT.getCode().equals(transportMain.getExcellentGoods());

        if (isSpecialCar) {
            tytTecServiceFeeConfig.setApplyTransportType(1);
            if (transportMain.getCargoOwnerId() == null || transportMain.getCargoOwnerId() == 1 || transportMain.getCargoOwnerId() == 0) {
                //专车签约合作商为平台
                tytTecServiceFeeConfig.setSpecialCarCooperativeType(1);
            } else {
                tytTecServiceFeeConfig.setSpecialCarCooperativeType(2);
            }
        } else if (isYMM) {
            tytTecServiceFeeConfig.setApplyTransportType(5);
        } else if (ExcellentGoodsTwoEnum.YES.getCode().equals(transportMain.getExcellentGoodsTwo())) {
            tytTecServiceFeeConfig.setApplyTransportType(4);
        } else if (isExcellentGoods) {
            tytTecServiceFeeConfig.setApplyTransportType(3);
        } else {
            tytTecServiceFeeConfig.setApplyTransportType(2);
        }

        tytTecServiceFeeConfig.setRefundFlagType(transportMain.getRefundFlag() == 0 ? 2 : 1);
        if (transportMain.getPublishType() == PublishTypeEnum.TELE.getCode().shortValue()) {
            if (TransportUtil.hasPrice(transportMain.getPrice())) {
                //电议有价
                tytTecServiceFeeConfig.setPricePublishType(1);
            } else {
                //电议无价
                tytTecServiceFeeConfig.setPricePublishType(2);
            }
        } else {
            //一口价
            tytTecServiceFeeConfig.setPricePublishType(3);
        }

        //调用BI接口获取抽佣分数
        BigDecimal commissionScore = mainExtend.getCommissionScore();
        if (commissionScore == null || commissionScore.compareTo(BigDecimal.ZERO) < 0) {
            commissionScore = new BigDecimal("-1");
        }

        BigDecimal checkScore;
        if (StringUtils.isNotBlank(transportMain.getPrice()) && new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            checkScore = commissionScore;
        } else {
            checkScore = mainExtend.getGoodModelScore();
        }
        log.info("抽佣金额计算-根据分数判断落在哪个阶梯：{}", checkScore);

        DrawCommissionReq drawCommissionReq = new DrawCommissionReq();
        drawCommissionReq.setSourceType(transportMain.getSourceType());
        drawCommissionReq.setExcellentGoods(transportMain.getExcellentGoods());
        drawCommissionReq.setGoodCarPriceTransport(ExcellentGoodsTwoEnum.YES.getCode().equals(transportMain.getExcellentGoodsTwo()) ? 1 : 0);
        drawCommissionReq.setPublishType(transportMain.getPublishType());
        drawCommissionReq.setHavePrice(StringUtils.isNotBlank(transportMain.getPrice()) && !transportMain.getPrice().equals("0") ? 1 : 0);
        drawCommissionReq.setRefundFlag(transportMain.getRefundFlag());
        drawCommissionReq.setCommissionTime(new Date());
        drawCommissionReq.setUserId(transportMain.getUserId());
        drawCommissionReq.setInvoiceTransport(transportMain.getInvoiceTransport());
        drawCommissionReq.setGoodsModelScore(checkScore);
        drawCommissionReq.setGoodTransportTabel(mainExtend.getGoodTransportLabel() == null ? 0 : mainExtend.getGoodTransportLabel());

        CommissionTypeVO commissionTypeVO = this.checkCommission(drawCommissionReq);
        if (commissionTypeVO != null && commissionTypeVO.getDrawCommission() != null) {
            basePublishProcessBO.setMeetCommissionRules(commissionTypeVO.getMeetCommissionRules());
            if (commissionTypeVO.getDrawCommission() == 1) {
                isCommissionTransport = true;
            }
        }

        if (!isCommissionTransport) {
            return null;
        }

        //无价货源优车指导价
        String goodCarPriceTransportCarryPrice = null;

        //默认技术服务费
        Integer defaultTecServiceFee = tytConfigRemoteService.getIntValue("default_tec_service_fee", 50);
        String priceString = transportMain.getPrice();
        if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
            //使用优车指导价
            priceString = makePriceByGoodCarPriceTransportCarryPrice(transportMain);
            if (StringUtils.isBlank(priceString) || new BigDecimal(priceString).compareTo(BigDecimal.ZERO) == 0) {
                //用户没有手填运费并且也没有通过BI接口获取到建议运费
                defaultTecServiceFee = tytConfigRemoteService.getIntValue("default_tec_service_fee_no_price", 20);
            } else {
                goodCarPriceTransportCarryPrice = priceString;
            }
        }
        log.info("抽佣金额计算-本次发货计算抽佣金额时用的运费为：{}", priceString);

        //抽佣金额计算抹零方式 0：对10向下取整；1:向下取整
        Integer tecServiceFeeRoundingType = tytConfigRemoteService.getIntValue("tec_service_fee_rounding_type", 0);

        TecServiceFeeConfigDO tecServiceFeeConfig = tecServiceFeeConfigService.getConfigs(tytTecServiceFeeConfig);
        if (tecServiceFeeConfig != null) {
            TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = new TecServiceFeeConfigComputeResult();
            BeanUtils.copyProperties(tecServiceFeeConfig, tecServiceFeeConfigComputeResult);
            tecServiceFeeConfigComputeResult.setUseCommissionScoreStageConfig(true);
            tecServiceFeeConfigComputeResult.setCommissionScore(commissionScore);
            tecServiceFeeConfigComputeResult.setGoodCarPriceTransportCarryPrice(goodCarPriceTransportCarryPrice);

            //满足条件一的时间，如果不满足则为null
            Date matchConditionEarliestTime = null;
            if (oldMain != null && oldMain.getSrcMsgId() != null) {
                matchConditionEarliestTime = getMatchConditionLastTime(oldMain
                        , tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount(), tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount());
            } else {
                if ((tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeViewCount() == 0)
                        || (tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeCallCount() == 0)) {
                    //当日新发货源并且查看拨打配置有0的，则满足条件一的时间为当前发货时间
                    matchConditionEarliestTime = new Date();
                }
            }

            //未满足条件一的情况下是否符合直接免佣条件
            boolean overTimeToFree = false;
            if (matchConditionEarliestTime == null) {
                overTimeToFree = checkIsNeedFreeTecServiceFeeByTime(tecServiceFeeConfigComputeResult, firstPublishTimeMinute);
                if (overTimeToFree) {
                    List<Integer> needFreeTecTypeList = new ArrayList<>();
                    needFreeTecTypeList.add(5);
                    tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                }
            }

            boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(transportMain, mainExtend, isExcellentGoodsTwo, tecServiceFeeConfigComputeResult);
            boolean freeTec = overTimeToFree || matchConditionToFree;

            //根据是否使用抽佣分数获取不同类型的分段配置
            List<TecServiceFeeStageConfigDO> stageConfigs = tecServiceFeeStageConfigService.getConfigs(tecServiceFeeConfig.getId(), 2);

            if (CollectionUtils.isNotEmpty(stageConfigs) && StringUtils.isNotBlank(priceString) && new BigDecimal(priceString).compareTo(BigDecimal.ZERO) > 0 && checkScore != null) {
                BigDecimal tecServiceFee = new BigDecimal(defaultTecServiceFee);
                BigDecimal tecServiceFeeBeforeDiscount = new BigDecimal(defaultTecServiceFee);
                BigDecimal price = new BigDecimal(priceString);
                for (TecServiceFeeStageConfigDO stageConfig : stageConfigs) {
                    if (stageConfig.getPriceMin() != null && stageConfig.getPriceMax() != null
                            && checkScore.compareTo(stageConfig.getPriceMin()) > 0 && checkScore.compareTo(stageConfig.getPriceMax()) <= 0) {

                        //取百分比用新方法
                        List<TecServiceFeeProportionConfigDO> proportionConfig = tecServiceFeeProportionConfigService.getByStageId(stageConfig.getId());
                        int transportProportionNum = ThreadLocalRandom.current().nextInt(100, 200);
                        if (oldMain != null && oldMain.getSrcMsgId() != null) {
                            String transportNumString = redisUtil.getString("commissionProportionTransportNum" + ":" + oldMain.getSrcMsgId());
                            if (StringUtils.isNotBlank(transportNumString) && StringUtils.isNumeric(transportNumString.trim())) {
                                transportProportionNum = Integer.parseInt(transportNumString);
                            }
                        }
                        tecServiceFeeConfigComputeResult.setTransportProportionNum(transportProportionNum);
                        TecServiceFeeProportionConfigDO serviceFeeProportionConfig = null;
                        BigDecimal tecServiceFeeRate = BigDecimal.ZERO;
                        if (CollectionUtils.isNotEmpty(proportionConfig)) {
                            serviceFeeProportionConfig = proportionConfig.get(transportProportionNum % proportionConfig.size());
                            if (serviceFeeProportionConfig.getTecServiceFeeRate() != null && serviceFeeProportionConfig.getTecServiceFeeRate().compareTo(BigDecimal.ZERO) > 0) {
                                tecServiceFeeRate = serviceFeeProportionConfig.getTecServiceFeeRate();
                            }
                        }

                        //在配置的运费高低区间内，做开右闭
                        BigDecimal tecServiceFeeSnap = price.multiply(tecServiceFeeRate.movePointLeft(2)).setScale(2, RoundingMode.DOWN);


                        if (stageConfig.getTecServiceFeeMin() != null && stageConfig.getTecServiceFeeMax() != null) {
                            if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMin()) < 0) {
                                tecServiceFeeSnap = stageConfig.getTecServiceFeeMin();
                            } else if (tecServiceFeeSnap.compareTo(stageConfig.getTecServiceFeeMax()) > 0) {
                                tecServiceFeeSnap = stageConfig.getTecServiceFeeMax();
                            }
                        }

                        //算最终折扣前抹零，并记录折前价格
                        if (tecServiceFeeRoundingType == 0) {
                            tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                        } else {
                            tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                        }
                        BigDecimal tecServiceFeeAfterDiscountSnap = tecServiceFeeSnap;

                        TecServiceFeeDiscountConfigDO tytTecServiceFeeDiscountConfig = null;
                        //最终打折节点，记录折前价
                        if (matchConditionEarliestTime != null && serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                            tecServiceFeeAfterDiscountSnap = new BigDecimal(tecServiceFeeSnap.toString());

                            //取折扣用新方法
                            long matchConditionEarliestTimeBetweenNowMin = Duration.between(matchConditionEarliestTime.toInstant(), new Date().toInstant()).toMinutes();
                            tytTecServiceFeeDiscountConfig = tecServiceFeeDiscountConfigMapper.getByProportionIdAndDiscountTimeMin(serviceFeeProportionConfig.getId(), matchConditionEarliestTimeBetweenNowMin);
                            if (tytTecServiceFeeDiscountConfig != null && tytTecServiceFeeDiscountConfig.getDiscount() != null) {
                                tecServiceFeeSnap = tecServiceFeeSnap.multiply(tytTecServiceFeeDiscountConfig.getDiscount().movePointLeft(1)).setScale(2, RoundingMode.DOWN);
                                if (tytTecServiceFeeDiscountConfig.getDiscount().compareTo(BigDecimal.ZERO) == 0) {
                                    if (tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList() != null) {
                                        tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList().add(5);
                                    } else {
                                        List<Integer> needFreeTecTypeList = new ArrayList<>();
                                        needFreeTecTypeList.add(5);
                                        tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(needFreeTecTypeList);
                                    }
                                    freeTec = true;
                                }
                            }
                        }

                        //算完最终折后价后再次抹零
                        if (tecServiceFeeRoundingType == 0) {
                            tecServiceFeeSnap = tecServiceFeeSnap.divide(new BigDecimal(10), 0, RoundingMode.DOWN).multiply(new BigDecimal(10));
                        } else {
                            tecServiceFeeSnap = tecServiceFeeSnap.setScale(0, RoundingMode.DOWN);
                        }

                        tecServiceFee = tecServiceFeeSnap;
                        tecServiceFeeBeforeDiscount = tecServiceFeeAfterDiscountSnap;

                        tecServiceFeeConfigComputeResult.setPriceMax(stageConfig.getPriceMax());
                        tecServiceFeeConfigComputeResult.setPriceMin(stageConfig.getPriceMin());
                        tecServiceFeeConfigComputeResult.setTecServiceFeeMax(stageConfig.getTecServiceFeeMax());
                        tecServiceFeeConfigComputeResult.setTecServiceFeeMin(stageConfig.getTecServiceFeeMin());
                        tecServiceFeeConfigComputeResult.setTecServiceFeeRate(tecServiceFeeRate);
                        if (tytTecServiceFeeDiscountConfig != null) {
                            tecServiceFeeConfigComputeResult.setDiscountTime(tytTecServiceFeeDiscountConfig.getDiscountTime());
                            tecServiceFeeConfigComputeResult.setDiscount(tytTecServiceFeeDiscountConfig.getDiscount());
                        }

                        if (serviceFeeProportionConfig != null && serviceFeeProportionConfig.getId() != null) {
                            List<TecServiceFeeDiscountConfigDO> tytTecServiceFeeDiscountConfigByProportionId = tecServiceFeeDiscountConfigMapper.getByProportionId(serviceFeeProportionConfig.getId());
                            if (CollectionUtils.isNotEmpty(tytTecServiceFeeDiscountConfigByProportionId)) {
                                tecServiceFeeConfigComputeResult.setDiscountConfig(JSON.toJSONString(tytTecServiceFeeDiscountConfigByProportionId));

                                //构造BI要的整体折扣信息例如：60,8.0,120,5.0,1440,0.0
                                StringBuilder allDiscount = new StringBuilder();
                                for (TecServiceFeeDiscountConfigDO tecServiceFeeDiscountConfig : tytTecServiceFeeDiscountConfigByProportionId) {
                                    allDiscount.append(tecServiceFeeDiscountConfig.getDiscountTime()).append(",").append(tecServiceFeeDiscountConfig.getDiscount()).append(",");
                                }
                                if (allDiscount.length() > 0) {
                                    allDiscount.deleteCharAt(allDiscount.length() - 1);
                                }
                                tecServiceFeeConfigComputeResult.setAllDiscount(allDiscount.toString());
                            }
                        }
                    }
                }
                if (freeTec) {
                    //超时免佣
                    tecServiceFee = new BigDecimal(0);
                }
                tecServiceFeeConfigComputeResult.setTecServiceFee(tecServiceFee);
                tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(tecServiceFeeBeforeDiscount);
            } else {
                tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(new BigDecimal(defaultTecServiceFee));
                if (freeTec) {
                    //超时免佣
                    tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(0));
                } else {
                    tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
                }
            }
            return tecServiceFeeConfigComputeResult;
        } else {
            TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = new TecServiceFeeConfigComputeResult();
            tecServiceFeeConfigComputeResult.setCarMemberType(0);
            tecServiceFeeConfigComputeResult.setPrivacyPhoneType(1);
            tecServiceFeeConfigComputeResult.setFreeTecServiceFeeType(1);
            tecServiceFeeConfigComputeResult.setFreeTecServiceFeeTime(60);
            tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(defaultTecServiceFee));
            tecServiceFeeConfigComputeResult.setUseCommissionScoreStageConfig(true);
            tecServiceFeeConfigComputeResult.setCommissionScore(commissionScore);
            tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(new BigDecimal(defaultTecServiceFee));

            if (tecServiceFeeConfigComputeResult.getFreeTecServiceFeeType() != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime() != null && firstPublishTimeMinute != null && tecServiceFeeConfigComputeResult.getFreeTecServiceFeeType() == 1 && firstPublishTimeMinute > tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime()) {
                tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(0));
                List<Integer> needFreeTecTypeList = new ArrayList<>();
                needFreeTecTypeList.add(5);
                tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(needFreeTecTypeList);
            }

            boolean matchConditionToFree = checkIsNeedFreeTecServiceFeeByTransport(transportMain, mainExtend, isExcellentGoodsTwo, tecServiceFeeConfigComputeResult);
            if (matchConditionToFree) {
                tecServiceFeeConfigComputeResult.setTecServiceFee(new BigDecimal(0));
            }

            return tecServiceFeeConfigComputeResult;
        }
    }

    private boolean isGoodMediumTransport(Integer goodTransportLabel) {
        if (goodTransportLabel != null && ((goodTransportLabel >= 11 && goodTransportLabel <= 13) || (goodTransportLabel >= 21 && goodTransportLabel <= 23))) {
            return true;
        } else {
            return false;
        }
    }

    private Date getMatchConditionLastTime(TransportMainDO oldTransport, Integer freeTecServiceFeeViewCount, Integer freeTecServiceFeeCallCount) {
        if ((freeTecServiceFeeViewCount != null && freeTecServiceFeeViewCount == 0) || (freeTecServiceFeeCallCount != null && freeTecServiceFeeCallCount == 0)) {
            LocalDate ctimeLocalDate = oldTransport.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (ctimeLocalDate.equals(LocalDate.now())) {
                return oldTransport.getCtime();
            } else {
                return new Date();
            }
        }
        //今日货源重发，并且条件一没有配置0的
        Date matchConditionLastViewTime = null;
        Date matchConditionLastCallTime = null;

        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime tomorrowStart = todayStart.plusDays(1);
        Date todayStartDate = Date.from(todayStart.atZone(ZoneId.systemDefault()).toInstant());
        Date tomorrowStartDate = Date.from(tomorrowStart.atZone(ZoneId.systemDefault()).toInstant());

        if (freeTecServiceFeeViewCount != null) {
            //获取符合条件的最近一次查看时间
            matchConditionLastViewTime = transportViewLogMapper.getLastViewTimeOffset(oldTransport.getSrcMsgId(), freeTecServiceFeeViewCount - 1);
        }
        if (freeTecServiceFeeCallCount != null) {
            //获取符合条件的最近一次通话时间
            matchConditionLastCallTime = appCallLogMapper.getLastCallTimeOffset(oldTransport.getSrcMsgId(), freeTecServiceFeeCallCount - 1);
        }
        //取这两个时间最早的那个作为满足条件一的时间
        if (matchConditionLastViewTime == null && matchConditionLastCallTime == null) {
            return null;
        } else if (matchConditionLastViewTime == null) {
            return matchConditionLastCallTime;
        } else if (matchConditionLastCallTime == null) {
            return matchConditionLastViewTime;
        } else {
            return matchConditionLastViewTime.before(matchConditionLastCallTime) ? matchConditionLastViewTime : matchConditionLastCallTime;
        }
    }

    public CommissionTypeVO checkCommission(DrawCommissionReq req) {
        log.info("调用货源抽佣规则接口开始，req:{}", JSONUtil.toJsonStr(req));
        if (req == null || req.getSourceType() == null || req.getExcellentGoods() == null || req.getPublishType() == null || req.getHavePrice() == null || req.getRefundFlag() == null || req.getUserId() == null) {
            throw TytException.createException(ResponseEnum.request_error.info());
        }
        if (req.getCommissionTime() == null) {
            req.setCommissionTime(new Date());
        }
        // 运满满货源
        if (Objects.equals(req.getSourceType(), SourceTypeEnum.YMM.getCode())) {
            req.setCommissionSource(CommissionSourceEnum.YMM.getCode());
            // 优车定价货源
        } else if (Objects.equals(req.getGoodCarPriceTransport(), YesOrNoEnum.YES.getId())) {
            req.setCommissionSource(CommissionSourceEnum.EXCELLENT_PRICE.getCode());
            // 普货 +  优车
        } else if (Objects.equals(req.getExcellentGoods(), ExcellentGoodsEnums.EXCELLENT.getCode()) || Objects.equals(req.getExcellentGoods(), ExcellentGoodsEnums.NORMAL.getCode())) {
            req.setCommissionSource(CommissionSourceEnum.ORDINARY_EXCELLENT.getCode());
            // 专车货源
        } else if (Objects.equals(req.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {
            req.setCommissionSource(CommissionSourceEnum.SPECIAL.getCode());
        }

        CommissionTypeVO commissionTypeVO = new CommissionTypeVO();

        if (req.getInvoiceTransport() != null
                && req.getInvoiceTransport().equals(YesOrNoEnum.YES.getId())
                && !req.getCommissionSource().equals(CommissionSourceEnum.SPECIAL.getCode())) {
            log.info("调用货源抽佣规则,非专车的开票货源不进行抽取,req:{}", JSONUtil.toJsonStr(req));
            return commissionTypeVO;
        }

        List<Integer> commissionSourceCodeList = Arrays.stream(CommissionSourceEnum.values())
                .map(CommissionSourceEnum::getCode).toList();
        if (req.getCommissionSource() == null || !commissionSourceCodeList.contains(req.getCommissionSource())) {
            log.info("调用货源抽佣规则,货源类型不正确，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
            return commissionTypeVO;
        }

        //如果是不需要跳过抽佣打标的调度账号则不进行下面的跳过判断
        String noSkipCheckCommissionDispatchCompanyUserId = tytConfigRemoteService.getStringValue("no_skip_check_commission_dispatch_company_user_id", "");
        boolean notIsNoSkipCheckCommissionUser = Arrays.stream(noSkipCheckCommissionDispatchCompanyUserId.split(",")).noneMatch(userId -> String.valueOf(req.getUserId()).equals(userId));
        if (notIsNoSkipCheckCommissionUser) {
            int count = dispatchCompanyMapper.countByUserId(req.getUserId());

            // 如果是代调或个人货主且不是运满满，则不抽佣(代调账号在app发的货也不抽佣)
            if ((Objects.equals(req.getSourceType(), SourceTypeEnum.DISPATCH.getCode())
                    || Objects.equals(req.getSourceType(), SourceTypeEnum.OWNER.getCode()) || count > 0)
                    && !req.getCommissionSource().equals(CommissionSourceEnum.YMM.getCode())) {
                log.info("调用货源抽佣规则,代调或个人货主，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
                return commissionTypeVO;
            }
        } else {
            log.info("调用货源抽佣规则,调度用户不跳过抽佣打标,userId:{}", req.getUserId());
        }

        DrawCommissionRuleDO drawCommissionRule = drawCommissionRuleService.selectRule(req);

        // 满足规则条件，就打标可抽佣，是否抽佣根据下面判断确定
        if (drawCommissionRule != null) {
            log.info("调用货源抽佣规则,满足抽佣规则,id,{},req:{}", drawCommissionRule.getId(), JSONUtil.toJsonStr(req));
            commissionTypeVO.setMeetCommissionRules(CommissionResultEnum.COMMISSION_Y.getCode());

            String redisKeySuffix = drawCommissionRule.getCommissionSource() + "_" + drawCommissionRule.getPublishType()
                    + "_" + drawCommissionRule.getHavePrice() + "_" + drawCommissionRule.getRefundFlag() + "_" + drawCommissionRule.getGoodTransportLabel() + "_"
                    + cn.hutool.core.date.DateUtil.format(new Date(), PURE_DATE_PATTERN);

            String commissionCountKey = String.format("commission_count_" + redisKeySuffix);
            int commissionCount = ObjectUtils.defaultIfNull(redisUtil.getInt(commissionCountKey), 0);
            // 如果已经超过了抽佣条数已经超过了最大值，不再抽佣
            Integer commissionMaxCount = drawCommissionRule.getCommissionMaxCount();
            if (commissionMaxCount != null && commissionCount >= commissionMaxCount) {
                log.info("调用货源抽佣规则,超过最大抽佣条数，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
                return commissionTypeVO;
            }

            // 如果不在当天的抽佣时间段内，不进行抽取
            LocalTime dailyStart = drawCommissionRule.getDailyStart().toLocalTime();
            LocalTime dailyEnd = drawCommissionRule.getDailyEnd().toLocalTime();
            LocalTime commissionTime = new Time(req.getCommissionTime().getTime()).toLocalTime();
            if (commissionTime.isBefore(dailyStart) || commissionTime.isAfter(dailyEnd)) {
                log.info("调用货源抽佣规则,不在当天抽佣时间段内，不进行抽取,req:{}", JSONUtil.toJsonStr(req));
                return commissionTypeVO;
            }

            //判断该货源有没有ab测试,如果有，就隔一条取一次
            if (Objects.equals(drawCommissionRule.getRule(), YesOrNoEnum.YES.getId())) {
                String commissionFlagKey = String.format("commission_flag_" + redisKeySuffix);
                String commissionFlag = redisUtil.getString(commissionFlagKey);
                // 如果上一条没抽，那么这一条就要抽
                if (StringUtils.isBlank(commissionFlag) || Objects.equals(commissionFlag, YesOrNoEnum.NO.getId().toString())) {
                    commissionTypeVO.setDrawCommission(YesOrNoEnum.YES.getId());
                    log.info("调用货源抽佣规则 隔条抽 本次抽佣");
                    // 更改标识
                    redisUtil.set(commissionFlagKey, YesOrNoEnum.YES.getId(), Duration.ofDays(1));
                    // 更改抽取数量
                    redisUtil.set(commissionCountKey, String.valueOf(commissionCount + 1), Duration.ofDays(1));
                    log.info("调用货源抽佣规则 隔条抽 当前抽佣总次数:{}", commissionCount);
                } else {
                    // 更改标识
                    redisUtil.set(commissionFlagKey, YesOrNoEnum.NO.getId().toString(), Duration.ofDays(1));
                    log.info("调用货源抽佣规则 隔条抽 本次不抽佣");
                }
            } else {
                // 没有ab测试的话，符合条件的都抽
                commissionTypeVO.setDrawCommission(YesOrNoEnum.YES.getId());
                redisUtil.set(commissionCountKey, String.valueOf(commissionCount + 1), Duration.ofDays(1));
            }
        }
        log.info("调用货源抽佣规则接口结束，返回值:{}", JSONUtil.toJsonStr(commissionTypeVO));
        return commissionTypeVO;
    }

    public boolean checkIsNeedFreeTecServiceFeeByTime(TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult, Long firstPublishTimeMinute) {
        //新超时免佣配置存在,看看是不是旧货源重发
        if (tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime() != null && firstPublishTimeMinute != null) {
            if (tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime() <= 0) {
                return true;
            }
            return firstPublishTimeMinute > Long.valueOf(tecServiceFeeConfigComputeResult.getFreeTecServiceFeeTime());
        }
        return false;
    }

    public boolean checkIsNeedFreeTecServiceFeeByTransport(TransportMainDO tytTransport,
                                                           TransportMainExtendDO mainExtend,
                                                           boolean isGoodCarPriceTransport,
                                                           TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult) {
        // 专车非平台货源不走这些免佣逻辑
        if (tytTransport.getExcellentGoods() != null && tytTransport.getExcellentGoods() == 2 && tytTransport.getCargoOwnerId() != null && tytTransport.getCargoOwnerId() != 1) {
            log.info("判断是否符合发货直接免佣条件 专车非平台不走这些免佣条件");
            return false;
        }
        CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = transportMainService.checkIsNeedFreeTecServiceFeeByTransport(tytTransport.getUserId(),
                tytTransport.getStartCity(), isGoodCarPriceTransport, mainExtend.getGoodTransportLabel());
        if (checkIsNeedFreeTecServiceFeeVO != null && checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec() != null && checkIsNeedFreeTecServiceFeeVO.getNeedFreeTec()) {
            if (CollectionUtils.isNotEmpty(tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList())) {
                tecServiceFeeConfigComputeResult.getNeedFreeTecTypeList().addAll(checkIsNeedFreeTecServiceFeeVO.getNeedFreeTecTypeList());
            } else {
                tecServiceFeeConfigComputeResult.setNeedFreeTecTypeList(checkIsNeedFreeTecServiceFeeVO.getNeedFreeTecTypeList());
            }
            return true;
        }
        return false;
    }


    public String makePriceByGoodCarPriceTransportCarryPrice(TransportMainDO tytTransport) {
        TransportCarryReq transportCarryReq = this.buildCarryBeanByTransport(tytTransport);
        log.info("抽佣金额计算-发货根据货源信息获取优车定价建议价最低值请求参数：{}", JSON.toJSONString(transportCarryReq));
        CarryPriceVO carryPriceVo = thPriceRpcService.getThPrice(transportCarryReq);
        log.info("抽佣金额计算-发货根据货源信息获取优车定价建议价最低值结果：{}", JSON.toJSONString(carryPriceVo));
        if (carryPriceVo != null && carryPriceVo.getFixPriceMin() != null) {
            return carryPriceVo.getFixPriceMin().toString();
        }
        return null;
    }

    /**
     * 构建TransportCarryReq
     */
    private TransportCarryReq buildCarryBeanByTransport(TransportMainDO tytTransport) {
        TransportCarryReq transportCarryBean = new TransportCarryReq();
        transportCarryBean.setStartProvince(tytTransport.getStartProvinc());
        transportCarryBean.setStartCity(tytTransport.getStartCity());
        transportCarryBean.setStartArea(tytTransport.getStartArea());
        transportCarryBean.setDestProvince(tytTransport.getDestProvinc());
        transportCarryBean.setDestCity(tytTransport.getDestCity());
        transportCarryBean.setDestArea(tytTransport.getDestArea());
        transportCarryBean.setGoodsName(tytTransport.getTaskContent());
        transportCarryBean.setGoodsWeight(new BigDecimal(tytTransport.getWeight()));
        transportCarryBean.setGoodsLength(tytTransport.getLength());
        transportCarryBean.setGoodsWide(tytTransport.getWide());
        transportCarryBean.setGoodsHigh(tytTransport.getHigh());
        transportCarryBean.setExcellentGoods(tytTransport.getExcellentGoods());
        transportCarryBean.setUserId(tytTransport.getUserId());
        transportCarryBean.setDistance(tytTransport.getDistance() != null ? tytTransport.getDistance() : BigDecimal.ZERO);
        transportCarryBean.setGoodTypeName(tytTransport.getGoodTypeName());
        return transportCarryBean;
    }

}
