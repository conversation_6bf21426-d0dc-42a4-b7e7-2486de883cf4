package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 税票主体服务商
 *
 * <AUTHOR>
 * @since 2025/01/18 15:05
 */
@Getter
public enum InvoiceServiceProviderEnum {

    JCZY("JCZY", "自营"),
    HBWJ("HBWJ", "湖北我家"),
    AHST("AHST", "安徽神通"),
    XHL("XHL", "翔和翎");

    private String code;
    private String desc;

    InvoiceServiceProviderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (InvoiceServiceProviderEnum isp : InvoiceServiceProviderEnum.values()) {
            if (isp.getCode().equals(code)) {
                return isp.getDesc();
            }
        }
        return null;
    }

    public static boolean isCodeExist(String code) {
        for (InvoiceServiceProviderEnum isp : InvoiceServiceProviderEnum.values()) {
            if (isp.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
