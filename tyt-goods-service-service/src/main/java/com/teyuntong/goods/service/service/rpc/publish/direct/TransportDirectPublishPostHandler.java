package com.teyuntong.goods.service.service.rpc.publish.direct;

import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.checker.PublishPermissionChecker;
import com.teyuntong.goods.service.service.rpc.publish.post.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 后置处理器
 *
 * <AUTHOR>
 * @since 2025/02/18 13:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportDirectPublishPostHandler {

    private final BuildReturnDataPostHandler buildReturnDataPostHandler;
    private final CommissionTransportPostHandler commissionTransportPostHandler;
    private final AddTransportHistoryHandler saveTransportHistoryBackHandler;
    private final AddDispatchOwnerPostHandler addDispatchOwnerPostHandler;
    private final AddExposureRecordPostHandler addExposureRecordPostHandler;
    private final ExcellentPermissionPostHandler excellentPermissionPostHandler;
    private final InvoiceTransportSaveLogPostHandler invoiceTransportSaveLogPostHandler;
    private final BackendTransportPostHandler backendTransportPostHandler;
    private final SendPublishMQPostHandler sendPublishMQPostHandler;
    private final SpecialtAutoAssignPostHandler specialTransportAutoAssignPostHandler;
    private final AddExtraRefreshPostHandler addExtraRefreshPostHandler;
    private final AutoResendTransportPostHandler autoResendTransportPostHandler;
    private final BiTrickingPostHandler biTrickingPostHandler;
    private final PublishPermissionPostHandler publishPermissionPostHandler;
    private final TransportPublishLogPostHandler transportPublishLogPostHandler;
    private final SyncYmmPostHandler syncYmmPostHandler;
    private final RecordUseExposureCardPostHandler recordUseExposureCardPostHandler;
    private final ExposureCardGiveawayPostHandler exposureCardGiveawayPostHandler;
    private final PublishPermissionChecker publishPermissionChecker;
    private final AddPricePostHandler addPricePostHandler;
    private final DiagnosisTaskCountDownPostHandler diagnosisTaskCountDownPostHandler;
    private final SameTransportPushPostHandler sameTransportPushPostHandler;
    private final PriceChangeCachePostHandler priceChangeCachePostHandler;

    public void postHandler(DirectPublishProcessBO processBO) {
        // 构建返回数据
        buildReturnDataPostHandler.handler(processBO);
        // 更新抽佣记录
        commissionTransportPostHandler.handler(processBO, processBO.getDirectPublishBO().isRecalculateTecServiceFee());
        // 添加货源曝光记录
        addExposureRecordPostHandler.handler(processBO);
        // 保存历史记录，货源发布新老映射表
        saveTransportHistoryBackHandler.handler(processBO);
        // 添加个人货主
        addDispatchOwnerPostHandler.handler(processBO);
        // 优车权益扣减
        excellentPermissionPostHandler.handler(processBO);
        // 保存发票信息
        invoiceTransportSaveLogPostHandler.saveForDirectPublish(processBO);
        // 扣减发货次数
        publishPermissionChecker.checkDirectPublishPermission(processBO, true);
        // 更新用户发货次数(user已经将)
        // publishPermissionPostHandler.updateAuthPermissionUsed(processBO);
        // 小程序货源
        backendTransportPostHandler.handler(processBO);
        // 货源发布后发送mq
        sendPublishMQPostHandler.handler(processBO);
        // 专车货源自动派单
        specialTransportAutoAssignPostHandler.specialAssign(processBO.getTransportMain());
        // 增加货源额外刷新次数
        addExtraRefreshPostHandler.handler(processBO);
        // 自动重发货源
        autoResendTransportPostHandler.handler(processBO);
        // 直接发布BI数据埋点
        biTrickingPostHandler.directPublish(processBO);
        // 货源发布记录
        transportPublishLogPostHandler.handler(processBO);
        // 通知运满满
        syncYmmPostHandler.handler(processBO);
        // 记录使用曝光卡的时效
        recordUseExposureCardPostHandler.handler(processBO);
        // 校验是否符合曝光卡发放规则
        exposureCardGiveawayPostHandler.handler(processBO);
        // 加价后置操作
        addPricePostHandler.handler(processBO);
        // 货源诊断任务（加价、填价、转一口价、补全货物信息、曝光）完成时，重置倒计时
        diagnosisTaskCountDownPostHandler.handler(processBO);
        // 首次由非相似货源变为相似货源push
        sameTransportPushPostHandler.handler(processBO.getTransportMain());
        // 填价、加价缓存记录
        priceChangeCachePostHandler.handler(processBO);
    }

}
