package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 加价校验
 *
 * <AUTHOR>
 * @since 2025/03/09 16:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddPriceChecker {

    private final RedisUtil redisUtil;

    public void checkDirectPublish(DirectPublishProcessBO processBO) {
        // 非加价操作忽略
        if (!PublishOptEnum.ADD_PRICE.equals(processBO.getOptEnum())) {
            return;
        }

        // 加价间隔校验
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        String addPriceTimeKey = ConfigKeyConstant.TRANSPORT_ADD_PRICE_INTERVAL + directPublishBO.getSrcMsgId();
        // 报价修改运费不再受十分钟限制
        if (directPublishBO.isCheckAddPriceInterval()) {
            // 校验时间差
            if (redisUtil.get(addPriceTimeKey) != null) {
                // 6710新版发货：融合版加价不受10分钟限制，但10分钟内只置顶一次
                if (processBO.getBaseParam().getClientFusion() != null) {
                    processBO.getDirectPublishBO().setTopFlag(false);
                } else {
                    log.info("货源10分钟内已加价，srcMsgId:{}", directPublishBO.getSrcMsgId());
                    throw new BusinessException(GoodsErrorCode.ADD_PRICE_INTERVAL_LIMIT);
                }
            }
        }

    }

}
