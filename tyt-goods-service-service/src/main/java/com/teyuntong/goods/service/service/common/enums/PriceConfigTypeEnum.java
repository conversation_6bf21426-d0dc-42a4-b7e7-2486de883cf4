package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 专车价格配置价格类型枚举
 * <AUTHOR>
 * @since 2024-06-22 18:19
 */
@Getter
public enum PriceConfigTypeEnum {
    FIXED_PRICE(1, "固定价"),
    KILOMETER_PRICE(2, "公里价");

    private Integer code;
    private String name;
    PriceConfigTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
