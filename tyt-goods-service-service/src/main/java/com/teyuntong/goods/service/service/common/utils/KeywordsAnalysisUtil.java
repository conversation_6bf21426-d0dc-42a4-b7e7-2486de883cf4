package com.teyuntong.goods.service.service.common.utils;

import cn.hutool.extra.tokenizer.Result;
import cn.hutool.extra.tokenizer.TokenizerEngine;
import cn.hutool.extra.tokenizer.TokenizerUtil;
import cn.hutool.extra.tokenizer.Word;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/5/10 20:21
 */
public class KeywordsAnalysisUtil {

    /**
     * 将keywords进行分词
     *
     * @param keywords
     * @return
     */
    public static Set<String> getkeywordsSet(String keywords) {
        TokenizerEngine engine = TokenizerUtil.createEngine();
        Result parse = engine.parse(keywords);
        Set<String> keywordsSet = new HashSet<>();
        for (Word word : parse) {
            keywordsSet.add(word.getText());
        }
        return keywordsSet;
    }

}
