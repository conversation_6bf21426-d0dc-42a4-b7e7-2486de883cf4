package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userRemoteService", fallbackFactory = UserRemoteService.UserServiceRemoteFallbackFactory.class)
public interface UserRemoteService extends UserRpcService {

    @Component
    class UserServiceRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<UserRemoteService>{
        protected UserServiceRemoteFallbackFactory() {
            super(true, UserRemoteService.class);
        }
    }
}