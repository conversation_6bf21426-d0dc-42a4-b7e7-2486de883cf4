package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 捂货配置
 *
 * @author: helian
 * @since: 2024/01/12 20:14
 */
@Getter
@AllArgsConstructor
public enum CoverGoodsEnum {
    /**
     * 发货勾选配置
     */
    GOODS_CHECK(1),
    /**
     * 引导开关配置
     */
    SWITCH_GUIDE(2),
    /**
     * 优车货源默认为优推好车主
     */
    EXCELLENT_COVER(3);

    private final Integer configCode;
}
