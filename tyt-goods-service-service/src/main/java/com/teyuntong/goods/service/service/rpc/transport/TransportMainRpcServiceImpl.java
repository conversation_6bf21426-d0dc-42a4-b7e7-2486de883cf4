package com.teyuntong.goods.service.service.rpc.transport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.client.publish.dto.PublishGoodsTypeDTO;
import com.teyuntong.goods.service.client.publish.vo.PublishGoodsTypeResultVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.SpecialCarPriceConfigDO;
import com.teyuntong.goods.service.client.publish.dto.CalculatePriceDTO;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.SpecialCarPriceConfigMapper;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.user.ExcellentGoodsCardRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.builder.CalcSpecialGoodsPriceService;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.UserPermissionStatusEnum;
import com.teyuntong.user.service.client.permission.enums.UserPermissionTypeEnum;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运输代调服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class TransportMainRpcServiceImpl implements TransportMainRpcService {

    private static final Integer MAX_SIZE = 100;

    private final TransportMainService transportMainService;
    private final StringRedisTemplate stringRedisTemplate;
    private final GoodCarPriceTransportService goodCarPriceTransportService;
    private final UserPermissionRemoteService userPermissionRemoteService;
    private final ExcellentGoodsCardRemoteService excellentGoodsCardRemoteService;
    private final ABTestRemoteService abTestRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final DispatchCargoOwnerService dispatchCargoOwnerService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final SpecialCarPriceConfigService specialCarPriceConfigService;
    private final TransportMainExtendService transportMainExtendService;
    private final UserRemoteService userServiceRemoteService;


    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final SpecialCarPriceConfigMapper specialCarPriceConfigMapper;
    private final CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;

    private final TransportValuableService transportValuableService;


    /**
     * 专车货主管理-平台
     */
    public static final String PLAT_CARGO_OWNER_NAME = "平台";
    /**
     * 发布拼车功能ab测
     */
    public static final String ABTEST_CODE_PUBLISH_CARPOOLING = "publish_carpooling";
    /**
     * 拼车运距下限
     */
    public static final String CARPOOL_DISTANCE_LIMIT = "carpool_distance_limit";
    /**
     * 拼车吨位上限
     */
    public static final String CARPOOL_TONNAGE_LIMIT = "carpool_tonnage_limit";
    /**
     * 专车匹配零担运费规则吨位阈值
     */
    public static final String SPECIAL_CAR_PRICE_CONFIG_TONNAGE = "special_car_price_config_tonnage";

    public static final String TransportDynamicRedisKey = "transport_dynamic";

    public static final String TransportDynamicTimesRedisKey = "transport_dynamic:times";


    @Override
    public List<TransportMainVO> queryByIds(List<Long> srcMsgIdList) {
        if (CollUtil.isEmpty(srcMsgIdList)) {
            throw new BusinessException(GoodsErrorCode.ERROR_SRC_MSG_ID_LACK);
        }
        if (srcMsgIdList.size() > MAX_SIZE) {
            throw new BusinessException(GoodsErrorCode.ERROR_OVER_LIMIT);
        }
        List<TransportMainDO> transportMainDOList = transportMainService.listByIds(srcMsgIdList);
        List<TransportMainVO> transportMainVOS = TytBeanUtil.convertBeanList(transportMainDOList, TransportMainVO.class);
        setTransportMainExtend(transportMainVOS);
        setInvoiceInfo(transportMainVOS);
        return transportMainVOS;
    }


    @Override
    public TransportMainVO queryById(Long srcMsgId) {
        if (srcMsgId == null || srcMsgId == 0L) {
            throw new BusinessException(GoodsErrorCode.ERROR_SRC_MSG_ID_LACK);
        }

        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        TransportMainVO transportMainVO = TytBeanUtil.convertBean(transportMainDO, TransportMainVO.class);
        if (transportMainVO != null) {
            setTransportMainExtend(List.of(transportMainVO));
            setInvoiceInfo(List.of(transportMainVO));
        }
        return transportMainVO;
    }

    @Override
    public List<Long> getInReleaseInvoiceTransportSrcMsgIdList(Long userId) {
        return transportMainService.getInReleaseInvoiceTransportSrcMsgIdList(userId);
    }

    @Override
    public Boolean checkUserIsTransportOwner(Long userId, Long srcMsgId) {
        if (userId == null || srcMsgId == null) {
            return false;
        }
        TransportMainDO transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain == null || transportMain.getUserId() == null) {
            return false;
        }
        return userId.compareTo(transportMain.getUserId()) == 0;
    }

    /**
     * 货源列表
     *
     * @param dto
     * @return List<MyTransportListVO>
     * <AUTHOR>
     * @date 2025/1/6 17:26
     */
    @Override
    public MyTransportVO getMyPublish(TransportListDTO dto) {
        return transportMainService.getMyPublish(dto);
    }


    @Override
    public TransportDynamicVO getTransportDynamic(Long userId) {
        String transportDynamicRedisKey = stringRedisTemplate.opsForValue().get(TransportDynamicRedisKey + ":" + userId);
        if (StringUtils.isNotBlank(transportDynamicRedisKey)) {
            TransportDynamicVO transportDynamicVO = new TransportDynamicVO();
            transportDynamicVO.setShowTransportDynamicTab(false);
            return transportDynamicVO;
        }

        String transportDynamicTimesRedisKey = stringRedisTemplate.opsForValue().get(TransportDynamicTimesRedisKey + ":" + userId);
        int times = 0;
        if (StringUtils.isNotBlank(transportDynamicTimesRedisKey)) {
            times = Integer.parseInt(transportDynamicTimesRedisKey);
            if (times >= 3) {
                TransportDynamicVO transportDynamicVO = new TransportDynamicVO();
                transportDynamicVO.setShowTransportDynamicTab(false);
                return transportDynamicVO;
            }
        }
        return transportMainService.getTransportDynamic(userId, times);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByCarUser(Long carUserId, Long srcMsgId) {
        return transportMainService.checkIsNeedFreeTecSericeFeeByCarUser(carUserId, srcMsgId);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(Long transportUserId, String startCity, boolean isGoodCarPriceTransport) {
        return transportMainService.checkIsNeedFreeTecSericeFeeByTransport(transportUserId, startCity, isGoodCarPriceTransport);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeByTransport(Long transportUserId, String startCity,
                                                                                  boolean isGoodCarPriceTransport, Integer goodTransportLabel) {
        return transportMainService.checkIsNeedFreeTecServiceFeeByTransport(transportUserId, startCity, isGoodCarPriceTransport, goodTransportLabel);
    }

    @Override
    public Long getLastTransportInvoiceSubjectId(Long userId) {
        return transportMainService.getLastTransportInvoiceSubjectId(userId);
    }

    @Override
    public void initRecordGoodsTransactionInfo(Long srcMsgId) {
        TransportMainDO transportMainForId = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMainForId != null) {
            transportMainService.initRecordGoodsTransactionInfo(transportMainForId);
        }
    }

    /**
     * 校验是否满足拼车
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean checkMatchCarpool(GoodsPointDTO dto) {
        try {
            if (Objects.equals(dto.getInvoiceTransport(), 1)) {
                return false;
            }
            if (Objects.isNull(dto.getWeight()) || Objects.isNull(dto.getDistance()) || StringUtils.isBlank(dto.getStartCity())
                    || StringUtils.isBlank(dto.getDestCity()) || Objects.isNull(dto.getUserId())) {
                return false;
            }
            GoodsPointResultDTO resultDTO = new GoodsPointResultDTO();
            carpoolProcess(dto, resultDTO, null);
            return resultDTO.getShowCarpool() && StringUtils.isBlank(resultDTO.getCarpoolNotice());
        } catch (Exception e) {
            log.error("checkMatchCarpool error, param:{}", dto, e);
        }
        return false;
    }

    /**
     * <p>获取发货锚点和发货方式</p>
     * <a href="https://alidocs.dingtalk.com/i/nodes/vy20BglGWOMYDzb9hEwKzGQeJA7depqY?utm_scene=team_space">需求链接</a>
     *
     * @param goodsPointDTO 请求参数实体
     * @return GoodsPointResultDTO  返回值实体
     */
    @Override
    public GoodsPointResultDTO getGoodsPoint(GoodsPointDTO goodsPointDTO) {
        GoodsPointResultDTO resultDTO = new GoodsPointResultDTO();
        // 整车默认锚点
        Integer goodsPoint = getGoodsPointValue(goodsPointDTO);
        resultDTO.setGoodsPoint(goodsPoint);
        if (Objects.nonNull(goodsPointDTO.getStartCity()) && Objects.nonNull(goodsPointDTO.getDestCity()) &&
                Objects.nonNull(goodsPointDTO.getDistance()) && Objects.nonNull(goodsPointDTO.getWeight())) {
            if (Objects.equals(goodsPointDTO.getInvoiceTransport(), 1)) {
                resultDTO.setShowCarpool(false);
                resultDTO.setSelectedUseCarType(UseCarTypeEnum.FULL.getCode());
                return resultDTO;
            }

            // 编辑重发，再发一单处理
            if (Objects.nonNull(goodsPointDTO.getSrcMsgId())) {
                TransportMainDO mainDO = transportMainService.getById(goodsPointDTO.getSrcMsgId());
                TransportMainExtendDO extendDO = transportMainExtendService.getBySrcMsgId(goodsPointDTO.getSrcMsgId());
                if (Objects.nonNull(extendDO)) {
                    // APP货源点击编辑再发布时，回填货源上次选择的整车、拼车选项，若当前未展示选项则不勾选，若不满足拼车条件时，默认为整车
                    if (Objects.isNull(extendDO.getUseCarType()) || Objects.equals(UseCarTypeEnum.FULL.getCode(), extendDO.getUseCarType())) {
                        carpoolProcess(goodsPointDTO, resultDTO, null);
                    } else {
                        carpoolProcess(goodsPointDTO, resultDTO, mainDO.getExcellentGoods());
                        if (resultDTO.getShowCarpool() && StringUtils.isBlank(resultDTO.getCarpoolNotice())) {
                            resultDTO.setSelectedUseCarType(UseCarTypeEnum.PART.getCode());
                        }
                    }
                    return resultDTO;
                }
            }

            // 拼车判断及处理
            carpoolProcess(goodsPointDTO, resultDTO, null);
        } else {
            boolean userInCarpoolAbTest = userInCarpoolAbTest(goodsPointDTO.getUserId());
            if (userInCarpoolAbTest && !Objects.equals(goodsPointDTO.getInvoiceTransport(), 1)) {
                resultDTO.setShowCarpool(true);
                resultDTO.setSelectedUseCarType(UseCarTypeEnum.FULL.getCode());
                resultDTO.setCarpoolNotice("请先填写装卸地和货物信息");
            }
        }

        return resultDTO;
    }

    private boolean userInCarpoolAbTest(Long userId) {
        // 发布拼车AB测
        Integer userType = abTestRemoteService.getUserType(ABTEST_CODE_PUBLISH_CARPOOLING, userId);
        return Objects.equals(userType, 1);
    }

    /**
     * 判断是否符合拼车
     *
     * @param goodsPointDTO
     * @param resultDTO
     * @param lastExcellentGoods
     */
    private void carpoolProcess(GoodsPointDTO goodsPointDTO, GoodsPointResultDTO resultDTO, Integer lastExcellentGoods) {
        String distanceLimit = tytConfigRemoteService.getStringValue(CARPOOL_DISTANCE_LIMIT, "200");
        String tonnageLimit = tytConfigRemoteService.getStringValue(CARPOOL_TONNAGE_LIMIT, "25");
        boolean userInAbtest = userInCarpoolAbTest(goodsPointDTO.getUserId());
        boolean distanceMatch = goodsPointDTO.getDistance().compareTo(new BigDecimal(distanceLimit)) >= 0;
        boolean tonnageMatch = goodsPointDTO.getWeight().compareTo(new BigDecimal(tonnageLimit)) <= 0 &&
                goodsPointDTO.getWeight().compareTo(new BigDecimal(0)) > 0;
        // 专车拼车
        boolean specialCarCarpoolMatch = isShowSpecialCarCarpool(goodsPointDTO);

        if (specialCarCarpoolMatch) {
            resultDTO.setShowCarpool(true);
            resultDTO.setSelectedExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
            List<PublishTypeDTO> carpoolExcellentGoodsList = getCarpoolExcellentGoodsList(ExcellentGoodsEnums.SPECIAL);

            if (userInAbtest && distanceMatch && tonnageMatch) {
                // 满足专车拼车也满足用户在拼车AB测
                carpoolExcellentGoodsList.addAll(getCarpoolExcellentGoodsList(ExcellentGoodsEnums.NORMAL));
                if (Objects.equals(ExcellentGoodsEnums.NORMAL.getCode(), lastExcellentGoods)) {
                    resultDTO.setSelectedExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                }
            }
            resultDTO.setCarpoolExcellentGoodsList(carpoolExcellentGoodsList);
        } else if (userInAbtest) {
            // 只满足用户在拼车AB测
            resultDTO.setShowCarpool(true);
            String carpoolNotice = null;
            if (!tonnageMatch) {
                carpoolNotice = "拼车仅支持" + tonnageLimit + "吨以下的货物";
                resultDTO.setSelectedUseCarType(UseCarTypeEnum.FULL.getCode());
            } else if (!distanceMatch) {
                carpoolNotice = "拼车仅支持" + distanceLimit + "公里以上的货物";
                resultDTO.setSelectedUseCarType(UseCarTypeEnum.FULL.getCode());
            } else {
                resultDTO.setCarpoolExcellentGoodsList(getCarpoolExcellentGoodsList(ExcellentGoodsEnums.NORMAL));
                resultDTO.setSelectedExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            }
            resultDTO.setCarpoolNotice(carpoolNotice);
        } else {
            // 不满足拼车
            resultDTO.setShowCarpool(false);
        }
    }

    private List<PublishTypeDTO> getCarpoolExcellentGoodsList(ExcellentGoodsEnums excellentGoodsEnums) {
        List<PublishTypeDTO> carpoolExcellentGoodsList = new ArrayList<>();
        PublishTypeDTO typeDTO = new PublishTypeDTO();
        typeDTO.setExcellentGoods(excellentGoodsEnums.getCode());
        typeDTO.setExcellentGoodsName(excellentGoodsEnums.getName());
        carpoolExcellentGoodsList.add(typeDTO);
        return carpoolExcellentGoodsList;
    }

    /**
     * 是否满足专车拼车条件
     *
     * @param goodsPointDTO
     * @return
     */
    private boolean isShowSpecialCarCarpool(GoodsPointDTO goodsPointDTO) {
        if (Objects.isNull(goodsPointDTO.getDistance()) || goodsPointDTO.getDistance().compareTo(new BigDecimal(0)) == 0) {
            return false;
        }
        Long cargoOwnerId = null;
        DispatchCargoOwnerDO owner = dispatchCargoOwnerService.selectSignedByUserId(goodsPointDTO.getUserId());
        if (Objects.isNull(owner)) {
            DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName(PLAT_CARGO_OWNER_NAME);
            if (Objects.nonNull(cooperative)) {
                cargoOwnerId = cooperative.getId();
            }
        } else {
            cargoOwnerId = owner.getCooperativeId();
        }
        if (Objects.isNull(cargoOwnerId)) {
            return false;
        }

        BigDecimal weight = goodsPointDTO.getWeight();
        SpecialCarPriceConfigDO priceConfig = specialCarPriceConfigService.selectMatchPriceConfig(cargoOwnerId,
                goodsPointDTO.getStartCity(), goodsPointDTO.getDestCity(), weight);
        if (Objects.nonNull(priceConfig) && StringUtils.isNotEmpty(priceConfig.getLessPriceRule())) {
            String tonnage = tytConfigRemoteService.getStringValue(SPECIAL_CAR_PRICE_CONFIG_TONNAGE, "28");
            if (weight.compareTo(new BigDecimal(tonnage)) <= 0) {
                CalculatePriceDTO priceDTO = new CalculatePriceDTO();
                priceDTO.setStartCity(goodsPointDTO.getStartCity());
                priceDTO.setDestCity(goodsPointDTO.getDestCity());
                priceDTO.setUserId(goodsPointDTO.getUserId());
                priceDTO.setWeight(String.valueOf(goodsPointDTO.getWeight()));
                priceDTO.setDistanceKilometer(goodsPointDTO.getDistance());
                priceDTO.setUseCarType(UseCarTypeEnum.PART.getCode());
                CalcSpecialGoodsPriceResultDTO priceResultDTO = calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
                if (Objects.nonNull(priceResultDTO) && Objects.nonNull(priceResultDTO.getPrice()) && priceResultDTO.getPrice().compareTo(new BigDecimal(0)) > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取发货锚点
     *
     * @param goodsPointDTO
     * @return
     */
    private Integer getGoodsPointValue(GoodsPointDTO goodsPointDTO) {
        try {
            Long userId = goodsPointDTO.getUserId();
            List<UserPermissionRpcVO> userPermission = getUserPermission(userId);
            if (Objects.nonNull(goodsPointDTO.getSrcMsgId())) {
                // 编辑发布和再发一单
                return getGoodsPointValueForModify(goodsPointDTO);
            }

            //如果出发地目的地在专车路线运费配置里面存在非平台的配置，则默认勾选专车
            int count = specialCarPriceConfigMapper.countMatchPriceConfigCityAndRule(goodsPointDTO.getStartCity(), goodsPointDTO.getDestCity());
            if (count > 0) {
                return GoodsPublishTypeEnum.SPECIAL_GOODS.getCode();
            }

            Boolean excellent = excellentGoodsCardRemoteService.haveExcellentGoodsPermission(userId);
            if (Objects.nonNull(excellent) && excellent) {
                return GoodsPublishTypeEnum.EXCELLENT_GOODS.getCode();
            }

            if (isHavePublishGoodsPermission(userPermission)) {
                return GoodsPublishTypeEnum.NORMAL_GOODS.getCode();
            }

        } catch (Exception e) {
            log.error("getGoodsPointValue error, param:{}", goodsPointDTO, e);
        }
        return GoodsPublishTypeEnum.NONE.getCode();
    }

    private List<UserPermissionRpcVO> getUserPermission(Long userId) {
        UserPermissionRpcDTO permissionRpcDTO = new UserPermissionRpcDTO();
        permissionRpcDTO.setUserId(userId);
        permissionRpcDTO.setStatus(UserPermissionStatusEnum.EFFECTIVE.getStatus());
        return userPermissionRemoteService.getUserPermissionByUserId(permissionRpcDTO);
    }

    /**
     * 处理货源编辑发布和再发一单
     *
     * @param goodsPointDTO
     * @return
     */
    private int getGoodsPointValueForModify(GoodsPointDTO goodsPointDTO) {
        TransportMainDO mainDO = transportMainService.getById(goodsPointDTO.getSrcMsgId());
        if (Objects.isNull(mainDO)) {
            return GoodsPublishTypeEnum.NORMAL_GOODS.getCode();
        }
        Integer excellentGoods = mainDO.getExcellentGoods();
        if (Objects.equals(excellentGoods, ExcellentGoodsEnums.EXCELLENT.getCode())) {
            // 先判断是否优车2.0
            TransportLabelJson transportLabelJson = JSON.parseObject(mainDO.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson != null && Objects.equals(transportLabelJson.getGoodCarPriceTransport(), 1)) {
                // 非优车一口价货源判断是否符合优车定价货源条件，如果符合则自动转优车定价
                TransportCarryDTO carryDTO = TransportCarryDTO.builder()
                        .startProvince(mainDO.getStartProvinc()).startCity(mainDO.getStartCity()).startArea(mainDO.getStartArea())
                        .destProvince(mainDO.getDestProvinc()).destCity(mainDO.getDestCity()).destArea(mainDO.getDestArea())
                        .goodsWeight(mainDO.getWeight())
                        .goodsLength(mainDO.getLength()).goodsWide(mainDO.getWide()).goodsHigh(mainDO.getHigh())
                        .distance(Objects.nonNull(mainDO.getDistance()) ? mainDO.getDistance().toString() : "0")
                        .userId(mainDO.getUserId())
                        .publishType(mainDO.getPublishType()).price(mainDO.getPrice()).goodTypeName(mainDO.getGoodTypeName())
                        .oldSrcMsgId(mainDO.getSrcMsgId())
                        .build();
                if (goodCarPriceTransportService.checkTransportIsGoodCarPriceTransport(carryDTO)) {
                    return GoodsPublishTypeEnum.EXCELLENT_GOODS_2.getCode();
                }
            } else { // 优车1.0
                // 是否有签约有优车发货权益
                Boolean excellent = excellentGoodsCardRemoteService.haveExcellentGoodsPermission(goodsPointDTO.getUserId());
                if (Objects.nonNull(excellent) && excellent) {
                    return GoodsPublishTypeEnum.EXCELLENT_GOODS.getCode();
                }
            }
        } else if (Objects.equals(excellentGoods, ExcellentGoodsEnums.SPECIAL.getCode())) {
            return GoodsPublishTypeEnum.SPECIAL_GOODS.getCode();
        }
        return GoodsPublishTypeEnum.NORMAL_GOODS.getCode();
    }

    private static boolean isHaveGoodsVipPermission(List<UserPermissionRpcVO> userPermission) {
        UserPermissionRpcVO userPermissionRpcVO = userPermission.stream()
                .filter(v -> Objects.equals(v.getServicePermissionTypeId(), UserPermissionTypeEnum.GOODS_VIP.getTypeId()))
                .findFirst()
                .orElse(null);
        return Objects.nonNull(userPermissionRpcVO);
    }

    private static boolean isHavePublishGoodsPermission(List<UserPermissionRpcVO> userPermission) {
        UserPermissionRpcVO userPermissionRpcVO = userPermission.stream()
                .filter(v -> Objects.equals(v.getServicePermissionId(), 10020)) // 普通发货权益
                .filter(v -> Objects.nonNull(v.getEndTime()) && v.getEndTime().after(TimeUtil.addDay(-1)))
                .findFirst()
                .orElse(null);
        return Objects.nonNull(userPermissionRpcVO);
    }

    /**
     * 查询扩展货源信息
     *
     * @param srcMsgId
     */
    @Override
    public TransportMainExtendVO getExtendBySrcMsgId(Long srcMsgId) {
        List<TransportMainExtendDO> extendList = transportMainService.getExtendList(Collections.singletonList(srcMsgId));
        return extendList.stream().findAny()
                .map(extend -> TytBeanUtil.convertBean(extend, TransportMainExtendVO.class))
                .orElse(null);
    }

    /**
     * 查询扩展货源信息
     *
     * @param srcMsgIdList
     */
    @Override
    public List<TransportMainExtendVO> getExtendBySrcMsgIds(List<Long> srcMsgIdList) {
        List<TransportMainExtendDO> extendList = transportMainService.getExtendList(srcMsgIdList);
        return TytBeanUtil.convertBeanList(extendList, TransportMainExtendVO.class);
    }

    /**
     * 查询相似货源数量
     *
     * @param similarityDTO
     */
    @Override
    public int getSimilarityCount(TransportSimilarityDTO similarityDTO) {
        String similarityCode;
        if (similarityDTO.getSrcMsgId() != null) {
            TransportMainDO transportMainDO = transportMainService.getById(similarityDTO.getSrcMsgId());
            if (transportMainDO == null) {
                return 0;
            }
            similarityCode = transportMainDO.getSimilarityCode();
        } else {
            TransportMainDO transportMainDO = new TransportMainDO();
            BeanUtils.copyProperties(similarityDTO, transportMainDO);
            similarityCode = transportMainService.genSimilarityCode(transportMainDO, false);
        }

        return transportMainService.countSimilarityGoods(similarityCode, similarityDTO.getSrcMsgId());
    }

    @Override
    public List<TransportMainVO> getLastDayTransport(Long userId) {

        Date lastPublishTime = transportMainService.hasUserTransportLast30Day(userId);
        if (null == lastPublishTime) {
            return new ArrayList<>();
        }
        String lastPublishDay = DateFormatUtils.ISO_DATE_FORMAT.format(lastPublishTime);
        List<TransportMainDO> userSomeDayTransportData = transportMainService.getUserSomeDayTransportData(userId, lastPublishDay);
        return TytBeanUtil.convertBeanList(userSomeDayTransportData, TransportMainVO.class);

    }

    @Override
    public Integer getPublishCountByUserId(Long userId, Date startTime, Date endTime) {
        return transportMainService.getPublishCountByUserId(userId, startTime, endTime);
    }

    @Override
    public List<TransportMainVO> getUserCommissionGoods(Long userId) {
        List<TransportMainDO> mainDOList = transportMainService.getUserCommissionGoods(userId);
        return TytBeanUtil.convertBeanList(mainDOList, TransportMainVO.class);
    }

    private void setTransportMainExtend(List<TransportMainVO> transportMainVOs) {
        List<Long> srcMsgIds = transportMainVOs.stream().map(TransportMainVO::getSrcMsgId).toList();
        Map<Long, TransportMainExtendDO> extendMap = transportMainService.getExtendList(srcMsgIds)
                .stream()
                .collect(Collectors.toMap(TransportMainExtendDO::getSrcMsgId, t -> t));
        for (TransportMainVO transportMainVO : transportMainVOs) {
            TransportMainExtendDO extendDO = extendMap.get(transportMainVO.getSrcMsgId());
            if (extendDO != null) {
                transportMainVO.setUseCarType(extendDO.getUseCarType());
                transportMainVO.setPerkPrice(extendDO.getPerkPrice());
            }
        }
    }

    /**
     * 设置开票货源相关信息
     *
     * @param transportMainVOS
     */
    private void setInvoiceInfo(List<TransportMainVO> transportMainVOS) {
        if (CollUtil.isNotEmpty(transportMainVOS)) {
            List<Long> srcMsgIds = transportMainVOS.stream().filter(t -> Objects.equals(t.getInvoiceTransport(), YesOrNoEnum.YES.getId()))
                    .map(TransportMainVO::getSrcMsgId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(srcMsgIds)) {
                List<TransportEnterpriseLogDO> transportEnterpriseLogDOList = transportEnterpriseLogService.getBySrcMsgIds(srcMsgIds);
                if (CollUtil.isEmpty(transportEnterpriseLogDOList)) {
                    return;
                }
                Map<Long, TransportEnterpriseLogDO> collect = transportEnterpriseLogDOList.stream().collect(Collectors.toMap(TransportEnterpriseLogDO::getSrcMsgId, t -> t, (k, v) -> k));
                for (TransportMainVO transportMainVO : transportMainVOS) {
                    if (Objects.equals(transportMainVO.getInvoiceTransport(), YesOrNoEnum.YES.getId())) {
                        if (collect.containsKey(transportMainVO.getSrcMsgId())) {
                            TransportEnterpriseLogDO transportEnterpriseLogDO = collect.get(transportMainVO.getSrcMsgId());
                            transportMainVO.setPaymentsType(transportEnterpriseLogDO.getPaymentsType());
                            transportMainVO.setPrepaidPrice(transportEnterpriseLogDO.getPrepaidPrice());
                            transportMainVO.setCollectedPrice(transportEnterpriseLogDO.getCollectedPrice());
                            transportMainVO.setReceiptPrice(transportEnterpriseLogDO.getReceiptPrice());
                        }
                    }

                }

            }

        }


    }

    /**
     * 判断货源是否是超额保障货源
     * 条件：直客（个人货主或企业货主）发布的货源 或 优车2.0 或 抽佣货源
     *
     * @param srcMsgId 货源id
     * @return true/false
     */
    @Override
    public Boolean isExcessGuaranteeTransport(Long srcMsgId) {
        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        if (transportMainDO == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }
        // 优车2.0 或 抽佣货源
        TransportLabelJson labelJson = JSON.parseObject(transportMainDO.getLabelJson(), TransportLabelJson.class);
        if (labelJson != null &&
                (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)
                        || Objects.equals(labelJson.getCommissionTransport(), 1))) {
            return true;
        }
        // 或 直客发布货源（直客=个人货主或企业货主）
        DwsNewIdentiwoDataRpcVO userIdentity = userServiceRemoteService.getDwsNewIdentiwoDataByUserId(transportMainDO.getUserId());
        return userIdentity != null && UserBiIdentityEnum.isCargoOwner(userIdentity.getType());
    }

    @Override
    public SimilarityTransportTurnoverRatioVO getSimilarityTransportTurnoverRatio(TransportSimilarityDTO similarityDTO) {
        return transportMainService.getSimilarityTransportTurnoverRatio(similarityDTO);
    }

    @Override
    public Boolean getSimilarityTransportHavePriceCount(String similarityCode) {
        return transportMainService.getSimilarityTransportHavePriceCount(similarityCode);
    }

    /**
     * 删除我的非发布中的货源
     */
    @Override
    public Boolean deleteMyGoods(Long srcMsgId) {
        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        if (transportMainDO == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        if (!Objects.equals(transportMainDO.getUserId(), userId)) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_BELONG_TO_ONESELF);
        }

        if (TransportStatusEnum.isPublish(transportMainDO.getStatus())
                && transportMainDO.getSource() == 0
                && transportMainDO.getDisplayType().equals("1")
                && transportMainDO.getCtime().after(DateUtil.beginOfDay(new Date()))) {
            throw new BusinessException(GoodsErrorCode.DELETE_GOODS_IN_PUBLISH);
        }

        log.info("删除货源，userId：{}，srcMsgId：{}", userId, srcMsgId);
        transportMainService.deleteMyGoods(srcMsgId);
        return Boolean.TRUE;
    }

    @Override
    public int getTransportCountForUserId(TransportCountDTO transportCountDTO) {
        return transportMainService.getTransportCountForUserId(transportCountDTO);
    }

    @Override
    public List<Long> getTransportForUser(Long userId, Integer status) {
        return transportMainService.getTransportForUser(userId, status, null, null);
    }

    @Override
    public PublishGoodsTypeResultVO processCarpool(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        if (Objects.equals(publishGoodsTypeDTO.getInvoiceTransport(), 1)) {
            return null;
        }
        PublishGoodsTypeResultVO resultVO = new PublishGoodsTypeResultVO();
        String distanceLimit = tytConfigRemoteService.getStringValue(CARPOOL_DISTANCE_LIMIT, "200");
        String tonnageLimit = tytConfigRemoteService.getStringValue(CARPOOL_TONNAGE_LIMIT, "25");
        boolean userInAbtest = userInCarpoolAbTest(publishGoodsTypeDTO.getUserId());
        boolean distanceMatch = publishGoodsTypeDTO.getDistance().compareTo(new BigDecimal(distanceLimit)) >= 0;
        boolean tonnageMatch = publishGoodsTypeDTO.getGoodsWeight().compareTo(new BigDecimal(tonnageLimit)) <= 0 &&
                publishGoodsTypeDTO.getGoodsWeight().compareTo(new BigDecimal(0)) > 0;
        List<PublishGoodsTypeVO> carpoolPublishTypeList = new ArrayList<>();
        // 专车拼车
        PublishGoodsTypeVO specialCarCarpoolPublishType = getSpecialCarCarpool(publishGoodsTypeDTO);

        if (null != specialCarCarpoolPublishType) {
            resultVO.setShowCarpool(true);
            carpoolPublishTypeList.add(specialCarCarpoolPublishType);
            if (userInAbtest && distanceMatch && tonnageMatch) {
                // 满足专车拼车也满足用户在拼车AB测
                carpoolPublishTypeList.addAll(getNormalPublishGoodsTypeList());
            }
            resultVO.setCarpoolPublishTypeList(carpoolPublishTypeList);
        } else if (userInAbtest) {
            // 只满足用户在拼车AB测
            resultVO.setShowCarpool(true);
            String carpoolNotice = null;
            if (!tonnageMatch) {
                carpoolNotice = "拼车仅支持" + tonnageLimit + "吨以下的货物";
            } else if (!distanceMatch) {
                carpoolNotice = "拼车仅支持" + distanceLimit + "公里以上的货物";
            } else {
                resultVO.setCarpoolPublishTypeList(getNormalPublishGoodsTypeList());
            }
            resultVO.setCarpoolNotice(carpoolNotice);
        } else {
            // 不满足拼车
            resultVO.setShowCarpool(false);
        }
        return resultVO;
    }

    private PublishGoodsTypeVO getSpecialCarCarpool(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        if (Objects.isNull(publishGoodsTypeDTO.getDistance()) || publishGoodsTypeDTO.getDistance().compareTo(new BigDecimal(0)) == 0) {
            return null;
        }
        Long cargoOwnerId = null;
        DispatchCargoOwnerDO owner = dispatchCargoOwnerService.selectSignedByUserId(publishGoodsTypeDTO.getUserId());
        if (Objects.isNull(owner)) {
            DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName(PLAT_CARGO_OWNER_NAME);
            if (Objects.nonNull(cooperative)) {
                cargoOwnerId = cooperative.getId();
            }
        } else {
            cargoOwnerId = owner.getCooperativeId();
        }
        if (Objects.isNull(cargoOwnerId)) {
            return null;
        }

        BigDecimal weight = publishGoodsTypeDTO.getGoodsWeight();
        SpecialCarPriceConfigDO priceConfig = specialCarPriceConfigService.selectMatchPriceConfig(cargoOwnerId,
                publishGoodsTypeDTO.getStartCity(), publishGoodsTypeDTO.getDestCity(), weight);
        if (Objects.nonNull(priceConfig) && StringUtils.isNotEmpty(priceConfig.getLessPriceRule())) {
            String tonnage = tytConfigRemoteService.getStringValue(SPECIAL_CAR_PRICE_CONFIG_TONNAGE, "28");
            if (weight.compareTo(new BigDecimal(tonnage)) <= 0) {
                CalculatePriceDTO priceDTO = getCalculatePriceDTO(publishGoodsTypeDTO);
                CalcSpecialGoodsPriceResultDTO priceResultDTO = calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
                if (Objects.nonNull(priceResultDTO) && Objects.nonNull(priceResultDTO.getPrice()) && priceResultDTO.getPrice().compareTo(new BigDecimal(0)) > 0) {
                    PublishGoodsTypeVO vo = newPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS, PublishTypeEnum.FIXED.getCode());
                    vo.setFixPriceFast(priceResultDTO.getPrice().compareTo(BigDecimal.ONE) ==0 ? null : priceResultDTO.getPrice());
                    vo.setFixPriceMin(priceResultDTO.getLowerLimitPrice());
                    vo.setNextStepDesc("免费发货");
                    return vo;
                }
            }
        }
        return null;
    }

    private CalculatePriceDTO getCalculatePriceDTO(PublishGoodsTypeDTO publishGoodsTypeDTO) {
        CalculatePriceDTO priceDTO = new CalculatePriceDTO();
        priceDTO.setStartCity(publishGoodsTypeDTO.getStartCity());
        priceDTO.setDestCity(publishGoodsTypeDTO.getDestCity());
        priceDTO.setUserId(publishGoodsTypeDTO.getUserId());
        priceDTO.setWeight(String.valueOf(publishGoodsTypeDTO.getGoodsWeight()));
        priceDTO.setDistanceKilometer(publishGoodsTypeDTO.getDistance());
        priceDTO.setUseCarType(UseCarTypeEnum.PART.getCode());
        return priceDTO;
    }

    private List<PublishGoodsTypeVO> getNormalPublishGoodsTypeList(){
        List<PublishGoodsTypeVO> list = new ArrayList<>();
        list.add(newPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS, PublishTypeEnum.TELE.getCode()));
        list.add(newPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS, PublishTypeEnum.TELE.getCode()));
        return list;
    }

    private PublishGoodsTypeVO newPublishGoodsType(PublishGoodsTypeEnum typeEnum, Integer publishType){
        PublishGoodsTypeVO publishGoodsTypeVO = new PublishGoodsTypeVO();
        publishGoodsTypeVO.setPublishGoodsType(typeEnum.getCode());
        publishGoodsTypeVO.setPublishGoodsTypeName(typeEnum.getName());
        publishGoodsTypeVO.setPublishType(publishType);
        publishGoodsTypeVO.setChooseType(1);
        return publishGoodsTypeVO;
    }



    @Override
    public void finishTransportValuable(Long srcMsgId) {
        TransportValuableDO transportValuableDO = transportValuableService.getTransportValuableBySrcMsgId(srcMsgId);
        if (null == transportValuableDO || transportValuableDO.getProcessStatus() == 3){
            return;
        }
        transportValuableService.finishTransportValuable(srcMsgId);
    }
}
