package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户端标识枚举类
 *
 * <AUTHOR>
 * @since 2024/05/29 13:45
 */
@Getter
@AllArgsConstructor
public enum ClientSignEnum {
    PC(1),
    ANDROID_CAR(21),
    ANDROID_GOODS(22),
    IOS_CAR(31),
    IOS_GOODS(32),
    ;

    private final Integer code;

    /**
     * 判断是否车主版
     * @param code
     * @return
     */
    public static boolean isCar(int code){
        return ANDROID_CAR.code == code || IOS_CAR.code == code;
    }

    /**
     * 判断是否货主版
     * @param code
     * @return
     */
    public static boolean isGoods(int code){
        return ANDROID_GOODS.code == code || IOS_GOODS.code == code;
    }

    /**
     * 是否是有效的客户端标识
     */
    public static boolean isValid(Integer code) {
        for (ClientSignEnum value : values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是货APP
     */
    public static boolean isGoodsApp(Integer code) {
        return IOS_GOODS.getCode().equals(code) || ANDROID_GOODS.getCode().equals(code);
    }

    /**
     * 是否是车APP
     */
    public static boolean isCarApp(Integer code) {
        return IOS_CAR.getCode().equals(code) || ANDROID_CAR.getCode().equals(code);
    }
}