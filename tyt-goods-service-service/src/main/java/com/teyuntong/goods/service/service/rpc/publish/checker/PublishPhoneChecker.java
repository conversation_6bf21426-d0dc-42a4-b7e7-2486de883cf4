package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.user.UserTelRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserTelVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 运满满货源校验
 *
 * <AUTHOR>
 * @since 2025/02/21 13:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublishPhoneChecker {

    private final UserTelRemoteService userTelRemoteService;

    /**
     * 手机号校验
     *
     * @param publishBO
     * @param user
     */
    public void checkPhone(PublishBO publishBO, UserRpcVO user) {
        //添加手机号检验
        String registerPhone = user.getCellPhone();
        String tel = publishBO.getTel();
        if (StringUtils.isNotBlank(tel)) {
            if (!checkPhone(tel, user.getId(), registerPhone)) {
                throw BusinessException.createException(GoodsErrorCode.ERROR_PHONE_LACK.getCode(), "电话号码 " + tel + " 已不存在，请重新选择");
            }
        }

        String tel3 = publishBO.getTel3();
        if (StringUtils.isNotBlank(tel3)) {
            if (!checkPhone(tel3, user.getId(), registerPhone)) {
                throw BusinessException.createException(GoodsErrorCode.ERROR_PHONE_LACK.getCode(), "电话号码 " + tel3 + " 已不存在，请重新选择");

            }
        }
        String tel4 = publishBO.getTel4();
        if (StringUtils.isNotBlank(tel4)) {
            if (!checkPhone(tel4, user.getId(), registerPhone)) {
                throw BusinessException.createException(GoodsErrorCode.ERROR_PHONE_LACK.getCode(), "电话号码 " + tel4 + " 已不存在，请重新选择");
            }
        }

    }


    private boolean checkPhone(String tel, Long userId, String registerPhone) {
        boolean isPhone = false;
        if (StringUtils.isNotBlank(tel)) {
            if (tel.equals(registerPhone)) {
                isPhone = true;
            } else {
                List<UserTelVO> userTelVOList = userTelRemoteService.getTelsById(userId, null);
                if (CollUtil.isNotEmpty(userTelVOList)) {
                    List<String> telList = userTelVOList.stream().filter(userTel -> Objects.equals(userTel.getStatus(), YesOrNoEnum.YES.getId().toString())).map(UserTelVO::getTel).toList();
                    if (CollectionUtils.isNotEmpty(telList) && telList.contains(tel)) {
                        isPhone = true;
                    }
                }
            }
        }
        return isPhone;

    }


}
