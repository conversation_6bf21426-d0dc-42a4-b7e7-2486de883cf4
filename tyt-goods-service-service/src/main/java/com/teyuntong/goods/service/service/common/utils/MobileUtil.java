package com.teyuntong.goods.service.service.common.utils;

import java.util.regex.Pattern;

public class MobileUtil {

	/**
     * 验证手机号码
	 * 校验规则：以1开头的11位数字
     * @return 验证成功返回true，验证失败返回false
     */
    public static boolean isMobile(String mobile){
		if(mobile == null){
			return false;
		}

        String regex = "^1[0-9]{10}$";
        return Pattern.matches(regex, mobile);
    }

    /**
     * 区号+座机号码
     * @param fixedPhone
     * @return
     */
    public static boolean isFixedPhone(String fixedPhone){
        String reg="(?:(0[0-9]{2,3}?)?([2-9][0-9]{6,7})?)";
        return Pattern.matches(reg, fixedPhone.trim());
    }

}

