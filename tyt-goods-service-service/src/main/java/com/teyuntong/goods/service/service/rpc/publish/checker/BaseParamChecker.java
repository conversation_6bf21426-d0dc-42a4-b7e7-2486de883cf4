package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.enums.ClientSignEnum;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/07/07 14:32
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BaseParamChecker {

    public void checkClientSign(BaseParamDTO baseParam, TransportMainDO oldMainDO, TransportMainExtendDO oldMainExtend) {
        if (oldMainDO == null || oldMainExtend == null){
            return;
        }
        if (!Objects.equals(baseParam.getClientSign(), ClientSignEnum.PC.getCode())){
            return;
        }
        if (null == oldMainDO.getPublishGoodsType() || null == oldMainExtend.getClientFusion()){
            return;
        }
        if (oldMainExtend.getClientFusion() == 1 && (oldMainDO.getPublishGoodsType() == PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode() || PublishGoodsTypeEnum.isExcellentGoods(oldMainDO.getPublishGoodsType()))){
            throw new BusinessException(GoodsErrorCode.PLAT_LIMIT);
        }

    }

}
