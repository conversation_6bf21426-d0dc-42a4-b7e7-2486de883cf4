package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.enums.PublishStyleEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.NoticePopupTemplRemoteService;
import com.teyuntong.goods.service.service.remote.market.CouponRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.market.activity.client.coupon.vo.VipNoticeForGoodsVo;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.TRANSMISSION_AB_TEST_KEY;

/**
 * 校验小程序发货有效性
 *
 * <AUTHOR>
 * @since 2025/02/21 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublishPermissionChecker {

    private final UserPermissionRemoteService userPermissionRemoteService;
    private final CouponRemoteService couponRemoteService;
    private final NoticePopupTemplRemoteService noticePopupTemplRemoteService;
    private final ABTestRemoteService abTestRemoteService;


    /**
     * 校验用户发货权益并进行扣除
     *
     * @param processBO
     * @param deduction 是否扣除权益
     */
    public void checkPublishPermission(PublishProcessBO processBO, boolean deduction) {
        PublishBO publishBO = processBO.getPublishBO();
        UserRpcVO user = processBO.getUser();
        TransportMainDO transportMain = processBO.getTransportMain();

        // 当日货源编辑发布不校验权益
        if (Objects.equals(publishBO.getPublishStyle(), PublishStyleEnum.TODAY_PUBLISH.getCode())) {
            return;
        }
        // 新版优车货源不校验发货权益
        // if (processBO.getBaseParam().getClientFusion() != null) {
        //     return;
        // }
        // 专车和优车发货卡不校验发货权益
        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())
                || Objects.equals(publishBO.getPublishGoodsType(), PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())
                || publishBO.getExcellentCardId() != null) {
            return;
        }
        // 开票指派单货源不校验发货权益
        if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId()) && StringUtils.isNotBlank(publishBO.getAssignCarTel())) {
            return;
        }
        Long srcMsgId = null;
        if (deduction) {
            srcMsgId = transportMain != null ? transportMain.getSrcMsgId() : null;
        }
        AuthPermissionRpcVO authPermissionRpcVO = authPermission(publishBO.getPrice(), srcMsgId, user.getId(), deduction);

        processBO.setAuthPermissionRpcVO(authPermissionRpcVO);

    }


    /**
     * 校验用户发货权益并进行扣除
     *
     * @param processBO
     * @param deduction 是否扣除权益
     */
    public void checkDirectPublishPermission(DirectPublishProcessBO processBO, boolean deduction) {
        UserRpcVO user = processBO.getUser();
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        TransportMainDO oldMain = processBO.getOldMain();
        TransportMainDO transportMain = processBO.getTransportMain();
        // 当日货源编辑发布不校验权益
        if (!directPublishBO.isHistoryGoods()) {
            return;
        }
        // 新版融合优车发货不校验权益
        if (processBO.getBaseParam().getClientFusion() != null && PublishGoodsTypeEnum.isExcellentGoods(directPublishBO.getPublishGoodsTypeEnum().getCode())) {
            log.info("新版优车发货不校验发货权益，srcMsgId:{}，publishGoodsType:{}", oldMain.getSrcMsgId(), directPublishBO.getPublishGoodsTypeEnum().getCode());
            return;
        }
        // 专车和优车发货卡不校验发货权益
        if (Objects.equals(oldMain.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) || directPublishBO.getExcellCardId() != null) {
            return;
        }
        // 自动重发不扣发货权益，重新上架不扣除权益
        if (PublishOptEnum.AUTO_RESEND.equals(processBO.getOptEnum()) || PublishOptEnum.RERELEASE.equals(processBO.getOptEnum())) {
            return;
        }
        Long srcMsgId = transportMain != null ? transportMain.getSrcMsgId() : null;
        AuthPermissionRpcVO authPermissionRpcVO = authPermission(oldMain.getPrice(), srcMsgId, user.getId(), deduction);

        processBO.setAuthPermissionRpcVO(authPermissionRpcVO);

    }

    /**
     * 校验并扣减发货权益
     *
     * @param price
     * @param userId
     * @param price
     * @param srcMsgId
     * @param userId
     * @param deduction 是否扣除权益
     * @return
     */
    private AuthPermissionRpcVO authPermission(String price, Long srcMsgId, Long userId, boolean deduction) {

        AuthPermissionRpcDTO permissionRpcDTO = new AuthPermissionRpcDTO();
        if (StringUtils.isNotBlank(price)) {
            permissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.有价货源发布);
        } else {
            permissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源发布);
        }
        permissionRpcDTO.setUserId(userId);
        permissionRpcDTO.setSrcMsgId(srcMsgId);
        AuthPermissionRpcVO authPermissionRpcVO = new AuthPermissionRpcVO();
        if (deduction) {
            authPermissionRpcVO = userPermissionRemoteService.authPermission(permissionRpcDTO);
        } else {
            authPermissionRpcVO = userPermissionRemoteService.checkAuthPermission(permissionRpcDTO);
        }
        if (!authPermissionRpcVO.isUse()) {
            VipNoticeForGoodsVo noticeVo = null;
            try {
                Integer userType = abTestRemoteService.getUserType(TRANSMISSION_AB_TEST_KEY, userId);
                if (Objects.equals(userType, YesOrNoEnum.YES.getId())) {
                    noticeVo = couponRemoteService.getVipNoticeForGoodsPublish(userId);
                }
            } catch (Exception ex) {
                log.error("发货调用market-service失败,userId:{}", userId);
            }
            if (noticeVo != null) {
                throw new BusinessException(GoodsErrorCode.PUBLISH_NO_PERMISSION_NOTICE, noticeVo);
            } else {
                PopupTypeEnum popupTypeEnum = PopupTypeEnum.getByName(authPermissionRpcVO.getPermissionPopupTypeEnum().name());
                NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(popupTypeEnum);
                throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
            }
        }
        return authPermissionRpcVO;
    }


}
