package com.teyuntong.goods.service.service.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/07/19 11:08
 */
public enum CarHasLadderEnum {

    LADDER_ONE(1, "带"),
    LADDER_TWO(2, "未知"),
    LADDER_THREE(0, "不带");



    private final Integer key;
    private final String value;

    CarHasLadderEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    // 根据key获取value的方法
    public static String getValueByKey(Integer key) {
        for (CarHasLadderEnum pair : CarHasLadderEnum.values()) {
            if (pair.getKey().equals(key)) {
                return pair.getValue();
            }
        }
        return null; // 如果找不到key，返回null
    }
}
