package com.teyuntong.goods.service.service.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class ApplicationContextUtils implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtils.applicationContext = applicationContext;
        synchronized (ApplicationContextUtils.class) {  
        	ApplicationContextUtils.class.notifyAll();  
        }  
    }

    public static ApplicationContext getApplicationContext() {
        return ApplicationContextUtils.applicationContext;
    }

    public static DefaultListableBeanFactory getDefaultListableBeanFactory() {
        ConfigurableApplicationContext configurableApplicationContext = (ConfigurableApplicationContext) applicationContext;
        return (DefaultListableBeanFactory) configurableApplicationContext.getBeanFactory();
    }

    public static boolean containsBean(String className) {
    	checkForInitialized();
        return applicationContext.containsBean(className);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String beanName) {
    	checkForInitialized();
        return (T) applicationContext.getBean(beanName);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static <T> T getBean(Class clazz) {
    	checkForInitialized();
        return (T) applicationContext.getBean(clazz);
    }
    
    private static void checkForInitialized() {  
        if (applicationContext == null) {  
            synchronized (ApplicationContextUtils.class) {  
                if (applicationContext == null) {  
                    try {  
                    	ApplicationContextUtils.class.wait();  
                    } catch (InterruptedException e) {  
                        Thread.currentThread().interrupt();  
                    }  
                }  
            }  
        }  
    }  
  
}
