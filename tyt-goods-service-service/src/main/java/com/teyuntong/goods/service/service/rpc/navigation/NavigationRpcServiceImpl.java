package com.teyuntong.goods.service.service.rpc.navigation;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.navigation.dto.NavigationQueryDTO;
import com.teyuntong.goods.service.client.navigation.dto.NavigationTruckDTO;
import com.teyuntong.goods.service.client.navigation.dto.TruckNavigationInfoDTO;
import com.teyuntong.goods.service.client.navigation.service.NavigationRpcService;
import com.teyuntong.goods.service.client.navigation.vo.NavigationResultVO;
import com.teyuntong.goods.service.client.navigation.vo.NavigationTruckInfoVO;
import com.teyuntong.goods.service.service.biz.navigation.service.TruckNavigationInfoService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.TruckModelEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.outer.DistanceRemoteService;
import com.teyuntong.goods.service.service.rpc.navigation.converter.NavigationConverter;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.outer.export.service.client.distance.dto.DistanceRpcDTO;
import com.teyuntong.outer.export.service.client.distance.vo.DistanceRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 货车导航服务实现类
 *
 * <AUTHOR>
 * @since 2025/07/24 10:45
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class NavigationRpcServiceImpl implements NavigationRpcService {

    private final DistanceRemoteService distanceRemoteService;
    private final TytConfigRemoteService configRemoteService;
    private final TruckNavigationInfoService truckNavigationInfoService;

    /**
     * 查询货车导航距离
     */
    @Override
    public NavigationResultVO navigationDistance(NavigationQueryDTO navQueryDTO) {
        if (navQueryDTO.getStartLatitude() == null || navQueryDTO.getStartLongitude() == null
                || navQueryDTO.getDestLatitude() == null || navQueryDTO.getDestLongitude() == null) {
            throw new BusinessException(GoodsErrorCode.COORDINATE_NULL);
        }

        // 调用三方货车导航
        DistanceRpcDTO distanceRpcDTO = buildDistanceRpcDTO(navQueryDTO);
        log.info("调用三方货车导航，入参：{}", JSON.toJSONString(distanceRpcDTO));
        DistanceRpcVO distanceRpcVO = distanceRemoteService.calculateDistance(distanceRpcDTO);
        log.info("调用三方货车导航，出参：{}", JSON.toJSONString(distanceRpcVO));
        if (distanceRpcVO == null || distanceRpcVO.getDistance() == null) {
            throw new BusinessException(GoodsErrorCode.NAVIGATION_ERROR);
        }

        // 返回导航结果
        NavigationResultVO navResultVO = new NavigationResultVO();
        BigDecimal kilometer = new BigDecimal(distanceRpcVO.getDistance())
                .divide(new BigDecimal(1000), 0, RoundingMode.HALF_UP);
        navResultVO.setDistance(kilometer);
        return navResultVO;
    }

    private DistanceRpcDTO buildDistanceRpcDTO(NavigationQueryDTO navQueryDTO) {
        // 设置货车信息
        DistanceRpcDTO distanceRpcDTO = new DistanceRpcDTO();
        // 货车导航选用类型 1高德；2腾讯
        distanceRpcDTO.setTruckNavigationType(configRemoteService.getIntValue("truck_navigation_type", 1));
        // 出发地目的地经纬度
        distanceRpcDTO.setFromLatitude(navQueryDTO.getStartLatitude().toString());
        distanceRpcDTO.setFromLongitude(navQueryDTO.getStartLongitude().toString());
        distanceRpcDTO.setToLatitude(navQueryDTO.getDestLatitude().toString());
        distanceRpcDTO.setToLongitude(navQueryDTO.getDestLongitude().toString());

        // 专车运距按单独的车货配置，条件：专车 & 10~32吨 工单：SSRS-3840
        if (ExcellentGoodsEnums.isSpecial(navQueryDTO.getExcellentGoods())
                && navQueryDTO.getWeight() != null
                && navQueryDTO.getWeight().compareTo(new BigDecimal("10")) >= 0
                && navQueryDTO.getWeight().compareTo(new BigDecimal("32")) <= 0) {
            // 专车货车配置，按总重，长，宽，高，轴数，车牌号
            String specialCarTruck = configRemoteService.getStringValue("truck_navigation_special_car_truck", "39,13,3,3.2,6,冀PN9651");
            String[] split = specialCarTruck.split(",");
            distanceRpcDTO.setWeight(new BigDecimal(split[0]));
            distanceRpcDTO.setLoad(navQueryDTO.getWeight());
            distanceRpcDTO.setLength(new BigDecimal(split[1]));
            distanceRpcDTO.setWidth(new BigDecimal(split[2]));
            distanceRpcDTO.setHeight(new BigDecimal(split[3]));
            distanceRpcDTO.setAxleCount(Integer.parseInt(split[4]));
            distanceRpcDTO.setPlateNumber(split[5]);
        } else {
            TruckModelEnum truckModelEnum = TruckModelEnum.getByWeight(navQueryDTO.getWeight());
            if (truckModelEnum != null) {
                distanceRpcDTO.setWeight(new BigDecimal(truckModelEnum.getWeight()));
                distanceRpcDTO.setLoad(new BigDecimal(truckModelEnum.getLoad()));
                distanceRpcDTO.setLength(new BigDecimal(truckModelEnum.getLength()));
                distanceRpcDTO.setWidth(new BigDecimal(truckModelEnum.getWidth()));
                distanceRpcDTO.setHeight(new BigDecimal(truckModelEnum.getHeight()));
                distanceRpcDTO.setAxleCount(truckModelEnum.getAxleCount());
                distanceRpcDTO.setPlateNumber(truckModelEnum.getPlateNumber());
            } else {
                // 设置默认车型
                distanceRpcDTO.setWeight(new BigDecimal("20"));
                distanceRpcDTO.setLoad(new BigDecimal("12"));
                distanceRpcDTO.setLength(new BigDecimal("18"));
                distanceRpcDTO.setWidth(new BigDecimal("3"));
                distanceRpcDTO.setHeight(new BigDecimal("4"));
                distanceRpcDTO.setAxleCount(6);
                distanceRpcDTO.setPlateNumber("冀PN9651");
            }
        }

        return distanceRpcDTO;
    }

    /**
     * 设置导航的货车信息
     */
    @Override
    public NavigationTruckInfoVO navigationTruckInfo(BigDecimal weight) {
        NavigationTruckInfoVO truckInfoVO = new NavigationTruckInfoVO();
        TruckModelEnum truckModelEnum = TruckModelEnum.getByWeight(weight);
        if (truckModelEnum != null) {
            truckInfoVO.setWeight(new BigDecimal(truckModelEnum.getWeight()));
            truckInfoVO.setLoad(new BigDecimal(truckModelEnum.getLoad()));
            truckInfoVO.setLength(new BigDecimal(truckModelEnum.getLength()));
            truckInfoVO.setWidth(new BigDecimal(truckModelEnum.getWidth()));
            truckInfoVO.setHeight(new BigDecimal(truckModelEnum.getHeight()));
            truckInfoVO.setAxleCount(truckModelEnum.getAxleCount());
            truckInfoVO.setPlateNumber(truckModelEnum.getPlateNumber());
        } else {
            // 设置默认车型
            truckInfoVO.setWeight(new BigDecimal("20"));
            truckInfoVO.setLoad(new BigDecimal("12"));
            truckInfoVO.setLength(new BigDecimal("18"));
            truckInfoVO.setWidth(new BigDecimal("3"));
            truckInfoVO.setHeight(new BigDecimal("4"));
            truckInfoVO.setAxleCount(6);
            truckInfoVO.setPlateNumber("冀PN9651");
        }
        return truckInfoVO;
    }

    /**
     * 查询货车导航
     */
    @Override
    public String navigationTruck(NavigationTruckDTO navigationTruckDTO) {
        DistanceRpcDTO distanceRpcDTO = NavigationConverter.INSTANCE.to(navigationTruckDTO);
        return distanceRemoteService.navigationTruck(distanceRpcDTO);
    }

    /**
     * 导航车辆信息上报
     */
    @Override
    public Boolean reportNavigationTruck(TruckNavigationInfoDTO truckInfoDTO) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        truckInfoDTO.setUserId(userId);
        truckNavigationInfoService.saveOrUpdate(truckInfoDTO);
        return true;
    }

    /**
     * 导航车辆信息删除
     */
    @Override
    public Boolean deleteNavigationTruck(Long userId, Integer navigateType) {
        truckNavigationInfoService.logicalDelete(userId, navigateType);
        return true;
    }

    /**
     * 获取导航车辆信息
     */
    @Override
    public List<TruckNavigationInfoDTO> getNavigationTruck(Long userId, Integer navigateType) {
        return truckNavigationInfoService.getNavigationTruck(userId, navigateType);
    }
}
