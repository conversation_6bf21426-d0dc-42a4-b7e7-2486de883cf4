package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 校验价格是否合理
 *
 * <AUTHOR>
 * @since 2025/03/09 16:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AllowPriceChecker {

    private final ThPriceService thPriceService;

    public void checkDirectPublish(DirectPublishProcessBO processBO) {
        // 加价、填价、直接发布（带价格）、转一口价(带价格)时校验价格是否合理
        if (PublishOptEnum.ADD_PRICE.equals(processBO.getOptEnum())
                || PublishOptEnum.FILL_PRICE.equals(processBO.getOptEnum())
                || PublishOptEnum.DIRECT.equals(processBO.getOptEnum()) && TransportUtil.hasPrice(processBO.getDirectPublishBO().getPrice())
                || PublishOptEnum.TRANSFER_FIXED.equals(processBO.getOptEnum()) && TransportUtil.hasPrice(processBO.getDirectPublishBO().getPrice())) {
            // 校验价格是否允许
            PublishBO publishBO = new PublishBO();
            BeanUtils.copyProperties(processBO.getOldMain(), publishBO);
            // bug fix：价格是最新提交的价格
            publishBO.setPrice(processBO.getDirectPublishBO().getPrice());
            checkPublishPrice(publishBO, processBO.getUser().getId());
        }
    }

    /**
     * 校验价格是否允许 .
     */
    public void checkPublishPrice(PublishBO publishBO, Long userId) {
        // 专车货源不校验运费
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBO.getExcellentGoods())) {
            return;
        }
        // 专票货源指派司机不校验运费
        if (publishBO.getInvoiceSubjectId() != null && StringUtils.isNotBlank(publishBO.getAssignCarTel())) {
            return;
        }
        boolean havePrice = StringUtils.isNotBlank(publishBO.getPrice());
        boolean havePriceCap = Objects.nonNull(publishBO.getPriceCap()) && publishBO.getPriceCap() > 0;
        // 无价货源不校验运费
        if (!havePrice && !havePriceCap) {
            return;
        }

        int thMinPrice = 100;
        int thMaxPrice = 999999;
        TransportCarryReq carryReq = buildCarryPrice(publishBO, userId);
        CarryPriceVO carryPriceVo = thPriceService.getThPrice(carryReq);
        if (carryPriceVo != null) {
            thMinPrice = carryPriceVo.getThMinPrice() != null ? carryPriceVo.getThMinPrice() : thMinPrice;
            thMaxPrice = carryPriceVo.getThMaxPrice() != null ? carryPriceVo.getThMaxPrice() : thMaxPrice;
            publishBO.setThMinPrice(thMinPrice);
            publishBO.setThMaxPrice(thMaxPrice);
            if (carryPriceVo.getSuggestMinPrice() != null && carryPriceVo.getSuggestMaxPrice() != null) {
                publishBO.setSuggestMaxPrice(carryPriceVo.getSuggestMaxPrice().intValue());
                publishBO.setSuggestMinPrice(carryPriceVo.getSuggestMinPrice().intValue());
            }
        }
        if (havePrice) {
            int priceInt = Integer.parseInt(publishBO.getPrice());
            if (priceInt < thMinPrice) {
                throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "该路线最低价" + thMinPrice + "元，请提高价格");
            }
            if (priceInt > thMaxPrice) {
                throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "您当前运费过高，可能存在填写错误，请重新填价");
            }
        }

        if (havePriceCap) {
            // 回价助手运费上限校验
            Integer priceCap = publishBO.getPriceCap();
            if (Objects.nonNull(priceCap) && priceCap > 0) {
                if (priceCap < thMinPrice) {
                    throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "运费上限需大于" + thMinPrice + "元");
                }
                if (priceCap > thMaxPrice) {
                    throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "回价助手运费上限过高，请重新填写");
                }
            }
        }
    }

    private TransportCarryReq buildCarryPrice(PublishBO publishBO, Long userId) {
        TransportCarryReq carryReq = new TransportCarryReq();
        BeanUtils.copyProperties(publishBO, carryReq);
        carryReq.setUserId(userId);
        carryReq.setStartProvince(publishBO.getStartProvinc());
        carryReq.setStartCity(publishBO.getStartCity());
        carryReq.setStartArea(publishBO.getStartArea());
        carryReq.setDestProvince(publishBO.getDestProvinc());
        carryReq.setDestCity(publishBO.getDestCity());
        carryReq.setDestArea(publishBO.getDestArea());
        carryReq.setDistance(publishBO.getDistance());
        carryReq.setGoodsWeight(new BigDecimal(publishBO.getWeight()));
        carryReq.setGoodsLength(publishBO.getLength());
        carryReq.setGoodsWide(publishBO.getWide());
        carryReq.setGoodsHigh(publishBO.getHigh());
        carryReq.setDistance(publishBO.getDistance());
        carryReq.setGoodTypeName(publishBO.getGoodTypeName());

        return carryReq;
    }
}
