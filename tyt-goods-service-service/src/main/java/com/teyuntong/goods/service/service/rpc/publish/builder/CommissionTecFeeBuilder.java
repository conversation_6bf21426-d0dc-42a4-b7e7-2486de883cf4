package com.teyuntong.goods.service.service.rpc.publish.builder;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.enums.ClientSignEnum;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.commission.CalcCommissionTecFeeService;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 抽佣技术费计算builder
 *
 * <AUTHOR>
 * @since 2025/02/21 16:15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommissionTecFeeBuilder {

    private final CalcCommissionTecFeeService calcCommissionTecFeeService;

    /**
     * 抽佣货源计算技术服务费
     */
    public void build(DirectPublishProcessBO processBO) {
        // 如果不抽佣，直接返回
        TransportMainDO transportMain = processBO.getTransportMain();
        if (!isNeedCalcCommission(processBO)) {
            // 如果技术服务费>0，算抽佣
            if (transportMain.getTecServiceFee() != null && transportMain.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                processBO.setCommissionTransport(true);
                processBO.setConfigToComputeResult(getManualTecServiceFeeResult(transportMain, processBO.getMainExtend()));
            }
            return;
        }

        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        //只有填价加价转一口价或手动直接发布的时候需要重新计算抽佣货源的技术服务费
        if (directPublishBO.isRecalculateTecServiceFee()) {
            TecServiceFeeConfigComputeResult tecServiceFeeResult = calcCommissionTecFeeService
                    .makeTecServiceFeeData(processBO, transportMain.getExcellentGoodsTwo() == 2);
            processBO.setCommissionTransport(tecServiceFeeResult != null);
            processBO.setConfigToComputeResult(tecServiceFeeResult);
        }

    }

    /**
     * 判断是否需要计算抽佣货源的技术服务费
     * 如果APP版本号>=6670 且是 代调账号发的专车非平台，使用客户端传的技术服务费，不计算技术服务费，
     * 后面填价、加价、直接发布均不重新计算佣金
     *
     * @return false不需要计算技术服务费，直接使用客户端传的
     * @since 6670
     */
    public boolean isNeedCalcCommission(BasePublishProcessBO processBO) {
        PublishOptEnum optEnum = processBO.getOptEnum();
        // 只有首发、编辑发布判断
        if (PublishOptEnum.PUBLISH == optEnum || PublishOptEnum.EDIT == optEnum) {
            BaseParamDTO baseParam = processBO.getBaseParam();
            TransportMainDO transportMain = processBO.getTransportMain();
            if (ClientSignEnum.isGoodsApp(baseParam.getClientSign()) // APP端
                    && baseParam.getClientVersion() != null && Integer.parseInt(baseParam.getClientVersion()) >= 6670 // 版本号>=6670
                    && SourceTypeEnum.isDispatch(transportMain.getSourceType())) {
                return false;
            }
        } else {
            // 其他操作不改变技术服务费
            TransportMainDO oldMain = processBO.getOldMain();
            TransportLabelJson labelJson = JSON.parseObject(oldMain.getLabelJson(), TransportLabelJson.class);
            if (labelJson != null && Objects.equals(labelJson.getIsManualTecServiceFee(), 1)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 返回手动设置技术服务费的抽佣结果
     */
    public TecServiceFeeConfigComputeResult getManualTecServiceFeeResult(TransportMainDO transportMain, TransportMainExtendDO mainExtendDO) {
        TecServiceFeeConfigComputeResult tecServiceFeeConfigComputeResult = new TecServiceFeeConfigComputeResult();
        tecServiceFeeConfigComputeResult.setCarMemberType(0);
        tecServiceFeeConfigComputeResult.setPrivacyPhoneType(1);
        tecServiceFeeConfigComputeResult.setFreeTecServiceFeeType(0); // 不超时免佣
        tecServiceFeeConfigComputeResult.setFreeTecServiceFeeTime(0); // 超时免佣时间数据库是非空的
        tecServiceFeeConfigComputeResult.setTecServiceFee(transportMain.getTecServiceFee());
        tecServiceFeeConfigComputeResult.setUseCommissionScoreStageConfig(true);
        tecServiceFeeConfigComputeResult.setCommissionScore(mainExtendDO.getCommissionScore());
        tecServiceFeeConfigComputeResult.setTecServiceFeeBeforeDiscount(transportMain.getTecServiceFee());
        tecServiceFeeConfigComputeResult.setApplyTransportType(1); // 目前只有专车
        tecServiceFeeConfigComputeResult.setRefundFlagType(transportMain.getRefundFlag());
        tecServiceFeeConfigComputeResult.setPricePublishType(3);
        return tecServiceFeeConfigComputeResult;
    }
}
