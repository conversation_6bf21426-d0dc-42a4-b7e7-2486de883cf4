package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 货源诊断任务（加价、填价、转一口价、补全货物信息、曝光）完成时，重置倒计时
 *
 * <AUTHOR>
 * @since 2025/07/17 09:51
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DiagnosisTaskCountDownPostHandler {

    private final StringRedisTemplate stringRedisTemplate;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        if (PublishOptEnum.isLiveOpt(processBO.getOptEnum())) {
            Long srcMsgId = processBO.getTransportMain().getSrcMsgId();
            long time = System.currentTimeMillis();
            stringRedisTemplate.opsForValue().set(RedisKeyConstant.DIAGNOSIS_TASK_COMPLETE_TIME_KEY + srcMsgId,
                    String.valueOf(time), Duration.ofHours(24));
        }
    }
}
