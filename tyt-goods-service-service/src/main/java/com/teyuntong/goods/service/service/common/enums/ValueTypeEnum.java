package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;

/**
 * 价值货源类型：1-新客首单货源(废弃)，2-优车2.0货源，3-新注册用户首月货源，4-历史用户首履货源
 *
 * <AUTHOR>
 * @since 2024-08-30 18:39
 */
@Getter
public enum ValueTypeEnum {
    NEW_CUSTOMER_FIRST(1, "新客首单"),
    EXCELLENT_2_0(2, "优车2.0货源"),
    NEW_CUSTOMER_FIRST_MONTH(3, "新注册用户首月货源"),
    OLD_CUSTOMER_FIRST_DEAL(4, "历史用户首履货源"),
    GOOD_MEDIUM_POOR(5, "好中货")
    ;
    private Integer code;
    private String name;
    ValueTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
