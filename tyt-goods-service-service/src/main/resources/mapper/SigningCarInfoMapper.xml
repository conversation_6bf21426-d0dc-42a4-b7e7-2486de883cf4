<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SigningCarInfoMapper">

    <select id="getCountBySigningIdAndPhone" resultType="java.lang.Integer">
        select count(*)
        from tyt_signing_car_info
        where signing_id = #{signingId}
          and phone = #{phone}
    </select>

    <update id="updateAssignNum">
        update tyt_signing_car_info
        set assign_success_num = assign_success_num + 1,
            update_time        = now()
        where signing_id = #{signingId}
          and phone = #{phone}
    </update>

    <select id="getByIdName" resultType="java.lang.Integer">
        select count(*) from tyt_signing_car_info where signing_id = #{id}  and phone = #{phone}
    </select>

    <select id="getByCarIdAndDriverId" resultType="java.lang.Long">
        select id
        from tyt_signing_car_info
        where signing_id = #{signingId}
          and driver_user_id = #{driverId} limit 1
    </select>

    <select id="getAutoCityById" resultType="string">
        select auto_city
        from tyt_signing_car_info
        where id = #{signingCarInfoId}
    </select>

    <select id="getBICityById" resultType="string">
        select bi_city
        from tyt_signing_car_info
        where id = #{signingCarInfoId}
    </select>

    <select id="checkCityExist" resultType="java.lang.Integer">
        select count(*)
        from tyt_signing_car_info_city
        where car_info_id = #{signingCarInfoId}
          and city = #{city}
        and type = 2
        and auto_type = #{autoType}
    </select>

    <insert id="addCity">
        insert into tyt_signing_car_info_city (car_info_id, type, auto_type, city, create_time, modify_time) VALUE (
            #{signingCarInfoId}, 2, #{autoType}, #{city}, now(), now()
            )
    </insert>

    <select id="getByDriverUserId" resultType="long">
        select id
        from tyt_signing_car_info
        where driver_user_id = #{driverUserId}
    </select>

    <delete id="deleteCity">
        delete from tyt_signing_car_info_city where car_info_id = #{signingCarInfoId} and type = 2 and auto_type = #{autoType}
        <if test="city != null and city != ''">
            and city = #{city}
        </if>
    </delete>

    <delete id="deleteCityById">
        delete from tyt_signing_car_info_city where id = #{signingCarInfoCityId}
    </delete>

    <delete id="deleteAllAutoAndBICity">
        delete from tyt_signing_car_info_city where auto_type != 0
    </delete>

    <update id="updateAutoCityById">
        update tyt_signing_car_info set auto_city = #{autoCity}
        where id = #{signingCarInfoId}
    </update>

    <update id="updateBICityById">
        update tyt_signing_car_info set bi_city = #{biCity}
        where id = #{signingCarInfoId}
    </update>

    <delete id="cleanAllAutoAndBICity">
        update tyt_signing_car_info set auto_city = '', bi_city = '' where 1 = 1
    </delete>

    <select id="getCityLimit1" resultType="java.lang.Long">
        select id from tyt_signing_car_info_city where car_info_id = #{signingCarInfoId} and type = 2 and auto_type = #{autoType}
        <if test="city != null and city != ''">
            and city = #{city}
        </if> limit 1
    </select>

</mapper>
