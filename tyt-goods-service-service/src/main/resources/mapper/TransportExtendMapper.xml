<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportExtendMapper">
    <update id="updateTransportExtend">
        UPDATE tyt_transport_extend
        SET src_msg_id                = #{srcMsgId},
            ts_id                     = #{tsId},
            use_car_type              = #{useCarType},
            price_type                = #{priceType},
            suggest_min_price         = #{suggestMinPrice},
            suggest_max_price         = #{suggestMaxPrice},
            fix_price_fast            = #{fixPriceFast},
            cost_price                = #{costPrice},
            create_time               = #{createTime},
            modify_time               = #{modifyTime},
            good_model_level          = #{goodModelLevel},
            good_model_score          = #{goodModelScore},
            price_cap                 = #{priceCap},
            lim_good_model_level      = #{limGoodModelLevel},
            lim_good_model_score      = #{limGoodModelScore},
            commission_score          = #{commissionScore},
            top_flag                  = #{topFlag},
            seckill_goods             = #{seckillGoods},
            perk_price                = #{perkPrice},
            good_transport_label      = #{goodTransportLabel},
            good_transport_label_part = #{goodTransportLabelPart},
            client_fusion             = #{clientFusion}
        WHERE id = #{id}
    </update>

    <select id="getByTsId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportExtendDO">
        select *
        from tyt_transport_extend
        where ts_id = #{tsId}
    </select>
</mapper>