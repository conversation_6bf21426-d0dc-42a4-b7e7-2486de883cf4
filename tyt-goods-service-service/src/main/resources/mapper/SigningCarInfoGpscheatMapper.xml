<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SigningCarInfoGpscheatMapper">

    <insert id="saveGpscheat">
        insert into tyt_signing_car_info_gpscheat (
            car_info_id, coord_x, coord_y, detail_add, longitude, latitude, 
            provinc, city, area, create_time, modify_time
        ) values (
            #{carInfoId}, #{coordX}, #{coordY}, #{detailAdd}, #{longitude}, #{latitude},
            #{provinc}, #{city}, #{area}, now(), now()
        )
    </insert>

    <delete id="deleteGpscheat">
        delete from tyt_signing_car_info_gpscheat where car_info_id = #{carInfoId} and coord_x = #{coordX} and coord_y = #{coordY} and detail_add = #{detailAdd}
                                                    and longitude = #{longitude} and latitude = #{latitude} and provinc = #{provinc} and city = #{city} and area = #{area}
    </delete>

    <delete id="deleteGpscheatById">
        delete from tyt_signing_car_info_gpscheat where id = #{id}
    </delete>

    <select id="getGpscheatLimit1" resultType="long">
        select id from tyt_signing_car_info_gpscheat where car_info_id = #{carInfoId} and coord_x = #{coordX} and coord_y = #{coordY} and detail_add = #{detailAdd}
                                                    and longitude = #{longitude} and latitude = #{latitude} and provinc = #{provinc} and city = #{city} and area = #{area} limit 1
    </select>

    <select id="getGpscheatByCarInfoId" resultType="com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarInfoGpscheatDO">
        select * from tyt_signing_car_info_gpscheat where car_info_id = #{carInfoId}
    </select>

    <delete id="deleteAllGpscheat">
        delete from tyt_signing_car_info_gpscheat where 1 = 1
    </delete>
</mapper>
