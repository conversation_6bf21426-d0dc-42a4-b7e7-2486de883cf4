<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarBiRouteMapper">

	<select id="countByRoute" resultType="java.lang.Integer">
		select count(*)
		from tyt_special_car_bi_route
		where del_status = 0
		and start_city = #{startCity} and dest_city = #{destCity}
	</select>
</mapper>
