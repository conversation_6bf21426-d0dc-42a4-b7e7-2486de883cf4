<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMappingMapper">

    <!-- 获取关联货源 -->
    <select id="getRelatedList" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMappingDO">
        select * from tyt_transport_mapping
        where old_src_msg_id in ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
        <if test="publishOpt != null and publishOpt != ''">
            and publish_opt = #{publishOpt}
        </if>
        union all
        select * from tyt_transport_mapping
        where new_src_msg_id in ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
        <if test="publishOpt != null and publishOpt != ''">
            and publish_opt = #{publishOpt}
        </if>
    </select>
</mapper>
