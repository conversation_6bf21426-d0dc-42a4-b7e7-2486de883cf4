<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportValuableMapper">

    <select id="selectBySrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportValuableDO">
        select *
        from tyt_transport_valuable
        where src_msg_id = #{srcMsgId}
            limit 1
    </select>

    <update id="finishTransportValuable">
        update tyt_transport_valuable
        set process_status = 3, modify_time = now()
        where src_msg_id = #{srcMsgId}
    </update>
</mapper>
