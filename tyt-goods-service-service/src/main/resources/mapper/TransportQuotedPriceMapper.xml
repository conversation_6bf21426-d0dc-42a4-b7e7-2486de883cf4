<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportQuotedPriceMapper">

    <update id="transportAgree">
        update tyt_transport_quoted_price set final_quoted_price_is_done = 1, car_is_done = 1, transport_is_done = 1
                                            , final_quoted_price = car_quoted_price, final_quoted_price_type = 1
                                            , transport_quoted_price_time = now() , price_assistant_auto_agree = #{priceAssistantAutoAgree}
                                          where id = #{transportQuotedPriceId}
    </update>
    <update id="transportQuotedPrice">
        update tyt_transport_quoted_price set transport_quoted_price = #{price}, car_is_done = 0, transport_is_done = 1
                                            , transport_quoted_price_time = now(), transport_quoted_price_times = transport_quoted_price_times + 1
        where id = #{transportQuotedPriceId}
    </update>

    <update id="subsequentCarQuotedPrice">
        update tyt_transport_quoted_price
        set car_user_name = #{carUserName},
            car_quoted_price = #{price},
            car_is_done = 1,
            transport_is_done = 0,
            car_quoted_price_time = now(),
            car_quoted_price_times = car_quoted_price_times + 1,
            <if test="reason != null">
                reason = #{reason}
            </if>
        where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
    </update>

    <update id="carAgree">
        update tyt_transport_quoted_price set final_quoted_price_is_done = 1, car_is_done = 1, transport_is_done = 1
                                            , final_quoted_price = transport_quoted_price, final_quoted_price_type = 2
                                            , car_quoted_price_time = now() where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
    </update>

    <select id="getQuotedPriceByCarUserIdAndTransportMainId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select *
        from tyt_transport_quoted_price
        where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
    </select>

    <select id="getQuotedPriceListByTransportMainId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select *
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} order by car_quoted_price_time desc;
    </select>

	<select id="getTransportQuotedPriceCountBySrcMsgId" resultType="java.lang.Integer">
        select count(1)
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} and car_quoted_price_time > current_date()
    </select>

    <select id="isFreightDepot" resultType="java.lang.Integer">
        select count(1)
        from tyt_user_identity_label where user_id = #{userId} and ((goods_type_first = 1 and goods_type_second = 2) or (goods_type_first = 2 and goods_type_second = 3));
    </select>

    <select id="getCarIsHaveQuotedPriceToTransport" resultType="java.lang.Integer">
        select count(1) from tyt_transport_quoted_price where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
    </select>

    <select id="getTransportHaveOptionQuotedPriceCount" resultType="java.lang.Integer">
        select count(1)
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} and (transport_quoted_price_times != 0 or (final_quoted_price_is_done = 1 and final_quoted_price_type = 1))
    </select>

    <insert id="firstCarQuotedPrice" useGeneratedKeys="true" keyProperty="id">
        insert into tyt_transport_quoted_price (car_id, car_user_name, transport_user_id, transport_user_name, src_msg_id, car_quoted_price, transport_quoted_price
                                               , car_is_done, transport_is_done, final_quoted_price_is_done, final_quoted_price, final_quoted_price_type
                                               , car_quoted_price_time, transport_quoted_price_time, car_quoted_price_times, transport_quoted_price_times, reason) VALUE (
            #{carUserId}, #{carUserName}, #{transportUserId}, #{transportUserName}, #{srcMsgId}, #{price}, null, 1, 0, 0, null, null, now(), null, 1, 0, #{reason})
    </insert>

    <insert id="firstCarQuotedPriceV2" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tyt_transport_quoted_price (
            car_id, car_user_name, transport_user_id, transport_user_name, src_msg_id,
            car_quoted_price, transport_quoted_price,
            car_is_done, transport_is_done, final_quoted_price_is_done,
            final_quoted_price, final_quoted_price_type,
            car_quoted_price_time, transport_quoted_price_time,
            car_quoted_price_times, transport_quoted_price_times, reason
        ) VALUES (
             #{carId}, #{carUserName}, #{transportUserId}, #{transportUserName}, #{srcMsgId},
             #{carQuotedPrice}, null,
             1, 0, 0,
             null, null,
             now(), null,
             1, 0, #{reason}
         )
    </insert>

    <select id="getQuotedPriceLastOneBySrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select *
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} limit 1
    </select>

    <select id="countSystemQuotedPrice" resultType="java.lang.Integer">
        select count(1)
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} and quoted_type = 1
    </select>

	<select id="getAllQuotedPriceListBySrcMsgIdList"
	        resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select *
        from tyt_transport_quoted_price
        where src_msg_id in
        <foreach close=")" collection="srcMsgIds" index="index" item="srcMsgId" open="(" separator=",">
            #{srcMsgId}
        </foreach>
        order by car_quoted_price_time desc
    </select>

    <select id="getCarQuotedPriceList"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select *
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} and transport_user_id = #{transportUserId}
          and final_quoted_price_is_done = 0 and car_is_done = 1 and transport_is_done = 0
    </select>

    <select id="getCarHaveNewTransportQuotedPriceLastTime" resultType="java.util.Date">
        select max(transport_quoted_price_time)
        from tyt_transport_quoted_price
        where car_id = #{carUserId} and final_quoted_price_is_done = 0 and car_quoted_price_times = transport_quoted_price_times
        and car_quoted_price_time > current_date()
        and src_msg_id in
        <foreach close=")" collection="srcMsgIdList" index="index" item="srcMsgId" open="(" separator=",">
            #{srcMsgId}
        </foreach>
    </select>

    <select id="getCarHaveAgreeTransportQuotedPrice" resultType="java.lang.Long">
        select src_msg_id
        from tyt_transport_quoted_price
        where car_id = #{carUserId} and final_quoted_price_is_done = 1 and final_quoted_price_type = 1
          and car_quoted_price_time > current_date()
    </select>

    <select id="getCarHaveAgreeTransportQuotedPriceLastTime" resultType="java.util.Date">
        select max(transport_quoted_price_time)
        from tyt_transport_quoted_price
        where car_id = #{carUserId} and final_quoted_price_is_done = 1 and final_quoted_price_type = 1
        and car_quoted_price_time > current_date()
        and src_msg_id in
        <foreach close=")" collection="srcMsgIdList" index="index" item="srcMsgId" open="(" separator=",">
            #{srcMsgId}
        </foreach>
    </select>

    <select id="getLatestQuotedRecord"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select *
        from tyt_transport_quoted_price
        where src_msg_id in
        <foreach collection="srcMsgIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc limit 1
    </select>

    <select id="getTransportHaveAnyQuotedPrice" resultType="java.lang.Long">
        select src_msg_id
        from tyt_transport_quoted_price
        where transport_user_id = #{transportUserId} and final_quoted_price_is_done = 0
          and car_quoted_price_time > current_date()
    </select>

    <select id="getCarHaveNewTransportQuotedPrice" resultType="java.lang.Long">
        select src_msg_id
        from tyt_transport_quoted_price
        where car_id = #{carUserId} and final_quoted_price_is_done = 0 and car_quoted_price_times = transport_quoted_price_times
          and car_quoted_price_time > current_date()
    </select>

    <select id="getQuotedPriceRecord"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO">
        select id,
               car_quoted_price as carQuotedPrice
        from tyt_transport_quoted_price
        where src_msg_id = #{srcMsgId} and car_id != -1
        order by id desc
            limit 1
    </select>

</mapper>
